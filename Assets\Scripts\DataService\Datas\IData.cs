
using System;
using System.IO;
using Newtonsoft.Json;
using UnityEngine;

/// <summary>
/// 数据类型基类 
/// </summary>
[Serializable]
public class DataBase
{
    private string _version = "0.0.1";

    /// <summary>
    /// 是否被修改
    /// </summary>
    public bool IsChanged = false;

    private int _sessionId = -1;

    private string _loadDataPath;

    protected DataBase()
    {

    }

    /// <summary>
    /// 初始化创建时调用1次
    /// </summary>
    public virtual void Init()
    {

    }

    /// <summary>
    /// 每次进入游戏第一次加载数据时调用
    /// </summary>
    public virtual void OnLoad()
    {

    }


    public void Save()
    {
        //TODO 这里待优化 - 如果存了data的引用不经过getData访问,则不会修改isChanged 最好加个装饰器
        // if (!IsChanged)
        // {
        //     //如果没被修改就什么都不干
        //     return;
        // }
        if (_loadDataPath == null)
        {
            return;
        }
        string jsonData = JsonConvert.SerializeObject(this);
        if (!File.Exists(_loadDataPath))
        {
            Directory.CreateDirectory(_loadDataPath);
        }
        Type type = GetType();
        if (typeof(ISubData).IsAssignableFrom(type))
        {
            File.WriteAllText(_loadDataPath + $"{type}_{_sessionId}.json", jsonData);
        }
        else
        {
            File.WriteAllText(_loadDataPath + $"{type}.json", jsonData);
        }
        // Debug.Log($"SaveData{GetType()} path:{_loadDataPath}");
        IsChanged = false;
    }
}

/// <summary>
/// 全局数据 需继承此方法
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IGlobeData
{

}

/// <summary>
/// 局内数据 需继承此方法
/// </summary>
public interface ISubData
{

}

