
using System.Collections.Generic;
using UnityEngine;

namespace AttributeSystem
{
    /// <summary>
    /// 属性计算类
    /// </summary>
    public class AttributeCalculation
    {
        /// <summary>属性类型</summary>
        private AttrType _attrType;

        /// <summary>基础值</summary>
        private float _baseValue;

        /// <summary>基础值</summary>
        public float BaseValue => _baseValue;

        /// <summary>缩放系数</summary>
        public float ScaleValue { get; set; }

        /// <summary>增量值</summary>
        public float FixValue { get; set; }

        /// <summary>最终值</summary>
        private float _finalValue;
        public float FinalValue => _finalValue;

        /// <summary>修改器</summary>
        private List<AttributeModifer> _modifiers;

        public AttributeCalculation(AttrType charAttrType)
        {
            _attrType = charAttrType;
            _baseValue = _finalValue = FixValue = 0f;
            ScaleValue = 1f;
            _modifiers = new List<AttributeModifer>();
        }

        /// <summary>设置基础值</summary>
        public void SetBaseValue(float value)
        {
            _baseValue = value;
            UpdateValue();
        }

        /// <summary>
        /// 清除所有修改器,并将属性还原为默认值
        /// </summary>
        public void Reset()
        {
            ClearModifier();
            ResetValue();
        }

        /// <summary>
        /// 重置为默认属性
        /// </summary>
        private void ResetValue()
        {
            FixValue = 0f;
            ScaleValue = 1f;
            _finalValue = _baseValue;
        }

        public void ClearModifier()
        {
            _modifiers.Clear();
            _modifiers = null;
        }

        /// <summary>
        /// 添加修改器
        /// </summary>
        /// <param name="modifier">修改器</param>
        public void AddModifier(AttributeModifer modifier)
        {
            //TODO 这里应该接入相同modifier的叠加规则 是否应该有栈

            if (!string.IsNullOrEmpty(modifier.Reason) && CheckSame(modifier))
            {
                UpdateValue();
                return;
            }

            //添加规则:相同优先级的叠加计算,若优先级中有覆盖的,则覆盖的优先，否则按照加减乘除进行计算。

            if (modifier.Priority >= 0)
            {
                int idx = -1;
                for (int i = 0; i < _modifiers.Count; i++)
                {
                    var mod = _modifiers[i];
                    if (modifier.Priority > mod.Priority)
                    {
                        idx = i;
                        break;
                    }

                    if (modifier.Priority == mod.Priority && modifier.OperationType == OperationType.Override)
                    {
                        idx = i;
                        break;
                    }

                    if (modifier.Priority == mod.Priority)
                    {
                        idx = i;
                        break;
                    }
                }

                if (idx == -1)
                {
                    _modifiers.Add(modifier);
                }
                else
                {
                    _modifiers.Insert(idx, modifier);
                }
            }
            else
            {
                _modifiers.Add(modifier);
            }

            UpdateValue();
        }

        private bool CheckSame(AttributeModifer modifier)
        {
            bool isSame = false;
            foreach (var mod in _modifiers)
            {
                if (!string.IsNullOrEmpty(mod.Reason) && mod.Reason == modifier.Reason)
                {
                    mod.AddStack(modifier);
                    isSame = true;
                }
            }
            return isSame;
        }

        /// <summary>更新属性值</summary>
        private void UpdateValue()
        {
            ResetValue();
            int length = _modifiers.Count;
            if (length > 0)
            {
                int priority = _modifiers[0].Priority;
                for (int i = 0; i < length; i++)
                {
                    var modifier = _modifiers[i];
                    if (_modifiers[i].Priority == priority)
                    {
                        if (modifier.OperationType == OperationType.Override)
                        {
                            _finalValue = modifier.Value;
                            return;
                        }
                        else
                        {
                            modifier.OnModify(this);
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                _finalValue = (BaseValue + FixValue) * ScaleValue;
            }
        }

        /// <summary>
        /// 通过来源删除修改器
        /// </summary>
        /// <param name="reason">来源</param>
        public void RemoveModifier(string reason)
        {
            for (int i = 0; i < _modifiers.Count; i++)
            {
                var modifier = _modifiers[i];
                if (modifier.Reason == reason)
                {
                    RemoveModifierInternal(modifier);
                    UpdateValue();
                    break;
                }
            }
        }

        private void RemoveModifierInternal(AttributeModifer modifier)
        {
            int idx = _modifiers.IndexOf(modifier);
            if (idx == -1)
            {
                Debug.Log($"_________________deleteAttrModifier_{modifier.AttrType} " +
                         $"from Attribute_{_attrType} fail");
                return;
            }
            _modifiers.RemoveAt(idx);
        }

        public bool OnUpdate(float deltaTime)
        {
            bool isModChange = false;
            var modifiersToRemove = new List<AttributeModifer>();

            foreach (var mod in _modifiers)
            {
                if (mod.OnUpdate(deltaTime))
                {
                    modifiersToRemove.Add(mod);
                    isModChange = true;
                }
            }

            foreach (var mod in modifiersToRemove)
            {
                RemoveModifierInternal(mod);
            }

            if (isModChange)
            {
                UpdateValue();
            }
            return isModChange;
        }

        public void Destroy()
        {
            ClearModifier();
        }
    }
}