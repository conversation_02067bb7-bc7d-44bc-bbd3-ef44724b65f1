using UnityEngine;
using GameUtils;
using Common;

namespace Test
{
    /// <summary>
    /// 按键配置系统测试脚本
    /// </summary>
    public class KeyBindingTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private GameObject keyBindingUI;
        
        private void Start()
        {
            // 注册事件监听
            RegisterEventListeners();
        }

        private void OnDestroy()
        {
            // 取消事件监听
            UnregisterEventListeners();
        }

        /// <summary>
        /// 注册事件监听
        /// </summary>
        private void RegisterEventListeners()
        {
            // 监听输入动作事件
            EventDispatcher.GameEvent.Regist<InputActionType>(EventName.InputActionTriggered, OnInputActionTriggered);
            
            // 监听按键绑定改变事件
            EventDispatcher.GameEvent.Regist<InputActionType, KeyCode>(EventName.KeyBindingChanged, OnKeyBindingChanged);
        }

        /// <summary>
        /// 取消事件监听
        /// </summary>
        private void UnregisterEventListeners()
        {
            EventDispatcher.GameEvent.UnRegist<InputActionType>(EventName.InputActionTriggered, OnInputActionTriggered);
            EventDispatcher.GameEvent.UnRegist<InputActionType, KeyCode>(EventName.KeyBindingChanged, OnKeyBindingChanged);
        }

        /// <summary>
        /// 处理输入动作触发事件
        /// </summary>
        private void OnInputActionTriggered(InputActionType actionType)
        {
            Debug.Log($"动作触发: {actionType}");

            // 根据不同的动作类型执行相应的逻辑
            switch (actionType)
            {
                case InputActionType.Escape:
                    HandleEscapeAction();
                    break;
                    
                case InputActionType.Menu:
                    HandleMenuAction();
                    break;
                    
                case InputActionType.Inventory:
                    HandleInventoryAction();
                    break;
                    
                case InputActionType.Skill1:
                    HandleSkill1Action();
                    break;
                    
                case InputActionType.Skill2:
                    HandleSkill2Action();
                    break;
                    
                case InputActionType.Skill3:
                    HandleSkill3Action();
                    break;
                    
                case InputActionType.Pause:
                    HandlePauseAction();
                    break;
                    
                case InputActionType.QuickSave:
                    HandleQuickSaveAction();
                    break;
                    
                case InputActionType.QuickLoad:
                    HandleQuickLoadAction();
                    break;
                    
                case InputActionType.Screenshot:
                    HandleScreenshotAction();
                    break;
                    
                case InputActionType.Debug:
                    HandleDebugAction();
                    break;
            }
        }

        /// <summary>
        /// 处理按键绑定改变事件
        /// </summary>
        private void OnKeyBindingChanged(InputActionType actionType, KeyCode keyCode)
        {
            Debug.Log($"按键绑定已改变: {actionType} -> {keyCode}");
        }

        // 各种动作的处理方法
        private void HandleEscapeAction()
        {
            Debug.Log("执行退出/取消操作");
            // 这里可以添加退出菜单、取消操作等逻辑
        }

        private void HandleMenuAction()
        {
            Debug.Log("打开/关闭菜单");
            // 切换按键配置UI的显示状态
            if (keyBindingUI != null)
            {
                keyBindingUI.SetActive(!keyBindingUI.activeSelf);
            }
        }

        private void HandleInventoryAction()
        {
            Debug.Log("打开/关闭背包");
            // 这里可以添加背包界面的逻辑
        }

        private void HandleSkill1Action()
        {
            Debug.Log("使用技能1");
            // 这里可以添加技能1的逻辑
        }

        private void HandleSkill2Action()
        {
            Debug.Log("使用技能2");
            // 这里可以添加技能2的逻辑
        }

        private void HandleSkill3Action()
        {
            Debug.Log("使用技能3");
            // 这里可以添加技能3的逻辑
        }

        private void HandlePauseAction()
        {
            Debug.Log("暂停/继续游戏");
            // 这里可以添加暂停游戏的逻辑
        }

        private void HandleQuickSaveAction()
        {
            Debug.Log("快速保存游戏");
            // 这里可以添加快速保存的逻辑
        }

        private void HandleQuickLoadAction()
        {
            Debug.Log("快速加载游戏");
            // 这里可以添加快速加载的逻辑
        }

        private void HandleScreenshotAction()
        {
            Debug.Log("截取游戏画面");
            // 这里可以添加截图的逻辑
            TakeScreenshot();
        }

        private void HandleDebugAction()
        {
            Debug.Log("打开调试界面");
            // 这里可以添加调试界面的逻辑
        }

        /// <summary>
        /// 截图功能示例
        /// </summary>
        private void TakeScreenshot()
        {
            string fileName = $"Screenshot_{System.DateTime.Now:yyyyMMdd_HHmmss}.png";
            string path = System.IO.Path.Combine(Application.persistentDataPath, fileName);
            ScreenCapture.CaptureScreenshot(path);
            Debug.Log($"截图已保存到: {path}");
        }

        /// <summary>
        /// 测试按键配置系统的方法
        /// </summary>
        [ContextMenu("测试按键配置")]
        private void TestKeyBinding()
        {
            var keyBindingManager = KeyBindingManager.Instance;
            if (keyBindingManager != null)
            {
                // 获取当前的按键配置
                var escapeBinding = keyBindingManager.GetKeyBinding(InputActionType.Escape);
                Debug.Log($"退出键配置: 主要={escapeBinding?.primaryKey}, 次要={escapeBinding?.secondaryKey}");

                // 测试设置新的按键绑定
                bool success = keyBindingManager.SetKeyBinding(InputActionType.Skill1, KeyCode.Q);
                Debug.Log($"设置技能1为Q键: {(success ? "成功" : "失败")}");
            }
        }
    }
}
