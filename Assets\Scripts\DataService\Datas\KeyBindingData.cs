using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Newtonsoft.Json;
using GameUtils;
using DataCenter;

namespace DataCenter
{
    /// <summary>
    /// 按键绑定配置数据 - 全局数据，会持久化保存
    /// </summary>
    [Serializable]
    public class KeyBindingData : DataBase, IGlobeData
    {
        [JsonProperty]
        private Dictionary<InputActionType, InputActionData> _keyBindings = new();

        /// <summary>
        /// 获取所有按键绑定
        /// </summary>
        public Dictionary<InputActionType, InputActionData> KeyBindings => _keyBindings;

        public override void Init()
        {
            // 初始化默认按键配置
            InitializeDefaultKeyBindings();
        }

        /// <summary>
        /// 初始化默认按键绑定
        /// </summary>
        private void InitializeDefaultKeyBindings()
        {
            _keyBindings.Clear();

            // 添加默认按键配置
            AddKeyBinding(InputActionType.Escape, KeyCode.Escape, KeyCode.None, "退出/取消", "退出当前界面或取消操作");
            AddKeyBinding(InputActionType.Confirm, KeyCode.Return, KeyCode.Space, "确认", "确认当前操作");
            AddKeyBinding(InputActionType.Menu, KeyCode.Tab, KeyCode.None, "菜单", "打开/关闭主菜单");
            AddKeyBinding(InputActionType.Inventory, KeyCode.I, KeyCode.None, "背包", "打开/关闭背包界面");
            AddKeyBinding(InputActionType.Skill1, KeyCode.Alpha1, KeyCode.None, "技能1", "使用技能1");
            AddKeyBinding(InputActionType.Skill2, KeyCode.Alpha2, KeyCode.None, "技能2", "使用技能2");
            AddKeyBinding(InputActionType.Skill3, KeyCode.Alpha3, KeyCode.None, "技能3", "使用技能3");
            AddKeyBinding(InputActionType.Pause, KeyCode.P, KeyCode.None, "暂停", "暂停/继续游戏");
            AddKeyBinding(InputActionType.QuickSave, KeyCode.F5, KeyCode.None, "快速保存", "快速保存游戏");
            AddKeyBinding(InputActionType.QuickLoad, KeyCode.F9, KeyCode.None, "快速加载", "快速加载游戏");
            AddKeyBinding(InputActionType.Screenshot, KeyCode.F12, KeyCode.None, "截图", "截取游戏画面");
            AddKeyBinding(InputActionType.Debug, KeyCode.F1, KeyCode.None, "调试", "打开调试界面", false);

            IsChanged = true;
        }

        /// <summary>
        /// 添加按键绑定
        /// </summary>
        private void AddKeyBinding(InputActionType actionType, KeyCode primaryKey, KeyCode secondaryKey, 
                                  string displayName, string description, bool canBeRebound = true)
        {
            _keyBindings[actionType] = new InputActionData(actionType, primaryKey, secondaryKey, 
                                                          displayName, description, canBeRebound);
        }

        /// <summary>
        /// 获取指定动作的按键绑定
        /// </summary>
        public InputActionData GetKeyBinding(InputActionType actionType)
        {
            return _keyBindings.TryGetValue(actionType, out var binding) ? binding : null;
        }

        /// <summary>
        /// 设置按键绑定
        /// </summary>
        public bool SetKeyBinding(InputActionType actionType, KeyCode newKey, bool isPrimary = true)
        {
            if (!_keyBindings.TryGetValue(actionType, out var binding))
                return false;

            if (!binding.canBeRebound)
                return false;

            // 检查是否与其他动作冲突
            if (IsKeyConflict(newKey, actionType))
                return false;

            if (isPrimary)
                binding.primaryKey = newKey;
            else
                binding.secondaryKey = newKey;

            IsChanged = true;
            return true;
        }

        /// <summary>
        /// 检查按键是否与其他动作冲突
        /// </summary>
        public bool IsKeyConflict(KeyCode keyCode, InputActionType excludeAction = InputActionType.None)
        {
            if (keyCode == KeyCode.None)
                return false;

            return _keyBindings.Values.Any(binding => 
                binding.actionType != excludeAction && 
                (binding.primaryKey == keyCode || binding.secondaryKey == keyCode));
        }

        /// <summary>
        /// 根据按键获取对应的动作类型
        /// </summary>
        public InputActionType GetActionByKey(KeyCode keyCode)
        {
            var binding = _keyBindings.Values.FirstOrDefault(b => b.IsKeyMatch(keyCode));
            return binding?.actionType ?? InputActionType.None;
        }

        /// <summary>
        /// 重置为默认按键配置
        /// </summary>
        public void ResetToDefault()
        {
            InitializeDefaultKeyBindings();
        }

        /// <summary>
        /// 获取所有可重新绑定的动作
        /// </summary>
        public List<InputActionData> GetRebindableActions()
        {
            return _keyBindings.Values.Where(binding => binding.canBeRebound).ToList();
        }

        /// <summary>
        /// 清除指定动作的次要按键
        /// </summary>
        public void ClearSecondaryKey(InputActionType actionType)
        {
            if (_keyBindings.TryGetValue(actionType, out var binding) && binding.canBeRebound)
            {
                binding.secondaryKey = KeyCode.None;
                IsChanged = true;
            }
        }
    }
}
