//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;

public partial class OptionTranslation : ScriptableObject {

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private OptionTranslationData[] _OptionTranslationDataItems;

	public OptionTranslationData GetOptionTranslationData(string key) {
		int min = 0;
		int max = _OptionTranslationDataItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			OptionTranslationData item = _OptionTranslationDataItems[index];
			if (item.key == key) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(key, item.key) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		OptionTranslationData GetOptionTranslationData(string key);
	}

	private class DataGetter : IDataGetter {
		private Func<string, OptionTranslationData> _GetOptionTranslationData;
		public OptionTranslationData GetOptionTranslationData(string key) {
			return _GetOptionTranslationData(key);
		}
		public DataGetter(Func<string, OptionTranslationData> getOptionTranslationData) {
			_GetOptionTranslationData = getOptionTranslationData;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(GetOptionTranslationData);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class OptionTranslationData {

	[SerializeField]
	private string _Key;
	public string key { get { return _Key; } }

	[SerializeField]
	private string _Chinese;
	public string Chinese { get { return _Chinese; } }

	[SerializeField]
	private string _English;
	public string English { get { return _English; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private OptionTranslation.IDataGetter mGetter;

	public OptionTranslationData Init(int version, OptionTranslation.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[OptionTranslationData]{{key:{0}, Chinese:{1}, English:{2}}}",
			key, Chinese, English);
	}

}

