using System;
using System.Collections;
using System.Collections.Generic;
using Common;
using KoganeUnityLib;
using Player;
using Spine;
using Spine.Unity;
using StoryCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Event = Spine.Event;
using Object = UnityEngine.Object;

namespace Story
{
    public class StoryView : MonoBehaviour
    {
        private TMP_Typewriter _tmpTypewriter;
        [SerializeField]
        private GameObject _playerDialogGo;
        [SerializeField]
        private GameObject _npcDialogGo;
        [SerializeField]
        private GameObject _npcGO;
        [SerializeField]
        private GameObject _playerGO;
        [SerializeField]
        private GameObject _nextTipsGO;
        [SerializeField]
        private GameObject _autoTipsGO;
        [SerializeField]
        private TMP_Text _npcNameTxt;

        [SerializeField]
        private GameObject _npcNameGo;
        [SerializeField]
        private TMP_Text _playerNameTxt;
        [SerializeField]
        private GameObject _playerNameGo;
        [SerializeField]
        private Button _pnl_btn;

        [SerializeField] private Button _expandBtn;
        [SerializeField] private Button _closeBtn;
        [SerializeField] private Button _speedBtn;
        [SerializeField] private Button _reviewBtn;
        [SerializeField] private Button _autoPlyBtn;
        [SerializeField] private Button _skipBtn;
        [SerializeField] private Button _manualBtn;
        [SerializeField] private SkeletonGraphic _npcSpineGraphic;

        private float _typewriterSpeed = 10;

        private bool _isExpand = false;
        private bool _isAutoPlay = false;
        public List<Sprite> autoPlaySprites;
        private int _curSpeed = 1;
        public List<Sprite> speedSprites;

        private Action _onDialogFinish;
        private void Awake()
        {
            _pnl_btn.onClick.AddListener(OnClickPnlTouch);
            _expandBtn.onClick.AddListener(() =>
            {
                SetExpand(true);
            });

            _closeBtn.onClick.AddListener(() =>
            {
                SetExpand(false);
            });

            _speedBtn.onClick.AddListener(OnClickSpeedUp);
            _autoPlyBtn.onClick.AddListener(OnClickAutoPlay);
            _reviewBtn.onClick.AddListener(OnClickReview);
            _skipBtn.onClick.AddListener(OnClickSkip);
        }

        private void OnClickSkip()
        {
            var storyGraph = StoryManager.Instance.storyGraph;
            storyGraph.SkipStory();
        }

        private void OnClickReview()
        {
            StoryManager.Instance.SetStoryReviewVisible(true);
        }

        private void OnClickAutoPlay()
        {
            _isAutoPlay = !_isAutoPlay;
            _autoTipsGO.SetActive(_isAutoPlay);
            int index = _isAutoPlay ? 0 : 1;
            _autoPlyBtn.transform.Find("img_icon").GetComponent<Image>().sprite = autoPlaySprites[index];
        }

        private void OnClickSpeedUp()
        {
            _curSpeed = (_curSpeed % 3) + 1;
            _speedBtn.transform.Find("img_icon").GetComponent<Image>().sprite = speedSprites[_curSpeed - 1];
        }

        private void Start()
        {
            SetExpand(false);
        }


        private void SetExpand(bool flag)
        {
            _isExpand = flag;
            _closeBtn.gameObject.SetActive(flag);
            _expandBtn.gameObject.SetActive(!flag);
            _speedBtn.gameObject.SetActive(flag);
            _reviewBtn.gameObject.SetActive(flag);
            _autoPlyBtn.gameObject.SetActive(flag);
            _skipBtn.gameObject.SetActive(flag);
            _manualBtn.gameObject.SetActive(flag);
        }

        public void ShowDialog(DialogInfo dialogInfo, Action onDialogFinish)
        {
            _nextTipsGO.SetActive(false);
            _onDialogFinish = onDialogFinish;
            bool isPlayer = dialogInfo.IsPlayer;
            SetPlayerDialogVisible(isPlayer);
            SetNpcDialogVisible(!isPlayer);
            if (isPlayer)
            {
                _tmpTypewriter = _playerDialogGo.GetComponentInChildren<TMP_Typewriter>(true);
                _playerNameTxt.text = PlayerEntity.Instance.UserName;
            }
            else
            {
                _tmpTypewriter = _npcDialogGo.GetComponentInChildren<TMP_Typewriter>(true);
                ExcelDataMgr.Instance.SetTranslationFunc<NpcDatas, NpcData>("name");
                var npcDatas = ExcelDataMgr.Instance.GetTable<NpcDatas>();
                var npcData = npcDatas.GetNpcData(dialogInfo.characterID);
                _npcNameTxt.text = npcData.name;

                var spinePath = string.Format(ResourcePathConst.NpcStorySpineDataPath, dialogInfo.characterID);
                var spineData = ResourceMgr.Instance.LoadResource<SkeletonDataAsset>(spinePath);
                if (spineData == null)
                {
                    spinePath = string.Format(ResourcePathConst.NpcStorySpineDataPath, 1);
                    Debug.LogWarning($"{dialogInfo.characterID} has no spine res, tmp use character 1");
                    spineData = ResourceMgr.Instance.LoadResource<SkeletonDataAsset>(spinePath);
                }
                _npcSpineGraphic.skeletonDataAsset = spineData;
                _npcSpineGraphic.Initialize(true);

                var spineAnim = dialogInfo.animName;
                var animLoop = dialogInfo.animLoop;
                if (string.IsNullOrEmpty(spineAnim))
                {
                    _npcSpineGraphic.AnimationState.SetAnimation(0, Const.DefaultSpineAnimName, true);
                }
                else
                {
                    if (animLoop)
                        _npcSpineGraphic.AnimationState.SetAnimation(0, spineAnim, true);
                    else
                    {
                        var trackEntry = _npcSpineGraphic.AnimationState.SetAnimation(0, spineAnim, false);
                        trackEntry.Complete += entry =>
                        {
                            _npcSpineGraphic.AnimationState.SetAnimation(0, Const.DefaultSpineAnimName, true);
                        };
                    }
                }
            }

            var conversionContent = StoryUtils.ConversionPlaceHolder(dialogInfo.content);
            _tmpTypewriter.Play(conversionContent, _typewriterSpeed * _curSpeed, OnTypeWriterFinish);
        }

        public void SkipToComplete()
        {
            _tmpTypewriter.Skip(false);
            _nextTipsGO.SetActive(true);
        }

        private void SetPlayerDialogVisible(bool flag)
        {
            _playerGO.SetActive(flag);
            _playerNameGo.SetActive(flag);
            _playerDialogGo.SetActive(flag);
        }

        private void SetNpcDialogVisible(bool flag)
        {
            _npcGO.SetActive(flag);
            _npcNameGo.SetActive(flag);
            _npcDialogGo.SetActive(flag);
        }

        private void OnTypeWriterFinish()
        {
            _nextTipsGO.SetActive(true);
            if (_isAutoPlay)
                OnClickPnlTouch();
        }

        private void OnClickPnlTouch()
        {
            if (_tmpTypewriter.IsPlaying())
            {
                _tmpTypewriter.Skip(true);
            }
            else
            {
                var tmp = _onDialogFinish;
                _onDialogFinish = null;
                tmp?.Invoke();
            }
        }
    }
}