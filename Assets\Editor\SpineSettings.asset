%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b29e98153ec2fbd44b8f7da1b41194e8, type: 3}
  m_Name: SpineSettings
  m_EditorClassIdentifier: 
  defaultScale: 0.01
  defaultMix: 0.2
  defaultShader: Spine/Skeleton
  defaultZSpacing: 0
  defaultInstantiateLoop: 1
  defaultPhysicsPositionInheritance: {x: 1, y: 1}
  defaultPhysicsRotationInheritance: 1
  showHierarchyIcons: 1
  reloadAfterPlayMode: 1
  setTextureImporterSettings: 1
  textureSettingsReference: Assets/Spine/Editor/spine-unity/Editor/ImporterPresets/StraightAlphaPreset.preset
  fixPrefabOverrideViaMeshFilter: 0
  removePrefabPreviewMeshes: 0
  blendModeMaterialMultiply: {fileID: 2100000, guid: 053fed1e18e65064ca564f05c4027e1e, type: 2}
  blendModeMaterialScreen: {fileID: 2100000, guid: 1a66c2d7ee7642a459bd3831abeb30af, type: 2}
  blendModeMaterialAdditive: {fileID: 2100000, guid: 517c2df5a5dc5d44a8b3820eca503d13, type: 2}
  atlasTxtImportWarning: 1
  textureImporterWarning: 1
  componentMaterialWarning: 1
  skeletonDataAssetNoFileError: 1
  autoReloadSceneSkeletons: 1
  handleScale: 1
  mecanimEventIncludeFolderName: 1
  timelineDefaultMixDuration: 0
  timelineUseBlendDuration: 1
