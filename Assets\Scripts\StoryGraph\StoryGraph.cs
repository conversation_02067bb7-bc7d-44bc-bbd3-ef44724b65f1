using System.Collections;
using System.Collections.Generic;
using System.Net.Mail;
using UnityEngine;
using XNode;
namespace StoryEditor
{
    [CreateAssetMenu(fileName = "StoryGraph")]
    public class StoryGraph : NodeGraph
    {
        private BaseNode _curDialogNode=null;

        public BaseNode curDialogNode
        {
            set => _curDialogNode = value;
            get => _curDialogNode;
        } 
        public void Start()
        {
            var allStarNode = nodes.FindAll((node => node.GetType() == typeof(StartNode)));
            foreach (var node in allStarNode)
            {
                var startNode = node as StartNode;
                if (startNode != null) startNode.Start();
            }
        }

        public void FinishNode(BaseNode node, string outFieldName)
        {
            var outputPort = node.GetOutputPort(outFieldName).Connection;
            node.Finish();
            if (outputPort == null)
            {
                return;
            }

            var outputNode = outputPort.node as BaseNode;
            if (outputNode != null) outputNode.Start();
        }

        public void SkipStory()
        {
            if (curDialogNode == null)
                return;
            var outputPort = curDialogNode.GetOutputPort("output").Connection;
            curDialogNode.Finish();
            BaseNode finishNode = null;
            while (outputPort != null)
            {
                var outNode = outputPort.node;
                if (outNode as DialogNode != null)
                {
                    var dialogNode = (DialogNode)outNode;
                    dialogNode.Start();
                    dialogNode.Finish();
                    outputPort = outNode.GetOutputPort("output").Connection;
                }
                else if (outNode as ExcelDialogNode != null)
                {
                    var dialogNode = (ExcelDialogNode)outNode;
                    dialogNode.Start();
                    dialogNode.Finish();
                    outputPort = outNode.GetOutputPort("output").Connection;
                }
                else if (outNode as FinishNode != null)
                {
                    finishNode = (FinishNode)outNode;
                    break;
                }
                else
                {
                    var branchNode = outputPort.node as BranchNode;
                    if (branchNode != null)
                    {
                        branchNode.Start();
                        return;
                    }
                }
            }
            if(finishNode!=null) finishNode.Start();
            
        }

        public void FinishDynamicNode(BaseNode node, int dynamicIndex)
        {
            var dynamicPorts = node.DynamicOutputs;
            foreach (NodePort dynamicPort in dynamicPorts)
            {
                if (dynamicPort.fieldName == "options" +" "+ dynamicIndex.ToString())
                {
                    var outputPort = dynamicPort.Connection;
                    node.Finish();
                    if (outputPort == null)
                    {
                        return;
                    }

                    var outputNode = outputPort.node as BaseNode;
                    if (outputNode != null) outputNode.Start();
                    break;
                }
            }
        }
    }
}



