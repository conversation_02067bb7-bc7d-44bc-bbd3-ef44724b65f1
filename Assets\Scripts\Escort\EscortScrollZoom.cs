using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class EscortScrollZoom : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler
{
    public RectTransform content;
    public RectTransform mapItemContainerTransform;
    public RectTransform mapRoadContainerTransform;
    public float scaleSpeed = 0.1f; // 缩放速度
    public float minScale = 0.5f; // 最小缩放值
    public float maxScale = 3f; // 最大缩放值

    private float scrollDuration = 1;

    private bool isPointerInside = false;
    private float targetScaleValue = 2f;
    private float curScaleValue = 1f;
    private Vector2 originContentSize;

    private Action scrollToItemCallback;


    private RectTransform itemRectTransform;

    private void Awake()
    {
        content = GetComponent<ScrollRect>().content;
        originContentSize = content.sizeDelta;
    }

    public void ScrollToItem(RectTransform itemRect, Action scrollAction = null)
    {
        itemRectTransform = itemRect;
        scrollToItemCallback = scrollAction;
        StartCoroutine(ScrollToCenterAndScale());
    }

    private void Update()
    {
        // 监听鼠标滚轮事件
        if (isPointerInside)
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0)
            {
                Vector2 originContentPosition = content.anchoredPosition / curScaleValue;
                // 获取鼠标在内容中的位置
                Vector2 mouseScreenPosition = Input.mousePosition; // 获取屏幕坐标
                Vector2 mouseLocalPosition;

                // 将屏幕坐标转为内容的局部坐标
                RectTransformUtility.ScreenPointToLocalPointInRectangle(content, mouseScreenPosition, null,
                    out mouseLocalPosition);

                // 获取当前缩放
                float currentScale = curScaleValue;

                // 计算新的缩放值
                float newScale = currentScale + scroll * scaleSpeed;
                newScale = Mathf.Clamp(newScale, minScale, maxScale);

                // 计算缩放前的内容位置
                Vector2 scaledMousePosition = mouseLocalPosition / currentScale; // 当前缩放下的鼠标位置
                Vector2 newContentPosition = (scaledMousePosition * newScale) - mouseLocalPosition; // 计算缩放后内容的新位置

                // 应用缩放
                SetContentScale(newScale);

                // // 更新内容的位置
                content.anchoredPosition = originContentPosition * curScaleValue;
            }
        }
    }

    private IEnumerator ScrollToCenterAndScale()
    {
        // 获取ScrollRect的大小和当前物体的位置
        RectTransform parentRectTransform = GetComponent<RectTransform>();

        Vector2 localPosition = itemRectTransform.anchoredPosition;

        // 计算目标滚动位置
        Vector2 targetScrollPosition = new Vector2(localPosition.x, localPosition.y);
        targetScrollPosition.x =
            -(Mathf.Abs(targetScrollPosition.x) * targetScaleValue - parentRectTransform.rect.width / 2); // 移动到物体中心
        targetScrollPosition.y =
            Mathf.Abs(targetScrollPosition.y) * targetScaleValue - parentRectTransform.rect.height / 2; // 移动到物体中心

        // 滚动到目标位置
        Vector2 originalScrollPosition = content.anchoredPosition;
        float elapsedTime = 0;
        float originScale = curScaleValue;

        while (elapsedTime < scrollDuration)
        {
            float scaleValue = Mathf.Lerp(originScale, targetScaleValue, elapsedTime / scrollDuration);
            SetContentScale(scaleValue);

            content.anchoredPosition =
                Vector2.Lerp(originalScrollPosition, targetScrollPosition, elapsedTime / scrollDuration);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        content.anchoredPosition = targetScrollPosition; // 确保滚动到最终位置
        SetContentScale(targetScaleValue);
        scrollToItemCallback?.Invoke();
    }

    private void SetContentScale(float scaleValue)
    {
        curScaleValue = scaleValue;
        content.sizeDelta = new Vector2(originContentSize.x * scaleValue, originContentSize.y * scaleValue);
        // mapItemContainerTransform.localScale = new Vector3(scaleValue, scaleValue, scaleValue);
        // mapRoadContainerTransform.localScale = new Vector3(scaleValue, scaleValue, scaleValue);
        Enumerable.Range(0, content.childCount).Select(index => content.GetChild(index)).ToList().ForEach(
            childTransform => childTransform.localScale = new Vector3(scaleValue, scaleValue, scaleValue));
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        isPointerInside = true;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        isPointerInside = false;
    }
}