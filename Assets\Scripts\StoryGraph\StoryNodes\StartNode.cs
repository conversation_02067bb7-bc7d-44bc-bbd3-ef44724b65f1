﻿using XNode;

namespace StoryEditor
{
    public class StartNode : BaseNode {
        [Output] public Empty output;
        public override object GetValue(NodePort port)
        {
            return null;
        }

        protected override void OnStart()
        {
            base.OnStart();
            var storyGraph = graph as StoryGraph;
            if (storyGraph != null) storyGraph.FinishNode(this, "output");
        }
        
    }
}