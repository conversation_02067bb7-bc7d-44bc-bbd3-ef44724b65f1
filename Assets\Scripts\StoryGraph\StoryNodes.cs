using System;
using System.Collections;
using System.Collections.Generic;
using Story;
using StoryCommon;
using UnityEngine;
using XNode;

namespace StoryEditor
{
    [Serializable]
    public class Empty
    {
        
    }

    public abstract class BaseNode : Node
    {
        public void Start()
        {
            OnStart();
        }
        
        protected virtual void OnStart()
        {
            
        }

        public void Finish()
        {
            OnFinish();
        }

        public override object GetValue(NodePort port)
        {
            return null;
        }

        protected virtual void OnFinish()
        {
            
        }
    }

}
