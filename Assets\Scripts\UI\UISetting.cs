﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace UI
{
    public class UISettingObj
    {
        public Type ViewType;
        
    }
    public static class UISetting
    {
        public static Dictionary<string, UISettingObj> SettingObjDict = new Dictionary<string, UISettingObj>();

        public static void Init()
        {
            SettingObjDict.Add("MainSceneHUD", new UISettingObj{ViewType = typeof(MainSceneHUDPresenter)});
        }

        public static UISettingObj GetViewSetting(string viewName)
        {
            UISettingObj settingObj;
            if (SettingObjDict.TryGetValue(viewName, out settingObj))
            {
                return settingObj;
            }

            return null;
        }
    }
}