using System.Collections.Generic;
using UnityEngine;
using UnityCommunity.UnitySingleton;
using Common;

namespace GameUtils
{
    /// <summary>
    /// 输入管理器
    /// 1.Input类
    /// 2.事件中心模块
    /// 3.公共Mono模块的使用
    /// 4.支持可配置按键绑定
    /// </summary>
    public class InputManager : PersistentMonoSingleton<InputManager>
    {
        private bool _isLock;
        private Dictionary<KeyCode, int> _lockDic;
        private Dictionary<InputActionType, int> _actionLockDic;
        private KeyBindingManager _keyBindingManager;

        protected override void OnInitialized()
        {
            _lockDic = new();
            _actionLockDic = new();
            _isLock = false;

            // 获取按键绑定管理器
            _keyBindingManager = KeyBindingManager.Instance;
        }

        public override void ClearSingleton()
        {
            _lockDic?.Clear();
            _actionLockDic?.Clear();
            _lockDic = null;
            _actionLockDic = null;
        }

        void Update()
        {
            if (_isLock)
            {
                return;
            }

            // 检查所有已配置的按键
            CheckAllConfiguredKeys();
        }

        /// <summary>
        /// 检查所有已配置的按键
        /// </summary>
        private void CheckAllConfiguredKeys()
        {
            if (_keyBindingManager?.KeyBindingData == null)
                return;

            // 遍历所有按键绑定，检查按键状态
            foreach (var binding in _keyBindingManager.KeyBindingData.KeyBindings.Values)
            {
                // 检查主要按键
                if (binding.primaryKey != KeyCode.None)
                {
                    CheckKeyForAction(binding.primaryKey, binding.actionType);
                }

                // 检查次要按键
                if (binding.secondaryKey != KeyCode.None)
                {
                    CheckKeyForAction(binding.secondaryKey, binding.actionType);
                }
            }
        }

        /// <summary>
        /// 检查指定按键是否触发了对应的动作
        /// </summary>
        private void CheckKeyForAction(KeyCode keyCode, InputActionType actionType)
        {
            if (Input.GetKeyDown(keyCode))
            {
                if (!IsKeyLocked(keyCode) && !IsActionLocked(actionType))
                {
                    // 分发原始按键事件（保持向后兼容）
                    EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyDown, keyCode);

                    // 分发动作事件（新的事件系统）
                    EventDispatcher.GameEvent.DispatchEvent(EventName.InputActionTriggered, actionType);
                }
            }

            if (Input.GetKeyUp(keyCode))
            {
                if (!IsKeyLocked(keyCode) && !IsActionLocked(actionType))
                {
                    // 分发原始按键事件（保持向后兼容）
                    EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyUp, keyCode);
                }
            }
        }

        /// <summary>
        /// 是否锁定所有key
        /// </summary>
        /// <param name="isLock"></param>
        public void IsLockAllKey(bool isLock)
        {
            _isLock = isLock;
        }

        /// <summary>
        /// 锁定某个key
        /// </summary>
        /// <param name="keyCode"></param>
        public void RegistLocker(KeyCode keyCode)
        {
            if (_lockDic.ContainsKey(keyCode))
            {
                _lockDic[keyCode]++;
            }
            else
            {
                _lockDic.Add(keyCode, 1);
            }
        }

        /// <summary>
        /// 解锁某个key
        /// </summary>
        /// <param name="keyCode"></param>
        public void UnRegistLocker(KeyCode keyCode)
        {
            if (_lockDic.ContainsKey(keyCode))
            {
                _lockDic[keyCode]--;
                if (_lockDic[keyCode] < 0)
                {
                    Debug.LogError($"_______InputManager UnRegistLocker Error, {keyCode} 锁定次数小于0");
                    _lockDic[keyCode] = 0;
                }
            }
            else
            {
                _lockDic.Add(keyCode, 0);
            }
        }

        /// <summary>
        /// 锁定某个动作
        /// </summary>
        /// <param name="actionType"></param>
        public void RegistActionLocker(InputActionType actionType)
        {
            if (_actionLockDic.ContainsKey(actionType))
            {
                _actionLockDic[actionType]++;
            }
            else
            {
                _actionLockDic.Add(actionType, 1);
            }
        }

        /// <summary>
        /// 解锁某个动作
        /// </summary>
        /// <param name="actionType"></param>
        public void UnRegistActionLocker(InputActionType actionType)
        {
            if (_actionLockDic.ContainsKey(actionType))
            {
                _actionLockDic[actionType]--;
                if (_actionLockDic[actionType] < 0)
                {
                    Debug.LogError($"_______InputManager UnRegistActionLocker Error, {actionType} 锁定次数小于0");
                    _actionLockDic[actionType] = 0;
                }
            }
            else
            {
                _actionLockDic.Add(actionType, 0);
            }
        }

        /// <summary>
        /// 检查按键是否被锁定
        /// </summary>
        private bool IsKeyLocked(KeyCode keyCode)
        {
            return _lockDic.ContainsKey(keyCode) && _lockDic[keyCode] > 0;
        }

        /// <summary>
        /// 检查动作是否被锁定
        /// </summary>
        private bool IsActionLocked(InputActionType actionType)
        {
            return _actionLockDic.ContainsKey(actionType) && _actionLockDic[actionType] > 0;
        }

        /// <summary>
        /// 获取指定动作的按键绑定信息
        /// </summary>
        public InputActionData GetActionKeyBinding(InputActionType actionType)
        {
            return _keyBindingManager?.GetKeyBinding(actionType);
        }

        /// <summary>
        /// 检查指定动作是否被触发（按下）
        /// </summary>
        public bool IsActionTriggered(InputActionType actionType)
        {
            var binding = GetActionKeyBinding(actionType);
            if (binding == null) return false;

            bool primaryTriggered = binding.primaryKey != KeyCode.None && Input.GetKeyDown(binding.primaryKey);
            bool secondaryTriggered = binding.secondaryKey != KeyCode.None && Input.GetKeyDown(binding.secondaryKey);

            return (primaryTriggered || secondaryTriggered) && !IsActionLocked(actionType);
        }

        /// <summary>
        /// 检查指定动作是否正在被按住
        /// </summary>
        public bool IsActionHeld(InputActionType actionType)
        {
            var binding = GetActionKeyBinding(actionType);
            if (binding == null) return false;

            bool primaryHeld = binding.primaryKey != KeyCode.None && Input.GetKey(binding.primaryKey);
            bool secondaryHeld = binding.secondaryKey != KeyCode.None && Input.GetKey(binding.secondaryKey);

            return (primaryHeld || secondaryHeld) && !IsActionLocked(actionType);
        }

        private void CheckKeyCode(KeyCode keyCode)
        {
            if (Input.GetKeyDown(keyCode))
            {
                if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
                {
                    //事件中心模块 分发按下按下事件
                    EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyDown, keyCode);
                }
            }

            if (Input.GetKeyUp(keyCode))
            {
                if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
                {
                    //事件中心模块 分发按下抬起事件
                    EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyUp, keyCode);
                }
            }
        }
    }
}
