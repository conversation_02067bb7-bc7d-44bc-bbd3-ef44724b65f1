using System.Collections.Generic;
using UnityEngine;
using UnityCommunity.UnitySingleton;
using Common;


/// <summary>
/// 1.Input类
/// 2.事件中心模块
/// 3.公共Mono模块的使用
/// </summary>
public class InputManager : PersistentMonoSingleton<InputManager>
{
    private bool _isLock;
    private Dictionary<KeyCode, int> _lockDic;

    protected override void OnInitialized()
    {
        _lockDic = new();
        _isLock = false;
    }

    public override void ClearSingleton()
    {
        _lockDic.Clear();
        _lockDic = null;
    }

    public void InitConfig()
    {

    }


    void Update()
    {
        if (_isLock)
        {
            return;
        }

        CheckKeyCode(KeyCode.Escape);
        CheckKeyCode(KeyCode.Alpha1);
        CheckKeyCode(KeyCode.Alpha2);
        CheckKeyCode(KeyCode.Alpha3);
        CheckKeyCode(KeyCode.Space);
    }



    /// <summary>
    /// 是否锁定所有key
    /// </summary>
    /// <param name="isLock"></param>
    public void IsLockAllKey(bool isLock)
    {
        _isLock = isLock;
    }

    /// <summary>
    /// 锁定某个key
    /// </summary>
    /// <param name="keyCode"></param>
    public void RegistLocker(KeyCode keyCode)
    {
        if (_lockDic.ContainsKey(keyCode))
        {
            _lockDic[keyCode]++;
        }
        else
        {
            _lockDic.Add(keyCode, 1);
        }
    }

    /// <summary>
    /// 解锁某个key
    /// </summary>
    /// <param name="keyCode"></param>
    public void UnRegistLocker(KeyCode keyCode)
    {
        if (_lockDic.ContainsKey(keyCode))
        {
            _lockDic[keyCode]--;
            if (_lockDic[keyCode] < 0)
            {
                Debug.LogError($"_______InputManager UnRegistLocker Error, {keyCode} 锁定次数小于0");
                _lockDic[keyCode] = 0;
            }
        }
        else
        {
            _lockDic.Add(keyCode, 0);
        }
    }



    private void CheckKeyCode(KeyCode keyCode)
    {
        if (Input.GetKeyDown(keyCode))
        {
            if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
            {
                //事件中心模块 分发按下按下事件
                EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyDown, keyCode);
            }
        }

        if (Input.GetKeyUp(keyCode))
        {
            if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
            {
                //事件中心模块 分发按下抬起事件
                EventDispatcher.GameEvent.DispatchEvent(EventName.InputKeyUp, keyCode);
            }
        }
    }
}
