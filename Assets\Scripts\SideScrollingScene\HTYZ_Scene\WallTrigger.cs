using Common;
using UnityEngine;

namespace SideScrollingScene.HTYZ_Scene
{
    class WallTrigger : MonoBehaviour
    {
        public SpriteRenderer Sr_Wall_Transparent;
        private int _wallPeopleCount = 0;
        private bool _isPeopleThroughWall = false;
        private SpriteRenderer _srNormal;
        int WallPeopleCount
        {
            get
            {
                return _wallPeopleCount;
            }
            set
            {
                _wallPeopleCount = value;
                if (_isPeopleThroughWall == _wallPeopleCount > 0)
                {
                    return;
                }
                _isPeopleThroughWall = _wallPeopleCount > 0;
                Sr_Wall_Transparent.color = _isPeopleThroughWall ? Color.white : Color.clear;
                _srNormal.color = !_isPeopleThroughWall ? Color.white : Color.clear;
            }
        }

        void Awake()
        {
            _isPeopleThroughWall = false;
            _wallPeopleCount = 0;
            Sr_Wall_Transparent.color = Color.clear;
            _srNormal = GetComponent<SpriteRenderer>();
            _srNormal.color = Color.white;
        }

        public void OnTriggerEnter2D(Collider2D collision)
        {
            WallPeopleCount++;
        }

        public void OnTriggerExit2D(Collider2D collision)
        {
            WallPeopleCount--;
        }
    }
}