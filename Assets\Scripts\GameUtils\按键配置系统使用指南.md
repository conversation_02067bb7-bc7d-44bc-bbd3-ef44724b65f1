# 按键配置系统使用指南

## 系统简介

我为你创建了一个完整的按键配置系统，让玩家可以自定义游戏中的按键绑定。这个系统具有以下特点：

✅ **完全可配置** - 玩家可以自定义所有按键  
✅ **持久化保存** - 配置自动保存到磁盘  
✅ **冲突检测** - 防止重复绑定按键  
✅ **双按键支持** - 每个动作支持主要和次要按键  
✅ **向后兼容** - 不影响现有代码  
✅ **事件驱动** - 基于事件系统，性能优秀  

## 核心文件

### 1. 数据层
- `InputAction.cs` - 定义动作类型和数据结构
- `KeyBindingData.cs` - 按键配置的数据存储类

### 2. 管理层
- `KeyBindingManager.cs` - 按键配置管理器（单例）
- `InputManager.cs` - 增强的输入管理器

### 3. UI层
- `KeyBindingUI.cs` - 按键配置界面
- `KeyBindingItem.cs` - 单个按键配置项

### 4. 测试和示例
- `KeyBindingTest.cs` - 测试脚本
- `GameManager.cs` - 集成示例

## 快速开始

### 1. 基本设置

在你的场景中添加以下组件：

```csharp
// 确保场景中有这些单例组件
InputManager.Instance;          // 输入管理器
KeyBindingManager.Instance;     // 按键配置管理器
```

### 2. 监听输入事件

在你的脚本中添加事件监听：

```csharp
private void Start()
{
    // 监听动作触发事件
    EventDispatcher.GameEvent.Regist<InputActionType>(
        EventName.InputActionTriggered, 
        OnInputActionTriggered
    );
}

private void OnInputActionTriggered(InputActionType actionType)
{
    switch (actionType)
    {
        case InputActionType.Escape:
            // 处理退出逻辑
            break;
        case InputActionType.Menu:
            // 处理菜单逻辑
            break;
        // ... 其他动作
    }
}

private void OnDestroy()
{
    // 记得取消监听
    EventDispatcher.GameEvent.UnRegist<InputActionType>(
        EventName.InputActionTriggered, 
        OnInputActionTriggered
    );
}
```

### 3. 创建按键配置UI

创建一个Canvas，添加以下UI结构：

```
Canvas
├── KeyBindingPanel (KeyBindingUI脚本)
│   ├── Header (标题)
│   ├── ScrollView
│   │   └── Content (用于放置按键配置项)
│   ├── ButtonPanel
│   │   ├── ResetButton (重置按钮)
│   │   ├── SaveButton (保存按钮)
│   │   └── CancelButton (取消按钮)
│   └── WaitingPanel (等待按键输入面板)
│       └── WaitingText (提示文本)
└── KeyBindingItemPrefab (按键配置项预制体)
    ├── ActionName (动作名称)
    ├── ActionDescription (动作描述)
    ├── PrimaryKeyButton (主要按键按钮)
    ├── SecondaryKeyButton (次要按键按钮)
    └── ClearSecondaryButton (清除次要按键按钮)
```

## 默认按键配置

系统提供了以下默认按键配置：

| 动作 | 主要按键 | 次要按键 | 说明 |
|------|----------|----------|------|
| 退出/取消 | ESC | - | 退出当前界面或取消操作 |
| 确认 | Enter | Space | 确认当前操作 |
| 菜单 | Tab | - | 打开/关闭主菜单 |
| 背包 | I | - | 打开/关闭背包界面 |
| 技能1 | 1 | - | 使用技能1 |
| 技能2 | 2 | - | 使用技能2 |
| 技能3 | 3 | - | 使用技能3 |
| 暂停 | P | - | 暂停/继续游戏 |
| 快速保存 | F5 | - | 快速保存游戏 |
| 快速加载 | F9 | - | 快速加载游戏 |
| 截图 | F12 | - | 截取游戏画面 |
| 调试 | F1 | - | 打开调试界面（不可重新绑定） |

## 高级功能

### 1. 动态检查按键状态

```csharp
// 检查动作是否被触发（按下）
bool isTriggered = InputManager.Instance.IsActionTriggered(InputActionType.Skill1);

// 检查动作是否正在被按住
bool isHeld = InputManager.Instance.IsActionHeld(InputActionType.Skill1);
```

### 2. 锁定输入

```csharp
// 锁定所有输入
InputManager.Instance.IsLockAllKey(true);

// 锁定特定动作
InputManager.Instance.RegistActionLocker(InputActionType.Skill1);
InputManager.Instance.UnRegistActionLocker(InputActionType.Skill1);
```

### 3. 程序化修改按键绑定

```csharp
var keyBindingManager = KeyBindingManager.Instance;

// 设置按键绑定
bool success = keyBindingManager.SetKeyBinding(InputActionType.Skill1, KeyCode.Q, true);

// 检查按键冲突
bool hasConflict = keyBindingManager.IsKeyConflict(KeyCode.Q);

// 重置为默认配置
keyBindingManager.ResetToDefault();
```

## 测试方法

1. 运行游戏
2. 按 Tab 键打开按键配置界面
3. 点击任意按键配置项进行重新绑定
4. 测试新的按键是否生效
5. 重启游戏验证配置是否保存

## 扩展建议

你可以根据游戏需要添加更多动作类型：

1. 在 `InputActionType` 枚举中添加新的动作
2. 在 `KeyBindingData.InitializeDefaultKeyBindings()` 中添加默认配置
3. 在你的游戏逻辑中处理新的动作事件

## 注意事项

1. **性能优化**：系统使用事件驱动，避免在Update中频繁检查
2. **数据安全**：配置自动保存，但建议在关键时刻手动保存
3. **用户体验**：提供重置功能，让玩家可以恢复默认配置
4. **兼容性**：保持了原有的按键事件系统，现有代码无需修改

这个系统为你的游戏提供了专业级的按键配置功能，让玩家可以根据自己的习惯自定义操作方式！
