
using System;
using System.Collections.Generic;
using DataCenter;
using UnityEngine;

namespace AttributeSystem
{

    /// <summary>
    /// 玩家内置属性聚合组件
    /// </summary>
    public class AttributeComp : MonoBehaviour
    {
        private IAttributeHolder mAttributeHolder;
        private AttributeRecord mRecordData;
        private Dictionary<AttrType, Attribute> _attributes = new Dictionary<AttrType, Attribute>();
        private Action<AttrType, object> _attrValueChange;
        public Action<AttrType, object> OnAttrValueChange
        {
            get
            {
                return _attrValueChange;
            }
        }

        public void InitAttributeData(IAttributeHolder attributeHolder)
        {
            mAttributeHolder = attributeHolder;
            var data = DataService.Ins().Get<AttributeSaveData>();
            AttributeRecord mRecordData = data.CreateAndGetRecord(mAttributeHolder.GetAttributeUniqueID());
            AttrType[] types = (AttrType[])Enum.GetValues(typeof(AttrType));

            foreach (var type in types)
            {
                Attribute attribute = new Attribute(type, _attrValueChange);
                _attributes.Add(type, attribute);
                mRecordData.ForceModifyEntryValue(type, attribute.Value);
            }
        }

        protected void Start()
        {
            _attrValueChange = new Action<AttrType, object>((type, value) =>
            {
                // 这里可以根据需要处理属性值变化的逻辑

            });
        }

        protected void Update()
        {
            if (_attributes != null)
            {
                foreach (var attr in _attributes.Values)
                {
                    attr.OnUpdate(Time.deltaTime);
                }
            }
        }

        protected void OnDestroy()
        {
            if (_attributes != null)
            {
                foreach (var attr in _attributes.Values)
                {
                    attr.Destroy();
                }
                _attributes.Clear();
                _attributes = null;
            }
        }

        /// <summary>获取属性</summary>
        public Attribute Get(AttrType attrType)
        {

            _attributes.TryGetValue(attrType, out Attribute attribute);
            return attribute;
        }


        /// <summary>
        /// 重置所有属性
        /// </summary>
        public void ResetAllAttr()
        {
            if (_attributes != null)
            {
                foreach (var attr in _attributes.Values)
                {
                    attr.Reset();
                }
            }
        }
    }
}
