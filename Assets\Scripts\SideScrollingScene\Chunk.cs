using System;
using System.Collections.Generic;
using Common;
using UnityEngine;

namespace SideScrollingScene
{
    public class Chunk : MonoBehaviour
    {

        public string SpritePath;

        public Bounds SpriteBounds = new Bounds();


        [Header("事件随机生成点位")]
        public List<EventSpawnPoint> EventSpawnPoints = new();


        [Header("玩家随机出生点位")]
        public List<Transform> PlayerSpawnPoints = new();


        private SpriteRenderer _spriteRenderer;
        SpriteRenderer SpRenderer
        {
            get
            {
                if (_spriteRenderer == null)
                {
                    _spriteRenderer = GetComponent<SpriteRenderer>();
                }
                return _spriteRenderer;
            }
        }

        void Awake()
        {

        }

        public void Load()
        {
            transform.gameObject.SetActive(true);
            ResourceMgr.Instance.LoadAsync<Sprite>(SpritePath, (sprite) =>
             {
                 if (sprite != null)
                 {
                     SpRenderer.sprite = sprite;
                 }
                 else
                 {
                     Debug.LogError($"场景块:{name},加载贴图为空,请检查路径:{SpritePath}");
                 }

             });
            // Debug.Log($"加载场景块:{name}");
        }

        public void UnLoad()
        {
            transform.gameObject.SetActive(false);
            SpRenderer.sprite = null;
            // Debug.Log($"卸载场景块:{name}");
        }

    }
}