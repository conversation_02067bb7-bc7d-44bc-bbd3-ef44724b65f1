
using UnityEngine;

namespace AttributeSystem
{
    /// <summary>
    /// 属性修改器类
    /// </summary>
    public class AttributeModifer
    {
        private AttrType _attrType;
        private OperationType _operationType;
        private float _value;
        private float _duration;
        private string _reason;
        private int _overridePriority;

        /// <summary>计时器</summary>
        private float _counter = 0f;

        public AttrType AttrType => _attrType;

        public OperationType OperationType => _operationType;

        /// <summary>来源(也可用作唯一KEY值)</summary>
        public string Reason => _reason;

        public float Value => _value;

        /// <summary>
        /// 如果优先级大于等于0,则说明是覆盖模式,当优先级相同时,则根据时间顺序覆盖
        /// </summary>
        public int Priority => _overridePriority;

        public float Duration => _duration;

        public AttributeModifer(AttrType charAttrType, OperationType operationType,
            float value, float duration = -1f, string reason = "", int overridePriority = -1)
        {
            _attrType = charAttrType;
            _operationType = operationType;
            _value = value;
            _duration = duration;
            _reason = reason;
            _overridePriority = overridePriority;
            _counter = 0f;
        }

        /// <summary>
        /// 添加一层堆栈
        /// </summary>
        /// <param name="modifier">修改器</param>
        public void AddStack(AttributeModifer modifier)
        {
            _value = modifier.Value;
        }

        public void RemoveStack()
        {
            // TODO: 实现移除堆栈逻辑
        }

        /// <summary>
        /// 修改计算值
        /// </summary>
        /// <param name="calculation">属性计算对象</param>
        public void OnModify(AttributeCalculation calculation)
        {
            switch (OperationType)
            {
                case OperationType.Add:
                    calculation.FixValue += Value;
                    break;
                case OperationType.Multiply:
                    calculation.ScaleValue += Value;
                    break;
                case OperationType.Override:
                    calculation.FixValue = 0;
                    calculation.ScaleValue = 1;
                    break;
                default:
                    break;
            }
            Debug.Log($"_____________modifier{Reason}   onModify Type:{OperationType} " +
                     $"value --->{Value} " +
                     $"fixValue --->{calculation.FixValue} " +
                     $"scaleValue --->{calculation.ScaleValue}");
        }

        /// <summary>
        /// 更新修改器
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        /// <returns>是否应该移除修改器</returns>
        public bool OnUpdate(float deltaTime)
        {
            if (_duration <= 0)
            {
                return false;
            }

            _counter += deltaTime;
            if (_counter > _duration)
            {
                return true;
            }
            return false;
        }
    }
}
