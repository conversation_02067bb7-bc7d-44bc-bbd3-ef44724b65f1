﻿using System;
using System.Collections.Generic;
using KoganeUnityLib;
using Player;
using StoryCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace StoryCommon
{
    [Serializable]
    public class DialogInfo
    {
        public string content;
        public int characterID;
        public string animName;
        public bool animLoop;
        
        public bool IsPlayer => characterID == 0;
    }
    
    [Serializable]
    public class OptionInfo
    {
        public List<string> optionList;
        public int optionIndex;
    }

    public static class StoryUtils
    {
        public static string ConversionPlaceHolder(string content)
        {
            var result = content.Replace("$p", PlayerEntity.Instance.UserName);
            return result;
        }
    }
    
}

