﻿using System.Collections.Generic;
using Story;
using StoryCommon;
using XNode;

namespace StoryEditor
{
    public class BranchNode : BaseNode
    {
        [Input] public Empty input;
        
        [Output(dynamicPortList =true )]
        public List<string> options;

        protected override void OnStart()
        {
            StoryManager.Instance.ShowOption(options, (optionStr) =>
            {
                var index = options.IndexOf(optionStr);
                OptionInfo optionInfo = new OptionInfo()
                {
                    optionIndex = index,
                    optionList = options
                };
                StoryManager.Instance.AddReviewInfoToList(optionInfo);
                StoryManager.Instance.SetStoryOptionVisible(false);
                var storyGraph = graph as StoryGraph;
                if (storyGraph != null) storyGraph.FinishDynamicNode(this, index);
            });
        }
    }
}