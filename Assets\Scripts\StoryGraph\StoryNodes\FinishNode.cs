﻿using Story;
using XNode;

namespace StoryEditor
{
    public class FinishNode : BaseNode {
        [Output] public Empty output;
        [Input] public Empty input;
        protected override void OnStart()
        {
            base.OnStart();
            StoryManager.Instance.SetStoryViewVisible(false);
            var storyGraph = graph as StoryGraph;
            if (storyGraph != null) storyGraph.FinishNode(this, "output");
        }
        
    }
}