using System.Collections;
using System.Collections.Generic;
using Common;
using Player;
using Story;
using StoryCommon;
using TMPro;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.UI;

public class ReviewPlayerItem
{
    private GameObject _go;
    private DialogInfo _dialogInfo;

    private TMP_Text _nameTxt;
    private TMP_Text _contentTxt;
    public ReviewPlayerItem(GameObject go)
    {
        _go = go;
        _nameTxt = go.transform.Find("dialog_root/name_txt").GetComponent<TMP_Text>();
        _contentTxt = go.transform.Find("dialog_root/content_root/content_txt").GetComponent<TMP_Text>();
    }

    public void BuildData(DialogInfo dialogInfo)
    {
        _dialogInfo = dialogInfo;
        RefreshView();
    }

    private void RefreshView()
    {
        _nameTxt.text = PlayerEntity.Instance.UserName;
        _contentTxt.text = _dialogInfo.content;
    }
}

public class ReviewNpcItem
{
    private GameObject _go;
    private DialogInfo _dialogInfo;

    private TMP_Text _nameTxt;
    private TMP_Text _contentTxt;

    public ReviewNpcItem(GameObject go)
    {
        _go = go;
        _nameTxt = go.transform.Find("dialog_root/name_txt").GetComponent<TMP_Text>();
        _contentTxt = go.transform.Find("dialog_root/content_root/content_txt").GetComponent<TMP_Text>();
    }

    public void BuildData(DialogInfo dialogInfo)
    {
        _dialogInfo = dialogInfo;
        RefreshView();
    }

    private void RefreshView()
    {

        // var npcDatas = ResourceMgr.Instance.LoadResource<NpcDatas>("datas/NpcDatas");
        ExcelDataMgr.Instance.SetTranslationFunc<NpcDatas, NpcData>("name");
        var npcDatas = ExcelDataMgr.Instance.GetTable<NpcDatas>();
        var npcData = npcDatas.GetNpcData(_dialogInfo.characterID);
        _nameTxt.text = npcData.name;
        _contentTxt.text = _dialogInfo.content;
    }
}

public class ReviewOptionItem
{
    private GameObject _go;
    private GameObject _optionGO;
    private GameObject _noOptionGO;
    private OptionInfo _optionInfo;

    public ReviewOptionItem(GameObject go)
    {
        _go = go;
        _optionGO = go.transform.Find("prefab_root/item_option").gameObject;
        _noOptionGO = go.transform.Find("prefab_root/item_no_option").gameObject;
    }


    public void BuildData(OptionInfo optionInfo)
    {
        _optionInfo = optionInfo;
        RefreshView();
    }

    private void RefreshView()
    {
        var optionList = _optionInfo.optionList;
        for (int i = 0; i < optionList.Count; i++)
        {
            GameObject go;
            if (i != _optionInfo.optionIndex)
            {
                go = Object.Instantiate(_noOptionGO, _go.transform);
            }
            else
            {
                go = Object.Instantiate(_optionGO, _go.transform);
            }

            var txt = go.transform.Find("option_txt").GetComponent<TMP_Text>();
            txt.text = optionList[i];
        }
    }
}

public class StoryReviewView : MonoBehaviour
{
    [SerializeField] private Button closeBtn;
    [SerializeField] private GameObject playerReviewPrefab;
    [SerializeField] private GameObject npcReviewPrefab;
    [SerializeField] private GameObject optionReviewPrefab;
    [SerializeField] private GameObject optionDiItem;
    [SerializeField] private Transform reviewItemRoot;

    // Start is called before the first frame update
    void Start()
    {
        closeBtn.onClick.AddListener(OnClickClose);
    }

    public void BuildData(List<object> reviewInfoList)
    {
        GoUtil.CleanAllChildren(reviewItemRoot.gameObject, optionDiItem.gameObject);
        foreach (object reviewInfo in reviewInfoList)
        {
            if (reviewInfo is DialogInfo dialogInfo)
            {
                if (dialogInfo.IsPlayer)
                {
                    var reviewItem = new ReviewPlayerItem(GameObject.Instantiate(playerReviewPrefab, reviewItemRoot));
                    reviewItem.BuildData(dialogInfo);
                }
                else
                {
                    var reviewItem = new ReviewNpcItem(GameObject.Instantiate(npcReviewPrefab, reviewItemRoot));
                    reviewItem.BuildData(dialogInfo);
                }
            }
            else if (reviewInfo is OptionInfo optionInfo)
            {
                var reviewItem = new ReviewOptionItem(GameObject.Instantiate(optionReviewPrefab, reviewItemRoot));
                reviewItem.BuildData(optionInfo);
            }
        }
        optionDiItem.transform.SetAsLastSibling();
    }

    private void OnClickClose()
    {
        StoryManager.Instance.SetStoryReviewVisible(false);
    }
}
