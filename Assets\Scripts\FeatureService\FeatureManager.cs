using System.Collections;
using CharacterSystem;
using EventObjSystem;
using SideScrollingScene;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using static EventsTable;

namespace FeatureService
{
    public class FeatureManager : Singleton<FeatureManager>
    {
        public void TriggerGameFeature(FeatureType featureType, IEventObj eventObj)
        {
            switch (featureType)
            {
                case FeatureType.Battle:
                    break;
                case FeatureType.Story:
                    break;
                case FeatureType.Divination:
                    break;
                case FeatureType.ThrowingHolyCups:
                    break;
                case FeatureType.GamblingDen:
                    break;
                case FeatureType.Shop:
                    break;
                case FeatureType.Default:
                    Debug.LogWarning($"TriggerGameFeature:Default eventObj.EventId:{eventObj.EventId}");
                    break;
            }
            if (eventObj != null)
            {
                MonoMgr.Instance.StartCoroutine(DestroyNpc(eventObj));
                Debug.Log($" npc EventId:{eventObj.EventId} 将在2s后销毁");
            }
            Debug.Log($"TriggerGameFeature:{EnumUtils.GetName(featureType)} npc.EventId:{eventObj.EventId}");
        }

        void TriggerBattle(int eventId)
        {

        }

        void TriggerStory(int eventId)
        {

        }

        IEnumerator DestroyNpc(IEventObj obj)
        {
            yield return new WaitForSeconds(2);
            BattleSceneManager.Instance.DestroyEventObj(obj);
        }
    }
}