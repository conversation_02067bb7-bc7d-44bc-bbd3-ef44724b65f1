using System.Collections;
using SideScrollingScene;
using UnityEngine;

namespace EventObjSystem
{

    public enum E_CardState
    {
        Faraway,
        Closer,
        Front
    }

    public class EventCard : IEventObj
    {

        private EventCardStyle _cardStyleInfo;
        public EventCardStyle CardStyleInfo
        {
            get
            {
                return _cardStyleInfo;
            }
        }

        public float CloserDistance = 10f;
        public float FrontDistance = 5f;

        E_CardState _cardState = E_CardState.Faraway;
        E_CardState CardState
        {
            get
            {
                return _cardState;
            }
            set
            {
                if (_cardState == value)
                {
                    return;
                }
                _cardState = value;
                OnCardStateChange();
                Vector3 playerPos = BattleSceneManager.Instance.Q_Player.transform.position;
                Vector3 cardPos = transform.position;
                float distance = Vector3.Distance(playerPos, cardPos);
                Debug.Log($"_________cardState{value} {distance}");
            }
        }

        public FlipCard mFlipCard;
        public ShadowMover mShadowMover;
        public GameObject ShadowCircle;

        private bool _isInitialized = false;
        public override void Init(int eventId)
        {
            base.Init(eventId);
            if (eventData != null)
            {
                mFlipCard.gameObject.SetActive(true);
                _cardStyleInfo = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventCardStyle(styleKey);
                mFlipCard.Init();
                mFlipCard.ResetFlip();
                mShadowMover.gameObject.SetActive(true);
                ShadowCircle.SetActive(true);
                _isInitialized = true;
            }
            else
            {
                mFlipCard.ResetFlip();
                mFlipCard.gameObject.SetActive(false);
                mShadowMover.gameObject.SetActive(false);
                ShadowCircle.SetActive(false);
                _isInitialized = false;
            }
        }

        void Start()
        {
            if (!_isInitialized)
            {
                Init(EventId);
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.Alpha0))
            {
                mFlipCard.Flip();
            }
            if (CanUpdate())
            {
                Vector3 playerPos = BattleSceneManager.Instance.Q_Player.transform.position;
                Vector3 cardPos = transform.position;
                float distance = Vector3.Distance(playerPos, cardPos);
                distance = Mathf.Abs(distance);
                if (distance > CloserDistance)
                {
                    CardState = E_CardState.Faraway;
                }
                else if (distance <= CloserDistance && distance > FrontDistance)
                {
                    CardState = E_CardState.Closer;
                }
                else if (distance <= FrontDistance)
                {
                    CardState = E_CardState.Front;
                }
            }
        }

        bool CanUpdate()
        {
            return BattleSceneManager.Instance.Q_Player != null && eventData != null;
        }

        void OnCardStateChange()
        {
            mFlipCard.OnCardStateChange(CardState);
            switch (CardState)
            {
                case E_CardState.Faraway:
                    mShadowMover.StopConveyorEffect();
                    break;
                case E_CardState.Closer:
                    mShadowMover.StartConveyorEffect();
                    break;
                case E_CardState.Front:
                    mShadowMover.StartConveyorEffect();
                    break;
            }
        }


        void OnDrawGizmos()
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.localPosition, CloserDistance);
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.localPosition, FrontDistance);
        }

    }
}