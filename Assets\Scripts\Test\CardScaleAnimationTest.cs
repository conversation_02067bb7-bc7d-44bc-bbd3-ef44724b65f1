using UnityEngine;
using EventObjSystem;

namespace Test
{
    /// <summary>
    /// 卡牌缩放动画测试脚本
    /// </summary>
    public class CardScaleAnimationTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private FlipCard testCard;
        [SerializeField] private float testScale1 = 1.0f;
        [SerializeField] private float testScale2 = 1.5f;
        [SerializeField] private float testScale3 = 0.8f;

        private void Update()
        {
            if (testCard == null) return;

            // 按键测试缩放动画
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                Debug.Log($"测试缩放到 {testScale1}");
                testCard.StartScaling(testScale1);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                Debug.Log($"测试缩放到 {testScale2}");
                testCard.StartScaling(testScale2);
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                Debug.Log($"测试缩放到 {testScale3}");
                testCard.StartScaling(testScale3);
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                Debug.Log("重置卡牌");
                testCard.ResetFlip();
            }
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("卡牌缩放动画测试");
            GUILayout.Label("按键说明：");
            GUILayout.Label($"1 - 缩放到 {testScale1}");
            GUILayout.Label($"2 - 缩放到 {testScale2}");
            GUILayout.Label($"3 - 缩放到 {testScale3}");
            GUILayout.Label("R - 重置卡牌");
            
            if (testCard != null)
            {
                GUILayout.Label($"当前缩放: {testCard.transform.localScale.x:F2}");
            }
            GUILayout.EndArea();
        }
    }
}
