//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;
using System.Collections.Generic;

public partial class DialogDatas : ScriptableObject {

	public Func<string, string> Translate;

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private DiagueData[] _DiagueDataItems;

	public List<DiagueData> GetDiagueDataList(int dialogID) {
		List<DiagueData> list = new List<DiagueData>(); 
		GetDiagueDataList(dialogID, list);
		return list;
	}
	public int GetDiagueDataList(int dialogID, List<DiagueData> list) {
		int min = 0;
		int len = _DiagueDataItems.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData item = _DiagueDataItems[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueDataItems[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueDataItems[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueDataItems[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_contentTranslate[] _DiagueData_contentTranslateItems;

	public DiagueData_contentTranslate GetDiagueData_contentTranslate(string key) {
		int min = 0;
		int max = _DiagueData_contentTranslateItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			DiagueData_contentTranslate item = _DiagueData_contentTranslateItems[index];
			if (item.key == key) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(key, item.key) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		string Translate(string key);
		int GetDiagueDataList(int dialogID, List<DiagueData> list);
		DiagueData_contentTranslate GetDiagueData_contentTranslate(string key);
	}

	private class DataGetter : IDataGetter {
		private Func<string, string> _Translate;
		public string Translate(string key) {
			return _Translate == null ? key : _Translate(key);
		}
		private Func<int, List<DiagueData>, int> _GetDiagueDataList;
		public int GetDiagueDataList(int dialogID, List<DiagueData> items) {
			return _GetDiagueDataList(dialogID, items);
		}
		private Func<string, DiagueData_contentTranslate> _GetDiagueData_contentTranslate;
		public DiagueData_contentTranslate GetDiagueData_contentTranslate(string key) {
			return _GetDiagueData_contentTranslate(key);
		}
		public DataGetter(Func<string, string> translate, Func<int, List<DiagueData>, int> getDiagueDataList, Func<string, DiagueData_contentTranslate> getDiagueData_contentTranslate) {
			_Translate = translate;
			_GetDiagueDataList = getDiagueDataList;
			_GetDiagueData_contentTranslate = getDiagueData_contentTranslate;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(Translate, GetDiagueDataList, GetDiagueData_contentTranslate);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class DiagueData {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	private string _Content_;
	public string content { get { if (_Content_ == null) { _Content_ = mGetter.Translate(_Content); } return _Content_; } }

	[SerializeField]
	private string _ContentDes;
	public string contentDes { get { return _ContentDes; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		_Content_ = null;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData]{{dialogID:{0}, ID:{1}, content:{2}, contentDes:{3}, charcterID:{4}, animName:{5}, animLoop:{6}}}",
			dialogID, ID, content, contentDes, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_contentTranslate {

	[SerializeField]
	private string _Key;
	public string key { get { return _Key; } }

	[SerializeField]
	private string _Chinese;
	public string Chinese { get { return _Chinese; } }

	[SerializeField]
	private string _English;
	public string English { get { return _English; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_contentTranslate Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_contentTranslate]{{key:{0}, Chinese:{1}, English:{2}}}",
			key, Chinese, English);
	}

}

