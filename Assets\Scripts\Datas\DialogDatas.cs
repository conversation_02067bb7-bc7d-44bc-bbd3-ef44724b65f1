//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;
using System.Collections.Generic;

public partial class DialogDatas : ScriptableObject {

	public Func<string, string> Translate;

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private DiagueData[] _DiagueDataItems;

	public List<DiagueData> GetDiagueDataList(int dialogID) {
		List<DiagueData> list = new List<DiagueData>(); 
		GetDiagueDataList(dialogID, list);
		return list;
	}
	public int GetDiagueDataList(int dialogID, List<DiagueData> list) {
		int min = 0;
		int len = _DiagueDataItems.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData item = _DiagueDataItems[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueDataItems[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueDataItems[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueDataItems[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_contentTranslate[] _DiagueData_contentTranslateItems;

	public DiagueData_contentTranslate GetDiagueData_contentTranslate(string key) {
		int min = 0;
		int max = _DiagueData_contentTranslateItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			DiagueData_contentTranslate item = _DiagueData_contentTranslateItems[index];
			if (item.key == key) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(key, item.key) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	[SerializeField]
	private DiagueData_2[] _DiagueData_2Items;

	public List<DiagueData_2> GetDiagueData_2List(int dialogID) {
		List<DiagueData_2> list = new List<DiagueData_2>(); 
		GetDiagueData_2List(dialogID, list);
		return list;
	}
	public int GetDiagueData_2List(int dialogID, List<DiagueData_2> list) {
		int min = 0;
		int len = _DiagueData_2Items.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData_2 item = _DiagueData_2Items[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueData_2Items[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueData_2Items[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueData_2Items[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_3[] _DiagueData_3Items;

	public List<DiagueData_3> GetDiagueData_3List(int dialogID) {
		List<DiagueData_3> list = new List<DiagueData_3>(); 
		GetDiagueData_3List(dialogID, list);
		return list;
	}
	public int GetDiagueData_3List(int dialogID, List<DiagueData_3> list) {
		int min = 0;
		int len = _DiagueData_3Items.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData_3 item = _DiagueData_3Items[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueData_3Items[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueData_3Items[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueData_3Items[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_4[] _DiagueData_4Items;

	public List<DiagueData_4> GetDiagueData_4List(int dialogID) {
		List<DiagueData_4> list = new List<DiagueData_4>(); 
		GetDiagueData_4List(dialogID, list);
		return list;
	}
	public int GetDiagueData_4List(int dialogID, List<DiagueData_4> list) {
		int min = 0;
		int len = _DiagueData_4Items.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData_4 item = _DiagueData_4Items[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueData_4Items[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueData_4Items[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueData_4Items[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_5[] _DiagueData_5Items;

	public List<DiagueData_5> GetDiagueData_5List(int dialogID) {
		List<DiagueData_5> list = new List<DiagueData_5>(); 
		GetDiagueData_5List(dialogID, list);
		return list;
	}
	public int GetDiagueData_5List(int dialogID, List<DiagueData_5> list) {
		int min = 0;
		int len = _DiagueData_5Items.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData_5 item = _DiagueData_5Items[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueData_5Items[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueData_5Items[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueData_5Items[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	[SerializeField]
	private DiagueData_6[] _DiagueData_6Items;

	public List<DiagueData_6> GetDiagueData_6List(int dialogID) {
		List<DiagueData_6> list = new List<DiagueData_6>(); 
		GetDiagueData_6List(dialogID, list);
		return list;
	}
	public int GetDiagueData_6List(int dialogID, List<DiagueData_6> list) {
		int min = 0;
		int len = _DiagueData_6Items.Length;
		int max = len;
		int index = -1;
		while (min < max) {
			int i = (min + max) >> 1;
			DiagueData_6 item = _DiagueData_6Items[i];
			if (item.dialogID == dialogID) {
				index = i;
				break;
			}
			if (dialogID < item.dialogID) {
				max = i;
			} else {
				min = i + 1;
			}
		}
		if (index < 0) { return 0; }
		int l = index;
		while (l - 1 >= 0 && _DiagueData_6Items[l - 1].dialogID == dialogID) { l--; }
		int r = index;
		while (r + 1 < len && _DiagueData_6Items[r + 1].dialogID == dialogID) { r++; }
		for (int i = l; i <= r; i++) {
			list.Add(_DiagueData_6Items[i].Init(mVersion, DataGetterObject));
		}
		return r - l + 1;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		string Translate(string key);
		int GetDiagueDataList(int dialogID, List<DiagueData> list);
		DiagueData_contentTranslate GetDiagueData_contentTranslate(string key);
		int GetDiagueData_2List(int dialogID, List<DiagueData_2> list);
		int GetDiagueData_3List(int dialogID, List<DiagueData_3> list);
		int GetDiagueData_4List(int dialogID, List<DiagueData_4> list);
		int GetDiagueData_5List(int dialogID, List<DiagueData_5> list);
		int GetDiagueData_6List(int dialogID, List<DiagueData_6> list);
	}

	private class DataGetter : IDataGetter {
		private Func<string, string> _Translate;
		public string Translate(string key) {
			return _Translate == null ? key : _Translate(key);
		}
		private Func<int, List<DiagueData>, int> _GetDiagueDataList;
		public int GetDiagueDataList(int dialogID, List<DiagueData> items) {
			return _GetDiagueDataList(dialogID, items);
		}
		private Func<string, DiagueData_contentTranslate> _GetDiagueData_contentTranslate;
		public DiagueData_contentTranslate GetDiagueData_contentTranslate(string key) {
			return _GetDiagueData_contentTranslate(key);
		}
		private Func<int, List<DiagueData_2>, int> _GetDiagueData_2List;
		public int GetDiagueData_2List(int dialogID, List<DiagueData_2> items) {
			return _GetDiagueData_2List(dialogID, items);
		}
		private Func<int, List<DiagueData_3>, int> _GetDiagueData_3List;
		public int GetDiagueData_3List(int dialogID, List<DiagueData_3> items) {
			return _GetDiagueData_3List(dialogID, items);
		}
		private Func<int, List<DiagueData_4>, int> _GetDiagueData_4List;
		public int GetDiagueData_4List(int dialogID, List<DiagueData_4> items) {
			return _GetDiagueData_4List(dialogID, items);
		}
		private Func<int, List<DiagueData_5>, int> _GetDiagueData_5List;
		public int GetDiagueData_5List(int dialogID, List<DiagueData_5> items) {
			return _GetDiagueData_5List(dialogID, items);
		}
		private Func<int, List<DiagueData_6>, int> _GetDiagueData_6List;
		public int GetDiagueData_6List(int dialogID, List<DiagueData_6> items) {
			return _GetDiagueData_6List(dialogID, items);
		}
		public DataGetter(Func<string, string> translate, Func<int, List<DiagueData>, int> getDiagueDataList, Func<string, DiagueData_contentTranslate> getDiagueData_contentTranslate, Func<int, List<DiagueData_2>, int> getDiagueData_2List, Func<int, List<DiagueData_3>, int> getDiagueData_3List, Func<int, List<DiagueData_4>, int> getDiagueData_4List, Func<int, List<DiagueData_5>, int> getDiagueData_5List, Func<int, List<DiagueData_6>, int> getDiagueData_6List) {
			_Translate = translate;
			_GetDiagueDataList = getDiagueDataList;
			_GetDiagueData_contentTranslate = getDiagueData_contentTranslate;
			_GetDiagueData_2List = getDiagueData_2List;
			_GetDiagueData_3List = getDiagueData_3List;
			_GetDiagueData_4List = getDiagueData_4List;
			_GetDiagueData_5List = getDiagueData_5List;
			_GetDiagueData_6List = getDiagueData_6List;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(Translate, GetDiagueDataList, GetDiagueData_contentTranslate, GetDiagueData_2List, GetDiagueData_3List, GetDiagueData_4List, GetDiagueData_5List, GetDiagueData_6List);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class DiagueData {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	private string _Content_;
	public string content { get { if (_Content_ == null) { _Content_ = mGetter.Translate(_Content); } return _Content_; } }

	[SerializeField]
	private string _ContentDes;
	public string contentDes { get { return _ContentDes; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		_Content_ = null;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData]{{dialogID:{0}, ID:{1}, content:{2}, contentDes:{3}, charcterID:{4}, animName:{5}, animLoop:{6}}}",
			dialogID, ID, content, contentDes, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_contentTranslate {

	[SerializeField]
	private string _Key;
	public string key { get { return _Key; } }

	[SerializeField]
	private string _Chinese;
	public string Chinese { get { return _Chinese; } }

	[SerializeField]
	private string _English;
	public string English { get { return _English; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_contentTranslate Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_contentTranslate]{{key:{0}, Chinese:{1}, English:{2}}}",
			key, Chinese, English);
	}

}

[Serializable]
public class DiagueData_2 {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	public string content { get { return _Content; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_2 Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_2]{{dialogID:{0}, ID:{1}, content:{2}, charcterID:{3}, animName:{4}, animLoop:{5}}}",
			dialogID, ID, content, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_3 {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	public string content { get { return _Content; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_3 Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_3]{{dialogID:{0}, ID:{1}, content:{2}, charcterID:{3}, animName:{4}, animLoop:{5}}}",
			dialogID, ID, content, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_4 {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	public string content { get { return _Content; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_4 Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_4]{{dialogID:{0}, ID:{1}, content:{2}, charcterID:{3}, animName:{4}, animLoop:{5}}}",
			dialogID, ID, content, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_5 {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	public string content { get { return _Content; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_5 Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_5]{{dialogID:{0}, ID:{1}, content:{2}, charcterID:{3}, animName:{4}, animLoop:{5}}}",
			dialogID, ID, content, charcterID, animName, animLoop);
	}

}

[Serializable]
public class DiagueData_6 {

	[SerializeField]
	private int _DialogID;
	public int dialogID { get { return _DialogID; } }

	[SerializeField]
	private int _ID;
	public int ID { get { return _ID; } }

	[SerializeField]
	private string _Content;
	public string content { get { return _Content; } }

	[SerializeField]
	private int _CharcterID;
	public int charcterID { get { return _CharcterID; } }

	[SerializeField]
	private string _AnimName;
	public string animName { get { return _AnimName; } }

	[SerializeField]
	private bool _AnimLoop;
	public bool animLoop { get { return _AnimLoop; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private DialogDatas.IDataGetter mGetter;

	public DiagueData_6 Init(int version, DialogDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[DiagueData_6]{{dialogID:{0}, ID:{1}, content:{2}, charcterID:{3}, animName:{4}, animLoop:{5}}}",
			dialogID, ID, content, charcterID, animName, animLoop);
	}

}

