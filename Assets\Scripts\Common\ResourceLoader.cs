using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityCommunity.UnitySingleton;
using UnityEngine;


namespace Common
{
    public class ResourceLoader : Singleton<ResourceLoader>
    {
        // 缓存已经加载好的资产
        readonly Dictionary<string, Object> _assetCache = new();
        //   正在加载的 Promise 去重字典
        readonly Dictionary<string, object> _loadingSources = new();
        // 实例 → Asset 路径 映射，用于释放时找到对应 Asset
        Dictionary<Object, string> _instanceMap = new();

        public T Load<T>(string path) where T : Object
        {
            return Resources.Load<T>(path);
        }

        /// <summary>
        /// 加载并实例化一个新的场景对象（唯一实例）
        /// </summary>
        public async UniTask<T> LoadAsync<T>(string path, CancellationToken token = default)
            where T : Object
        {
            // 1. 先加载（或复用缓存）Asset
            var asset = await LoadAssetAsync<T>(path, token);
            // 2. Instantiate 得到场景内实例
            var instance = Object.Instantiate(asset) as T;
            // 3. 记录实例对应的路径
            _instanceMap[instance] = path;
            return instance;
        }
        /// <summary>
        /// 异步加载 T 资产，去重、支持多 await
        /// </summary>
        public UniTask<T> LoadAssetAsync<T>(string path, CancellationToken token = default)
            where T : UnityEngine.Object
        {
            // 1) 如果已经加载过，直接返回结果
            if (_assetCache.TryGetValue(path, out var exists) && exists is T t)
            {
                return UniTask.FromResult(t);
            }

            // 2) 如果正在加载，则复用同一个 Promise
            if (_loadingSources.TryGetValue(path, out var srcObj)
                && srcObj is UniTaskCompletionSource<T> existingSrc)
            {
                return existingSrc.Task;
            }

            // 3) 否则新建一个 Promise
            var src = new UniTaskCompletionSource<T>();
            _loadingSources[path] = src;

            // 发起真正的加载
            ActuallyLoadAsync(path, src, token).Forget();
            return src.Task;
        }

        // 真正的加载流程：完成后设置结果并清理
        private async UniTaskVoid ActuallyLoadAsync<T>(
            string path,
            UniTaskCompletionSource<T> src,
            CancellationToken token)
            where T : UnityEngine.Object
        {
            try
            {
                var req = Resources.LoadAsync<T>(path);
                await req.ToUniTask(cancellationToken: token);

                var asset = req.asset as T;
                if (asset == null)
                    throw new System.Exception($"[ResourceManager] 资源加载失败: {path}");

                _assetCache[path] = asset;
                src.TrySetResult(asset);
            }
            catch (System.Exception ex)
            {
                src.TrySetException(ex);
            }
            finally
            {
                _loadingSources.Remove(path);
            }
        }

        /// <summary>
        /// 销毁实例并自动卸载对应的 Asset（如果没有其它实例在用）
        /// </summary>
        public void ReleaseInstance(Object instance)
        {
            if (!_instanceMap.TryGetValue(instance, out var path))
            {
                Debug.LogWarning($"[ResourceManager] 未知实例: {instance}");
                return;
            }

            // 销毁场景中的实例
            Object.Destroy(instance);
            _instanceMap.Remove(instance);

            // 如果再也没人用这个路径的实例，就准备卸载它对应的 Asset
            if (!_instanceMap.ContainsValue(path))
            {
                if (_assetCache.TryGetValue(path, out var asset))
                {
                    // 1. 如果 asset 不是 GameObject，就可以安全调用 UnloadAsset
                    if (!(asset is GameObject))
                    {
                        Resources.UnloadAsset(asset);
                    }
                    // 2. 无论哪种类型，都从缓存里移除
                    _assetCache.Remove(path);

                    // // 3. 对于 GameObject 资产，或者为了彻底清理内存，
                    // //    可以在下一帧调用 UnloadUnusedAssets
                    // _ = UniTask.RunOnThreadPool(async () =>
                    // {
                    //     // 等待一帧，确保所有 Destroy 都执行完
                    //     await UniTask.Yield();
                    //     // 异步执行 UnloadUnusedAssets
                    //     await Resources.UnloadUnusedAssets();
                    // });
                }
            }
        }

        /// <summary>
        /// 强制卸载所有资源
        /// </summary>
        public void ReleaseAll()
        {
            foreach (var kv in _instanceMap)
                Object.Destroy(kv.Key as Object);
            _instanceMap.Clear();

            foreach (var kv in _assetCache)
            {
                // 只能对非 GameObject 用 UnloadAsset
                if (!(kv.Value is GameObject))
                    Resources.UnloadAsset(kv.Value);
            }
            _assetCache.Clear();
            // 全局回收一次
            Resources.UnloadUnusedAssets();
        }

    }
}