using System;
using System.Collections.Generic;
using UnityEngine;

namespace AttributeSystem
{
    /// <summary>
    /// 基础属性类
    /// </summary>
    public class Attribute
    {
        private List<AttributeCalculation> _modChannel;
        private AttrDataType _attrDataType;
        private AttrType _attrType;
        private string _attributeName;
        private bool _isDestroy = false;
        private Action<AttrType, object> _onChangeAction;
        private float _baseValue;
        private float _value;

        public float Value => _value;

        private int _level;

        private AttributeConfig _data;

        public Attribute(AttrType attrType, Action<AttrType, object> action)
        {
            _onChangeAction = action;
            _attrType = attrType;
            _attributeName = attrType.ToString();
            _modChannel = new List<AttributeCalculation>();
            _isDestroy = false;
            //TODO 留了个value类型的口子
            int length = 1;
            _attrDataType = AttrDataType.Number;

            for (int i = 0; i < length; i++)
            {
                _modChannel.Add(new AttributeCalculation(_attrType));
            }
        }

        /// <summary>
        /// 初始化基础值
        /// </summary>
        /// <param name="value">初始值，可以是float或Vector3</param>
        public void InitBaseValue(object value = null)
        {
            if (value is float floatValue)
            {
                _modChannel[0].SetBaseValue(floatValue);
            }
            else if (value is Vector3 vectorValue)
            {
                _modChannel[0].SetBaseValue(vectorValue.x);
                _modChannel[1].SetBaseValue(vectorValue.y);
                _modChannel[2].SetBaseValue(vectorValue.z);
            }
            UpdateValue();
        }

        /// <summary>
        /// 清除所有修改器效果，重置为默认属性值
        /// </summary>
        public void Reset()
        {
            foreach (var mod in _modChannel)
            {
                mod.Reset();
            }
            UpdateValue();
        }


        #region AddModifier方法重载 便于直接调用

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="reason">用于溯源及删除的key</param>
        public void AddModifier(float value, string reason)
        {
            AddModifierInternal(value, -1f, reason);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="reason">用于溯源及删除的key</param>
        public void AddModifier(Vector3 value, string reason)
        {
            AddModifierInternal(value, -1f, reason);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间(单位为秒) 若为永久则填-1</param>
        public void AddModifier(float value, float duration)
        {
            AddModifierInternal(value, duration);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间(单位为秒) 若为永久则填-1</param>
        public void AddModifier(Vector3 value, float duration)
        {
            AddModifierInternal(value, duration);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="reason">用于溯源及删除的key值</param>
        public void AddModifier(float value, float duration, string reason)
        {
            AddModifierInternal(value, duration, reason);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="reason">用于溯源及删除的key值</param>
        public void AddModifier(Vector3 value, float duration, string reason)
        {
            AddModifierInternal(value, duration, reason);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">覆盖优先级</param>
        public void AddModifier(float value, float duration, int priority)
        {
            AddModifierInternal(value, duration, "", priority);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">覆盖优先级</param>
        public void AddModifier(Vector3 value, float duration, int priority)
        {
            AddModifierInternal(value, duration, "", priority);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="reason">用于溯源及手动删除的key值</param>
        public void AddModifier(float value, float duration, int priority, string reason)
        {
            AddModifierInternal(value, duration, reason, priority);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="reason">用于溯源及手动删除的key值</param>
        public void AddModifier(Vector3 value, float duration, int priority, string reason)
        {
            AddModifierInternal(value, duration, reason, priority);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="operationType">计算类型</param>
        public void AddModifier(float value, float duration, int priority, OperationType operationType)
        {
            AddModifierInternal(value, duration, "", priority, operationType);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="operationType">计算类型</param>
        public void AddModifier(Vector3 value, float duration, int priority, OperationType operationType)
        {
            AddModifierInternal(value, duration, "", priority, operationType);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="operationType">计算类型</param>
        /// <param name="reason">用于溯源及手动删除的key值</param>
        public void AddModifier(float value, float duration, int priority, OperationType operationType, string reason)
        {
            AddModifierInternal(value, duration, reason, priority, operationType);
        }

        /// <summary>
        /// 默认为直接覆盖模式
        /// </summary>
        /// <param name="value">修改变量 计算模式为覆盖则为最终值，否则为增量</param>
        /// <param name="duration">持续时间 (单位为秒) 若为永久则填-1</param>
        /// <param name="priority">优先级</param>
        /// <param name="operationType">计算类型</param>
        /// <param name="reason">用于溯源及手动删除的key值</param>
        public void AddModifier(Vector3 value, float duration, int priority, OperationType operationType, string reason)
        {
            AddModifierInternal(value, duration, reason, priority, operationType);
        }

        #endregion


        /// <summary>
        /// 修改属性值
        /// </summary>
        /// <param name="value">修改值(根据计算类型的不同，而是增值或者直接修改值)</param>
        /// <param name="duration">持续时间(不填,默认为永久修改,需手动移除) (单位为秒)</param>
        /// <param name="reason">原因(可作为唯一KEY) 默认为"" 若是需手动移除,则必填</param>
        /// <param name="overridePriority">覆盖优先级 默认为-1 非覆盖模式,若为覆盖则默认为0</param>
        /// <param name="operationType">计算类型 默认为 OperationType.Override</param>
        private void AddModifierInternal(object value,
            float duration = -1f,
            string reason = "",
            int overridePriority = -1,
            OperationType operationType = OperationType.Override)
        {
            if (_isDestroy)
            {
                return;
            }

            if (operationType == OperationType.Override && overridePriority < 0)
            {
                overridePriority = 0;
            }

            if (value is float floatValue)
            {
                var modifier = new AttributeModifer(_attrType,
                    operationType, floatValue, duration, reason, overridePriority);
                _modChannel[0].AddModifier(modifier);
            }
            else if (value is Vector3 vectorValue)
            {
                float[] nums = { vectorValue.x, vectorValue.y, vectorValue.z };
                for (int i = 0; i < 3; i++)
                {
                    var modifier = new AttributeModifer(_attrType,
                        operationType, nums[i], duration, reason, overridePriority);
                    _modChannel[i].AddModifier(modifier);
                }
            }
            UpdateValue();
        }

        /// <summary>
        /// 删除修改器
        /// </summary>
        /// <param name="reason">原因</param>
        public void RemoveModifier(string reason)
        {
            if (string.IsNullOrEmpty(reason))
            {
                Debug.Log($"________________removeModifier_{_attributeName} fail,source invalid");
                return;
            }

            switch (_attrDataType)
            {
                case AttrDataType.Vector:
                    for (int i = 0; i < 3; i++)
                    {
                        _modChannel[i].RemoveModifier(reason);
                    }
                    break;
                case AttrDataType.Number:
                    _modChannel[0].RemoveModifier(reason);
                    break;
                default:
                    break;
            }
            UpdateValue();
        }

        /// <summary>
        /// 更新属性
        /// </summary>
        private void UpdateValue()
        {

            float newValue;
            switch (_attrDataType)
            {
                // case AttrDataType.Vector:
                //     newValue = new Vector3(
                //         _modChannel[0].FinalValue,
                //         _modChannel[1].FinalValue,
                //         _modChannel[2].FinalValue
                //     );
                //     break;
                case AttrDataType.Number:
                    newValue = _modChannel[0].FinalValue;
                    break;
                default:
                    newValue = 0f;
                    break;
            }

            _value = newValue;
            _onChangeAction?.Invoke(_attrType, _value);
        }

        public void OnUpdate(float deltaTime)
        {
            bool isModChange = false;
            foreach (var mod in _modChannel)
            {
                if (mod.OnUpdate(deltaTime))
                {
                    isModChange = true;
                }
            }
            if (isModChange)
            {
                UpdateValue();
            }
        }

        public void Destroy()
        {
            foreach (var modChannel in _modChannel)
            {
                modChannel.Destroy();
            }
            _modChannel.Clear();
            _isDestroy = true;
        }
    }
}