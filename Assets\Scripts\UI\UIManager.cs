﻿using System;
using System.Collections.Generic;
using UI;
using UnityCommunity.UnitySingleton;
using UnityEngine;

namespace UI
{
    public enum ViewRootEnum
    {
        HUD,
        STORY
    }
    public class UIManager : MonoSingleton<UIManager>
    {
        private GameObject UIRoot;

        private Dictionary<string, ViewPresenter> _viewDict = new Dictionary<string, ViewPresenter>();
        private Dictionary<ViewRootEnum, GameObject> _viewRootDict= new Dictionary<ViewRootEnum, GameObject>();

        protected override void Awake()
        {
            base.Awake();
            UISetting.Init();
            this.UIRoot = gameObject;
            _viewRootDict[ViewRootEnum.HUD] = this.transform.Find("HUD").gameObject;
            _viewRootDict[ViewRootEnum.STORY] = this.transform.Find("STORY").gameObject;
        }

        public GameObject GetUIRoot()
        {
            return UIRoot;
        }

        public GameObject GetViewRoot(ViewRootEnum viewRootEnum)
        {
            GameObject tmpGo = null;
            _viewRootDict.TryGetValue(viewRootEnum, out tmpGo);
            return tmpGo;
        }

        public void ShowHUD()
        {
            var go = _viewRootDict[ViewRootEnum.HUD];
            go.SetActive(true);
        }

        public void HideHUD()
        {
            var go = _viewRootDict[ViewRootEnum.HUD];
            go.SetActive(false);
        }

        public void Open(string viewName, params object[] viewParams)
        {
            var settingObj = UISetting.GetViewSetting(viewName);
            if (settingObj == null)
            {
                return;
            }

            var viewType = settingObj.ViewType;
            var viewObj = Activator.CreateInstance(viewType) as ViewPresenter;
            _viewDict[viewName] = viewObj;
            viewObj.Open(viewParams);
        }

        public void Close(string viewName)
        {
            ViewPresenter viewPresenter;
            if (_viewDict.TryGetValue(viewName, out viewPresenter))
            {
                _viewDict.Remove(viewName);
                viewPresenter.Close();
            }
        }
    }
}