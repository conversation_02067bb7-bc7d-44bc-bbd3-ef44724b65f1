using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEngine;
using Newtonsoft.Json;
namespace DataCenter
{
    using SessionDataType = Dictionary<Type, DataBase>;
    using GlobeDataType = Dictionary<Type, DataBase>;
    /// <summary>
    /// 游戏配置数据 - 完全由DataProcessingData托管
    /// </summary>
    [Serializable]
    class GameCfgData : DataBase, IGlobeData
    {
        [SerializeField]
        public List<int> SessionIds = new();
    }

    [Serializable]
    public class SessionCfgData : DataBase, ISubData
    {

        /// <summary>
        /// 开始此局游戏的时间戳 
        /// </summary>
        [SerializeField]
        private long _createTimeStamp = DateTime.UtcNow.Ticks;
        public long CreateTimeStamp
        {
            get
            {
                return _createTimeStamp;
            }
        }

        public override void Init()
        {
            _createTimeStamp = DateTime.UtcNow.Ticks;
        }
    }
    /// <summary>
    /// 数据处理中心 - 进行数据处理等操作
    /// </summary>
    class DataProcessingCenter
    {
        private string AutoSavePath;
        public string GlobeSavePath;
        /// <summary>
        /// 全局数据缓存
        /// </summary>
        private GlobeDataType _cacheGlobeDatas = new();

        /// <summary>
        /// 局内数据缓存
        /// </summary>
        private Dictionary<int, SessionDataType> _cacheSessionDatas = new();

        private GameCfgData _gameCfgData;

        public string persistentDataPath;

        public DataProcessingCenter()
        {


        }
        public void Init()
        {
            GlobeSavePath = $"{Application.persistentDataPath}/SaveDatas/GlobeGameData/";
            persistentDataPath = Application.persistentDataPath;
            _gameCfgData = GetGlobeData<GameCfgData>();
            AutoSavePath = GetPathById(DataDefine.AutoSaveId);
        }


        /// <summary>
        /// 开始新游戏并加载数据
        /// </summary>
        public void StartNewGameAndLoadData()
        {
            DeleteAutoSaveData();
            //强制保存一下创建时间
            GetSessionDataById<SessionCfgData>(DataDefine.AutoSaveId).Save();
            SaveGlobeData();
        }

        /// <summary>
        /// 继续游戏并且加载游戏数据
        /// </summary>
        /// <param name="index">存档插槽id - 默认为AutoSaveId</param>
        public void ContinueGameAndLoadData(int index)
        {
            CreateAutoSaveDataFromSavedFile(index);
            //进入游戏时保存下全局数据
            SaveGlobeData();
        }



        private string GetPathById(int index)
        {
            return $"{persistentDataPath}/SaveDatas/GameSessionData_{index}/";
        }

        #region 局内数据保存与删除
        /// <summary>
        /// 创建新的局内数据存档
        /// </summary>
        /// <param name="index"></param>
        public void CreateNewGameSessionSaveData(int index)
        {
            SaveAutoData();
            //将缓存数据copy到对应存档路径
            FileTool.CopyDirectory(AutoSavePath, GetPathById(index), $"_{DataDefine.AutoSaveId}", $"_{index}");
            if (_cacheSessionDatas.ContainsKey(index))
            {
                _cacheSessionDatas[index].Clear();
            }
            _gameCfgData.SessionIds.Add(index);
            _gameCfgData.Save();
        }

        public void DeleteSessionDataById(int index)
        {
            if (_gameCfgData.SessionIds.Contains(index))
            {
                FileTool.DeleteAllFilesInFolder(GetPathById(index));
                if (_cacheSessionDatas.ContainsKey(index))
                {
                    _cacheSessionDatas[index].Clear();
                }
                _gameCfgData.SessionIds.Remove(index);
                _gameCfgData.Save();
            }
            else
            {
                Debug.LogError($"错误地删除局内数据 index:{index}");
            }
        }

        public void DeleteAllSessionData()
        {
            //删除存档数据
            int[] indexs = _gameCfgData.SessionIds.ToArray();

            foreach (int i in indexs)
            {
                FileTool.DeleteAllFilesInFolder(GetPathById(i));
                if (_cacheSessionDatas.ContainsKey(i))
                {
                    _cacheSessionDatas[i].Clear();
                }
            }

            _gameCfgData.SessionIds.Clear();
            _gameCfgData.Save();

            //删除自动存档数据
            DeleteAutoSaveData();
        }

        #endregion


        #region autoSave相关
        /// <summary>
        /// 从存档中读取数据创建自动缓存数据
        /// </summary>
        /// <param name="index"></param>
        private void CreateAutoSaveDataFromSavedFile(int index)
        {

            if (_gameCfgData.SessionIds.Contains(index))
            {
                DeleteAutoSaveData();
                //将对应存档数据copy到缓存
                FileTool.CopyDirectory(GetPathById(index), AutoSavePath, $"_{index}", $"_{DataDefine.AutoSaveId}");
            }

        }

        private void DeleteAutoSaveData()
        {
            //删除自动存储文件夹的数据
            FileTool.DeleteAllFilesInFolder(AutoSavePath);

            //删除临时缓存
            if (_cacheSessionDatas.ContainsKey(DataDefine.AutoSaveId))
            {
                _cacheSessionDatas[DataDefine.AutoSaveId].Clear();
            }

        }

        public void SaveAllData()
        {
            SaveAutoData();
            SaveGlobeData();
        }

        public void SaveAutoData()
        {
            if (_cacheSessionDatas.ContainsKey(DataDefine.AutoSaveId))
            {
                SessionDataType autoSaveData = _cacheSessionDatas[DataDefine.AutoSaveId];
                //先保存缓存数据
                foreach (DataBase data in autoSaveData.Values)
                {
                    data.Save();
                }
            }
        }

        public void SaveGlobeData()
        {
            foreach (DataBase data in _cacheGlobeDatas.Values)
            {
                data.Save();
            }
        }

        public void DeleteGlobeData()
        {
            FileTool.DeleteAllFilesInFolder(GlobeSavePath);
            _cacheGlobeDatas.Clear();
        }

        #endregion

        private FieldInfo _loadDataPathField = typeof(DataBase).GetField("_loadDataPath", BindingFlags.NonPublic | BindingFlags.Instance);
        private FieldInfo _sessionIdField = typeof(DataBase).GetField("_sessionId", BindingFlags.NonPublic | BindingFlags.Instance);
        /// <summary>
        /// 从磁盘读取数据,若没有则会自动创建数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="path"></param>
        /// <returns></returns>
        T GetDataFromDisk<T>(string path, string directoryPath) where T : DataBase
        {
            T data;
            if (File.Exists(path))
            {
                string jsonData = File.ReadAllText(path);
                data = JsonConvert.DeserializeObject<T>(jsonData);
                _loadDataPathField.SetValue(data, directoryPath);
                data.OnLoad();
            }
            else
            {
                data = (T)Activator.CreateInstance(typeof(T));
                _loadDataPathField.SetValue(data, directoryPath);
                data.Init();
                data.OnLoad();
            }
            return data;
        }

        T GetSessionDataFromDisk<T>(string prefix, int sessionId) where T : DataBase
        {
            string path = $"{prefix}{typeof(T)}_{sessionId}.json";
            T data = GetDataFromDisk<T>(path, prefix);
            _sessionIdField.SetValue(data, sessionId);
            return data;
        }

        T GetGlobeDataFromDisk<T>(string prefix) where T : DataBase
        {
            string path = $"{prefix}{typeof(T)}.json";
            return GetDataFromDisk<T>(path, prefix);
        }



        /// <summary>
        /// 获取某个数据类型的所有局内数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public List<T> GetAllSessionData<T>() where T : DataBase
        {
            List<T> datas = new();
            //添加自动存档数据
            T data = GetSessionDataById<T>(DataDefine.AutoSaveId);
            if (data != default)
            {
                datas.Add(data);
            }

            foreach (int index in _gameCfgData.SessionIds)
            {
                data = GetSessionDataById<T>(index);
                if (data != default)
                {
                    datas.Add(data);
                }
            }
            return datas;
        }

        /// <summary>
        /// 根据sessionId,访问局内数据
        /// </summary>
        /// <typeparam name="T">局内数据类型</typeparam>
        /// <param name="sessionId"></param>
        public T GetSessionDataById<T>(int sessionId) where T : DataBase
        {
            Type dataType = typeof(T);
            if (sessionId != DataDefine.AutoSaveId && !_gameCfgData.SessionIds.Contains(sessionId))
            {
                return null;
            }

            SessionDataType sessionData;
            T data;
            if (_cacheSessionDatas.ContainsKey(sessionId))
            {
                sessionData = _cacheSessionDatas[sessionId];
            }
            else
            {
                sessionData = new();
                _cacheSessionDatas.Add(sessionId, sessionData);
            }

            if (sessionData.ContainsKey(dataType))
            {
                data = sessionData[dataType] as T;
            }
            else
            {
                data = GetSessionDataFromDisk<T>(GetPathById(sessionId), sessionId);
                sessionData.Add(dataType, data);
            }
            //被访问了就默认它被修改
            data.IsChanged = true;
            return data;
        }

        public T GetGlobeData<T>() where T : DataBase
        {
            Type dataType = typeof(T);
            T data;
            if (_cacheGlobeDatas.ContainsKey(dataType))
            {
                data = _cacheGlobeDatas[dataType] as T;
            }
            else
            {
                data = GetGlobeDataFromDisk<T>(GlobeSavePath);
                _cacheGlobeDatas.Add(dataType, data);
            }
            //被访问了就默认它被修改
            data.IsChanged = true;
            return data;
        }

        //#endregion


    }
}
