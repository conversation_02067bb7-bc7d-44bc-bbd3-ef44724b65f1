using System;
using System.Collections.Generic;
using UnityEngine;
using UnityCommunity.UnitySingleton;
using DataCenter;
using Common;

namespace GameUtils
{
    /// <summary>
    /// 按键绑定管理器 - 负责管理按键配置的加载、保存和修改
    /// </summary>
    public class KeyBindingManager : PersistentMonoSingleton<KeyBindingManager>
    {
        private KeyBindingData _keyBindingData;
        
        /// <summary>
        /// 按键绑定数据
        /// </summary>
        public KeyBindingData KeyBindingData
        {
            get
            {
                if (_keyBindingData == null)
                {
                    LoadKeyBindings();
                }
                return _keyBindingData;
            }
        }

        protected override void OnInitialized()
        {
            LoadKeyBindings();
        }

        public override void ClearSingleton()
        {
            SaveKeyBindings();
            _keyBindingData = null;
        }

        /// <summary>
        /// 加载按键绑定配置
        /// </summary>
        private void LoadKeyBindings()
        {
            try
            {
                _keyBindingData = DataService.DataService.Ins().Get<KeyBindingData>();
                Debug.Log("按键配置加载成功");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载按键配置失败: {e.Message}");
                // 如果加载失败，创建默认配置
                _keyBindingData = new KeyBindingData();
                _keyBindingData.Init();
            }
        }

        /// <summary>
        /// 保存按键绑定配置
        /// </summary>
        public void SaveKeyBindings()
        {
            if (_keyBindingData != null)
            {
                _keyBindingData.Save();
                Debug.Log("按键配置保存成功");
            }
        }

        /// <summary>
        /// 获取指定动作的按键绑定
        /// </summary>
        public InputActionData GetKeyBinding(InputActionType actionType)
        {
            return KeyBindingData.GetKeyBinding(actionType);
        }

        /// <summary>
        /// 设置按键绑定
        /// </summary>
        public bool SetKeyBinding(InputActionType actionType, KeyCode newKey, bool isPrimary = true)
        {
            bool success = KeyBindingData.SetKeyBinding(actionType, newKey, isPrimary);
            if (success)
            {
                // 触发按键绑定改变事件
                EventDispatcher.GameEvent.DispatchEvent(EventName.KeyBindingChanged, actionType, newKey);
                Debug.Log($"按键绑定已更改: {actionType} -> {newKey}");
            }
            return success;
        }

        /// <summary>
        /// 检查按键冲突
        /// </summary>
        public bool IsKeyConflict(KeyCode keyCode, InputActionType excludeAction = InputActionType.None)
        {
            return KeyBindingData.IsKeyConflict(keyCode, excludeAction);
        }

        /// <summary>
        /// 根据按键获取对应的动作类型
        /// </summary>
        public InputActionType GetActionByKey(KeyCode keyCode)
        {
            return KeyBindingData.GetActionByKey(keyCode);
        }

        /// <summary>
        /// 重置为默认按键配置
        /// </summary>
        public void ResetToDefault()
        {
            KeyBindingData.ResetToDefault();
            SaveKeyBindings();
            
            // 触发按键绑定改变事件
            EventDispatcher.GameEvent.DispatchEvent(EventName.KeyBindingChanged, InputActionType.None, KeyCode.None);
            Debug.Log("按键配置已重置为默认");
        }

        /// <summary>
        /// 获取所有可重新绑定的动作
        /// </summary>
        public List<InputActionData> GetRebindableActions()
        {
            return KeyBindingData.GetRebindableActions();
        }

        /// <summary>
        /// 清除指定动作的次要按键
        /// </summary>
        public void ClearSecondaryKey(InputActionType actionType)
        {
            KeyBindingData.ClearSecondaryKey(actionType);
            
            // 触发按键绑定改变事件
            EventDispatcher.GameEvent.DispatchEvent(EventName.KeyBindingChanged, actionType, KeyCode.None);
        }

        /// <summary>
        /// 获取按键的显示名称
        /// </summary>
        public string GetKeyDisplayName(KeyCode keyCode)
        {
            var tempAction = new InputActionData();
            return tempAction.GetPrimaryKeyDisplayName();
        }

        /// <summary>
        /// 检查指定动作是否可以重新绑定
        /// </summary>
        public bool CanRebind(InputActionType actionType)
        {
            var binding = GetKeyBinding(actionType);
            return binding?.canBeRebound ?? false;
        }

        /// <summary>
        /// 获取动作的显示名称
        /// </summary>
        public string GetActionDisplayName(InputActionType actionType)
        {
            var binding = GetKeyBinding(actionType);
            return binding?.displayName ?? actionType.ToString();
        }

        /// <summary>
        /// 获取动作的描述
        /// </summary>
        public string GetActionDescription(InputActionType actionType)
        {
            var binding = GetKeyBinding(actionType);
            return binding?.description ?? "";
        }

        /// <summary>
        /// 应用程序退出时自动保存
        /// </summary>
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                SaveKeyBindings();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                SaveKeyBindings();
            }
        }
    }
}
