﻿namespace Common
{
    public static class EventName
    {
        public const string EnterEditMode = nameof(EnterEditMode);
        public const string ExitEditMode = nameof(ExitEditMode);
        public const string OnDayChanged = nameof(OnDayChanged);
        public const string InputKeyDown = nameof(InputKeyDown);
        public const string InputKeyUp = nameof(InputKeyUp);
        public const string InputActionTriggered = nameof(InputActionTriggered);
        public const string KeyBindingChanged = nameof(KeyBindingChanged);

    }
}