using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;

namespace AttributeSystem
{
    /// <summary>
    /// 每条属性记录存储数据
    /// </summary>
    [System.Serializable]
    public class AttributeEntryRecord
    {
        /// <summary>
        /// 属性值
        /// </summary>
        public float value;
    }

    /// <summary>
    /// 属性记录存储数据
    /// </summary>
    [System.Serializable]
    public class AttributeRecord
    {
        [JsonProperty]
        private Dictionary<AttrType, AttributeEntryRecord> _records = new();

        public AttributeEntryRecord GetEntry(AttrType type)
        {
            if (_records.ContainsKey(type))
            {
                return _records[type];
            }
            return null;
        }

        public AttributeEntryRecord CreateEntry(AttrType type, int value)
        {
            if (_records.ContainsKey(type))
            {
                Debug.LogError($"AttributeRecord CreateEntry Error: {type} already exist");
                return _records[type];
            }
            _records.Add(type, new AttributeEntryRecord());
            _records[type].value = value;
            return _records[type];
        }

        public void ForceModifyEntryValue(AttrType type, float value)
        {
            if (!_records.ContainsKey(type))
            {
                _records[type] = new AttributeEntryRecord();
            }
            _records[type].value = value;
        }
    }

    [System.Serializable]
    public class AttributeSaveData : DataBase, ISubData
    {
        [JsonProperty]
        private Dictionary<int, AttributeRecord> _records = new();

        /// <summary>
        /// 添加属性记录
        /// </summary>
        /// <param name="entityId">实体id</param>
        /// <param name="attrType">属性类型</param>
        /// <param name="value">值</param>
        public void ModifyEntry(int entityId, AttrType attrType, float value)
        {
            if (!_records.ContainsKey(entityId))
            {
                _records[entityId] = new AttributeRecord();
            }
            _records[entityId].ForceModifyEntryValue(attrType, value);
        }



        public AttributeRecord GetRecord(int entityId)
        {
            if (_records.ContainsKey(entityId))
            {
                return _records[entityId];
            }
            return null;
        }

        public AttributeRecord CreateAndGetRecord(int entityId)
        {
            AttributeRecord record = GetRecord(entityId);
            if (record == null)
            {
                record = new AttributeRecord();
                _records.Add(entityId, record);
            }
            return record;
        }
    }
}