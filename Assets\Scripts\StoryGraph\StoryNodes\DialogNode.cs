﻿using System.Collections.Generic;
using Story;
using StoryCommon;
using XNode;

namespace StoryEditor
{
    public class DialogNode : BaseNode
    {
        [Input] public Empty input;
        [Output] public Empty output;

        public DialogInfo dialogInfo;
        protected override void OnStart()
        {
            base.OnStart();
            var storyGraph = graph as StoryGraph;
            storyGraph.curDialogNode = this;
            StoryManager.Instance.ShowDialog(dialogInfo, () =>
            {
                var storyGraph = graph as StoryGraph;
                if (storyGraph != null) storyGraph.FinishNode(this, "output");
            });
        }

        protected override void OnFinish()
        {
            base.OnFinish();
            StoryManager.Instance.SkipDialogToComplete();
        }
    }

}