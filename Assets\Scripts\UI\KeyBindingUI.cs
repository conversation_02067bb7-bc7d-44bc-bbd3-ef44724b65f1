using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using GameUtils;
using Common;

namespace UI
{
    /// <summary>
    /// 按键配置UI界面
    /// </summary>
    public class KeyBindingUI : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Transform contentParent;
        [SerializeField] private GameObject keyBindingItemPrefab;
        [SerializeField] private Button resetButton;
        [SerializeField] private Button saveButton;
        [SerializeField] private Button cancelButton;

        [Header("等待按键输入UI")]
        [SerializeField] private GameObject waitingForKeyPanel;
        [SerializeField] private TextMeshProUGUI waitingText;

        private List<KeyBindingItem> _keyBindingItems = new();
        private KeyBindingManager _keyBindingManager;
        private InputActionType _currentRebindingAction = InputActionType.None;
        private bool _isRebindingPrimary = true;

        private void Start()
        {
            InitializeUI();
        }

        private void InitializeUI()
        {
            _keyBindingManager = KeyBindingManager.Instance;
            
            // 绑定按钮事件
            resetButton?.onClick.AddListener(ResetToDefault);
            saveButton?.onClick.AddListener(SaveAndClose);
            cancelButton?.onClick.AddListener(CancelAndClose);

            // 创建按键绑定项
            CreateKeyBindingItems();

            // 隐藏等待按键面板
            waitingForKeyPanel?.SetActive(false);
        }

        /// <summary>
        /// 创建按键绑定项
        /// </summary>
        private void CreateKeyBindingItems()
        {
            if (_keyBindingManager == null || contentParent == null || keyBindingItemPrefab == null)
                return;

            // 清除现有项
            foreach (var item in _keyBindingItems)
            {
                if (item != null && item.gameObject != null)
                    DestroyImmediate(item.gameObject);
            }
            _keyBindingItems.Clear();

            // 获取所有可重新绑定的动作
            var rebindableActions = _keyBindingManager.GetRebindableActions();

            foreach (var actionData in rebindableActions)
            {
                GameObject itemObj = Instantiate(keyBindingItemPrefab, contentParent);
                KeyBindingItem item = itemObj.GetComponent<KeyBindingItem>();
                
                if (item != null)
                {
                    item.Initialize(actionData, this);
                    _keyBindingItems.Add(item);
                }
            }
        }

        /// <summary>
        /// 开始重新绑定按键
        /// </summary>
        public void StartRebinding(InputActionType actionType, bool isPrimary)
        {
            _currentRebindingAction = actionType;
            _isRebindingPrimary = isPrimary;

            // 显示等待按键面板
            waitingForKeyPanel?.SetActive(true);
            
            string actionName = _keyBindingManager.GetActionDisplayName(actionType);
            string keyType = isPrimary ? "主要按键" : "次要按键";
            
            if (waitingText != null)
                waitingText.text = $"请为 {actionName} 设置{keyType}\n按任意键或ESC取消";

            // 锁定所有输入
            InputManager.Instance.IsLockAllKey(true);
        }

        /// <summary>
        /// 取消重新绑定
        /// </summary>
        public void CancelRebinding()
        {
            _currentRebindingAction = InputActionType.None;
            waitingForKeyPanel?.SetActive(false);
            
            // 解锁输入
            InputManager.Instance.IsLockAllKey(false);
        }

        private void Update()
        {
            // 如果正在等待按键输入
            if (_currentRebindingAction != InputActionType.None)
            {
                HandleKeyInput();
            }
        }

        /// <summary>
        /// 处理按键输入
        /// </summary>
        private void HandleKeyInput()
        {
            // 检查ESC取消
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                CancelRebinding();
                return;
            }

            // 检查其他按键
            foreach (KeyCode keyCode in System.Enum.GetValues(typeof(KeyCode)))
            {
                if (Input.GetKeyDown(keyCode) && keyCode != KeyCode.None)
                {
                    // 尝试设置按键绑定
                    bool success = _keyBindingManager.SetKeyBinding(_currentRebindingAction, keyCode, _isRebindingPrimary);
                    
                    if (success)
                    {
                        // 更新UI显示
                        RefreshKeyBindingItems();
                        CancelRebinding();
                    }
                    else
                    {
                        // 显示冲突提示
                        Debug.LogWarning($"按键 {keyCode} 已被其他动作使用");
                    }
                    break;
                }
            }
        }

        /// <summary>
        /// 刷新按键绑定项显示
        /// </summary>
        private void RefreshKeyBindingItems()
        {
            foreach (var item in _keyBindingItems)
            {
                item?.RefreshDisplay();
            }
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        private void ResetToDefault()
        {
            _keyBindingManager.ResetToDefault();
            RefreshKeyBindingItems();
        }

        /// <summary>
        /// 保存并关闭
        /// </summary>
        private void SaveAndClose()
        {
            _keyBindingManager.SaveKeyBindings();
            gameObject.SetActive(false);
        }

        /// <summary>
        /// 取消并关闭
        /// </summary>
        private void CancelAndClose()
        {
            // 重新加载配置（撤销未保存的更改）
            // 这里可以实现更复杂的撤销逻辑
            gameObject.SetActive(false);
        }

        /// <summary>
        /// 清除指定动作的次要按键
        /// </summary>
        public void ClearSecondaryKey(InputActionType actionType)
        {
            _keyBindingManager.ClearSecondaryKey(actionType);
            RefreshKeyBindingItems();
        }

        private void OnDestroy()
        {
            // 确保解锁输入
            if (InputManager.Instance != null)
            {
                InputManager.Instance.IsLockAllKey(false);
            }
        }
    }
}
