using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using UnityEngine.Events;

namespace Common
{

    public class ResourceMgr : Singleton<ResourceMgr>
    {
        public T LoadResource<T>(string resPath) where T : Object
        {
            return Resources.Load<T>(resPath);
        }



        /// <summary>
        /// 异步加载资源且会初始化到场景中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="path"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public Coroutine LoadAsync<T>(string path, UnityAction<T> callback) where T : Object
        {
            //开启异步加载的协程
            return MonoMgr.Instance.StartCoroutine(ReallyLoadAsync(path, callback));
        }

        /// <summary>
        /// 异步加载资源且不初始化到场景中
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="path"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public Coroutine LoadAsyncWithoutInstantiate<T>(string path, UnityAction<T> callback) where T : Object
        {
            //开启异步加载的协程
            return MonoMgr.Instance.StartCoroutine(ReallyLoadAsync(path, callback, false));
        }



        //真正的协同程序函数 用于开启异步加载资源
        private IEnumerator ReallyLoadAsync<T>(string path, UnityAction<T> callback, bool willInstantiate = true) where T : Object
        {
            ResourceRequest r = Resources.LoadAsync<T>(path);
            yield return r;
            if (!willInstantiate || !(r.asset is GameObject))
            {
                callback(r.asset as T);
            }
            else
            {
                callback(GameObject.Instantiate(r.asset) as T);
            }
        }
    }

}