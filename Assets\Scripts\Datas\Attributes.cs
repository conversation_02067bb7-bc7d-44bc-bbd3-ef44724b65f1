//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;

public partial class Attributes : ScriptableObject {

	public Func<string, string> Translate;

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private AttributeConfig[] _AttributeConfigItems;

	public AttributeConfig GetAttributeConfig(string attributeKey) {
		int min = 0;
		int max = _AttributeConfigItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			AttributeConfig item = _AttributeConfigItems[index];
			if (item.attributeKey == attributeKey) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(attributeKey, item.attributeKey) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	[SerializeField]
	private AttributeConfig_nameTranslate[] _AttributeConfig_nameTranslateItems;

	public AttributeConfig_nameTranslate GetAttributeConfig_nameTranslate(string key) {
		int min = 0;
		int max = _AttributeConfig_nameTranslateItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			AttributeConfig_nameTranslate item = _AttributeConfig_nameTranslateItems[index];
			if (item.key == key) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(key, item.key) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		string Translate(string key);
		AttributeConfig GetAttributeConfig(string attributeKey);
		AttributeConfig_nameTranslate GetAttributeConfig_nameTranslate(string key);
	}

	private class DataGetter : IDataGetter {
		private Func<string, string> _Translate;
		public string Translate(string key) {
			return _Translate == null ? key : _Translate(key);
		}
		private Func<string, AttributeConfig> _GetAttributeConfig;
		public AttributeConfig GetAttributeConfig(string attributeKey) {
			return _GetAttributeConfig(attributeKey);
		}
		private Func<string, AttributeConfig_nameTranslate> _GetAttributeConfig_nameTranslate;
		public AttributeConfig_nameTranslate GetAttributeConfig_nameTranslate(string key) {
			return _GetAttributeConfig_nameTranslate(key);
		}
		public DataGetter(Func<string, string> translate, Func<string, AttributeConfig> getAttributeConfig, Func<string, AttributeConfig_nameTranslate> getAttributeConfig_nameTranslate) {
			_Translate = translate;
			_GetAttributeConfig = getAttributeConfig;
			_GetAttributeConfig_nameTranslate = getAttributeConfig_nameTranslate;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(Translate, GetAttributeConfig, GetAttributeConfig_nameTranslate);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class AttributeConfig {

	[SerializeField]
	private string _AttributeKey;
	public string attributeKey { get { return _AttributeKey; } }

	[SerializeField]
	private string _NameDes;
	public string nameDes { get { return _NameDes; } }

	[SerializeField]
	private string _Name;
	private string _Name_;
	public string name { get { if (_Name_ == null) { _Name_ = mGetter.Translate(_Name); } return _Name_; } }

	[SerializeField]
	private int _BaseValue;
	public int baseValue { get { return _BaseValue; } }

	[SerializeField]
	private bool _Upgradable;
	public bool upgradable { get { return _Upgradable; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private Attributes.IDataGetter mGetter;

	public AttributeConfig Init(int version, Attributes.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		_Name_ = null;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[AttributeConfig]{{attributeKey:{0}, nameDes:{1}, name:{2}, baseValue:{3}, upgradable:{4}}}",
			attributeKey, nameDes, name, baseValue, upgradable);
	}

}

[Serializable]
public class AttributeConfig_nameTranslate {

	[SerializeField]
	private string _Key;
	public string key { get { return _Key; } }

	[SerializeField]
	private string _Chinese;
	public string Chinese { get { return _Chinese; } }

	[SerializeField]
	private string _English;
	public string English { get { return _English; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private Attributes.IDataGetter mGetter;

	public AttributeConfig_nameTranslate Init(int version, Attributes.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[AttributeConfig_nameTranslate]{{key:{0}, Chinese:{1}, English:{2}}}",
			key, Chinese, English);
	}

}

