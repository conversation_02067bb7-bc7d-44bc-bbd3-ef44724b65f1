﻿using System.Collections.Generic;
using UnityEngine;

namespace UI
{
    public abstract class ViewComponent
    {
        public virtual void OnInit()
        {
        }

        public virtual void OnEnter()
        {
        }

        public virtual void OnExit()
        {
            
        }

        public virtual void OnDestroy()
        {
            
        }
    }
    
    public abstract class ViewPresenter
    {
        private List<ViewComponent> _viewComponents;
        private GameObject _mainGo;
        public virtual List<string> GetDependenceResources()
        {
            return new List<string>();
        }

        public void Open(params object[] viewParams)
        {
            var resList = GetDependenceResources();
            
        }

        public void Close()
        {
            
        }
        
        private void OnInit()
        {
        }

        private void OnEnter()
        {
        }

        private void OnExit()
        {
            
        }

        private void OnDestroy()
        {
            
        }

    }
}