using System;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;


// public class { ClassName_Child } : { ClassName_Parent}
// {

// }


public class ClassName : MonoBehaviour
{

    private UnityAction<UIBehaviour> action;
    void Awake()
    {

        Text text = gameObject.AddComponent<Text>();

        Button btn = gameObject.AddComponent<Button>();
        btn.onClick.AddListener(() =>
        {
            EventDispatcher.GameEvent.DispatchEvent("UIBtnOnClick", btn.name, GetType().ToString());
            Debug.Log($"_______UIBtnOnClick,name:{btn.name} typeName:{GetType()}");
        });
        // 初始化节点
        // InitLanguage(text);
    }

    #region 初始化多语言函数重载
    // void InitLanguage(Text text)
    // {
    //     LanguageUtil.Ins().BindLanUI(text);
    //     LanguageUtil.Ins().ModifyUILan(text);
    // }

    // void InitLanguage(TextMeshPro text)
    // {
    //     LanguageUtil.Ins().BindLanUI(text);
    //     LanguageUtil.Ins().ModifyUILan(text);
    // }

    // void InitLanguage(TextMeshProUGUI text)
    // {
    //     LanguageUtil.Ins().BindLanUI(text);
    //     LanguageUtil.Ins().ModifyUILan(text);
    // }
    #endregion

}