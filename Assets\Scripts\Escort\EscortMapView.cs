﻿using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Escort
{
    public class EscortTransportItem : MonoBehaviour
    {
        private int _escortOrderID;

        private GameObject _tipsPnl;
        private TMP_Text _tipsTxt;
        private GameObject _goPnl;
        
        private GameObject _backPnl;

        private void Awake()
        {
            var hoverHandler = this.transform.Find("touch_pnl").AddComponent<MouseHoverHandler>();
            _tipsPnl = gameObject.FindChildRecursively("tips_pnl");
            _goPnl = gameObject.FindChildRecursively("go_pnl");
            _backPnl = gameObject.FindChildRecursively("back_pnl");
            _tipsTxt = gameObject.FindChildRecursively("city_text").GetComponent<TMP_Text>();
            _tipsPnl.SetActive(false);
            
            hoverHandler.OnMouseEnterEvent += OnPointerEnter;
            hoverHandler.OnMouseExitEvent += OnPointerExit;
        }

        private void OnPointerEnter(PointerEventData eventData)
        {
            _tipsPnl.SetActive(true);
        }

        private void OnPointerExit(PointerEventData eventData)
        {
            _tipsPnl.SetActive(false);
        }

        public void SetOrderID(int orderID)
        {
            _escortOrderID = orderID;
            RefreshOrderInfo();
        }

        private void RefreshOrderInfo()
        {
            
        }
    }

    public class EscortMapView : MonoBehaviour
    {
        public EscortScrollZoom escortScrollZoom;

        public RectTransform mapItemContainer;
        public RectTransform mapHighlightContainer;
        public RectTransform mapRoadItemContainer;

        public GameObject transportItemPrefab;
        public GameObject transportItemContainer;

        public GameObject detailGo;

        public Button closeDetailBtn;
        public Button closeBtn;

        private Dictionary<int, EscortCityItem> _allCityItemDict = new Dictionary<int, EscortCityItem>();

        private Dictionary<Tuple<int, int>, EscortRoadItem> _allCityRoadDict =
            new Dictionary<Tuple<int, int>, EscortRoadItem>();


        private void Awake()
        {
            foreach (Button button in mapItemContainer.GetComponentsInChildren<Button>())
            {
                button.onClick.AddListener(() =>
                    OnClickCity(button.GetComponentInParent<EscortCityItem>()));
            }

            closeDetailBtn.onClick.AddListener(CloseDetailPanel);
            closeBtn.onClick.AddListener(() => gameObject.SetActive(false));
            InitRoadItemInfo();
            RefreshTransportItem();
        }

        private void InitRoadItemInfo()
        {
            _allCityItemDict = GetComponentsInChildren<EscortCityItem>().ToDictionary(
                item => item.CityID, item => item);

            var allRoadList =
                GetComponentsInChildren<EscortRoadItem>().ToList().Where(item => !item.IsWeak); //todo 先不考虑水运
            foreach (var escortRoadItem in allRoadList)
            {
                escortRoadItem.SetMapViewContext(this);
                var pathDict = escortRoadItem.GetAllCityRoadDict();
                foreach (KeyValuePair<Tuple<int, int>, List<Transform>> tmpPath in pathDict)
                {
                    if (!_allCityRoadDict.TryAdd(tmpPath.Key, escortRoadItem))
                    {
                        Debug.Log("has error");
                    }
                }
            }
        }

        public EscortCityItem GetCityItem(int cityID)
        {
            _allCityItemDict.TryGetValue(cityID, out var resultItem);
            return resultItem;
        }

        private void OnClickCity(EscortCityItem cityItem)
        {
            escortScrollZoom.ScrollToItem(cityItem.transform as RectTransform, OnScrollToItem);
        }

        private void SetSelectOrderRoadHighlightEnable(bool flag)
        {
            mapHighlightContainer.gameObject.SetActive(flag);
            List<int> roadCityIDs = new List<int>() { 1, 2, 13 };
            for (int index = 0; index < roadCityIDs.Count - 1; index++)
            {
                var curCityID = roadCityIDs[index];
                var nextCityID = roadCityIDs[index + 1];
                var roadItem = _allCityRoadDict[Tuple.Create(curCityID, nextCityID)];
                // var originWorldPos = roadItem.transform.position;
                var targetTransform = flag ? mapHighlightContainer : mapRoadItemContainer;
                roadItem.transform.SetParent(targetTransform, true);
            }
        }

        private void OnScrollToItem()
        {
            SetSelectOrderRoadHighlightEnable(true);
            detailGo.SetActive(true);
        }

        private void CloseDetailPanel()
        {
            SetSelectOrderRoadHighlightEnable(false);
            detailGo.SetActive(false);
        }

        private void RefreshTransportItem()
        {
            List<int> roadCityIDs = new List<int>() { 1, 2, 13 };
            List<Transform> pathPoints = new List<Transform>();
            for (int index = 0; index < roadCityIDs.Count - 1; index++)
            {
                var curCityID = roadCityIDs[index];
                var nextCityID = roadCityIDs[index + 1];
                var pathKey = Tuple.Create(curCityID, nextCityID);
                var roadItem = _allCityRoadDict[pathKey];
                var pathPointsDict = roadItem.GetAllCityRoadDict();
                var pathList = pathPointsDict[pathKey];
                pathPoints.AddRange(pathList);
            }

            float percent = 1;
            var targetPointIndex = (int)(percent * (pathPoints.Count-1));
            var targetPointTransform = pathPoints[targetPointIndex];

            var transportGo = GameObject.Instantiate(transportItemPrefab, transportItemContainer.transform);
            transportGo.AddComponent<EscortTransportItem>();
            transportGo.transform.position = targetPointTransform.position;
        }
    }
}