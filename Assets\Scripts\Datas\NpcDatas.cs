//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;

public partial class NpcDatas : ScriptableObject {

	public Func<string, string> Translate;

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private NpcData[] _NpcDataItems;

	public NpcData GetNpcData(int npcID) {
		int min = 0;
		int max = _NpcDataItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			NpcData item = _NpcDataItems[index];
			if (item.npcID == npcID) { return item.Init(mVersion, DataGetterObject); }
			if (npcID < item.npcID) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	[SerializeField]
	private NpcData_nameTranslate[] _NpcData_nameTranslateItems;

	public NpcData_nameTranslate GetNpcData_nameTranslate(string key) {
		int min = 0;
		int max = _NpcData_nameTranslateItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			NpcData_nameTranslate item = _NpcData_nameTranslateItems[index];
			if (item.key == key) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(key, item.key) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		string Translate(string key);
		NpcData GetNpcData(int npcID);
		NpcData_nameTranslate GetNpcData_nameTranslate(string key);
	}

	private class DataGetter : IDataGetter {
		private Func<string, string> _Translate;
		public string Translate(string key) {
			return _Translate == null ? key : _Translate(key);
		}
		private Func<int, NpcData> _GetNpcData;
		public NpcData GetNpcData(int npcID) {
			return _GetNpcData(npcID);
		}
		private Func<string, NpcData_nameTranslate> _GetNpcData_nameTranslate;
		public NpcData_nameTranslate GetNpcData_nameTranslate(string key) {
			return _GetNpcData_nameTranslate(key);
		}
		public DataGetter(Func<string, string> translate, Func<int, NpcData> getNpcData, Func<string, NpcData_nameTranslate> getNpcData_nameTranslate) {
			_Translate = translate;
			_GetNpcData = getNpcData;
			_GetNpcData_nameTranslate = getNpcData_nameTranslate;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(Translate, GetNpcData, GetNpcData_nameTranslate);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class NpcData {

	[SerializeField]
	private int _NpcID;
	public int npcID { get { return _NpcID; } }

	[SerializeField]
	private string _NameDes;
	public string nameDes { get { return _NameDes; } }

	[SerializeField]
	private string _Name;
	private string _Name_;
	public string name { get { if (_Name_ == null) { _Name_ = mGetter.Translate(_Name); } return _Name_; } }

	[SerializeField]
	private string _QSpine;
	public string qSpine { get { return _QSpine; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private NpcDatas.IDataGetter mGetter;

	public NpcData Init(int version, NpcDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		_Name_ = null;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[NpcData]{{npcID:{0}, nameDes:{1}, name:{2}, qSpine:{3}}}",
			npcID, nameDes, name, qSpine);
	}

}

[Serializable]
public class NpcData_nameTranslate {

	[SerializeField]
	private string _Key;
	public string key { get { return _Key; } }

	[SerializeField]
	private string _Chinese;
	public string Chinese { get { return _Chinese; } }

	[SerializeField]
	private string _English;
	public string English { get { return _English; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private NpcDatas.IDataGetter mGetter;

	public NpcData_nameTranslate Init(int version, NpcDatas.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[NpcData_nameTranslate]{{key:{0}, Chinese:{1}, English:{2}}}",
			key, Chinese, English);
	}

}

