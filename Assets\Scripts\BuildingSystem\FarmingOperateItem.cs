﻿using System;
using System.Collections.Generic;
using UI;
using UnityEngine;
using UnityEngine.UI;

public class FarmingOperateItem : MonoBehaviour
{
    private Camera _camera;

    private GameObject _followBuilding = null;

    public Vector3 Offset;

    public Button LastArrowBtn;
    public Button NextArrowBtn;
    public Button WateringBtn;
    public Button FertilizeBtn;
    public Button ReceiveBtn;
    public List<Button> ItemGrids;
    public GameObject SeedRootGo;
    public GameObject CropRootGo;

    private FarmingBuilding _farmingBuilding => _followBuilding.GetComponentInParent<FarmingBuilding>();

    private void Awake()
    {
        for (int i = 0; i < ItemGrids.Count; i++)
        {
            var index = i;
            ItemGrids[i].onClick.AddListener((() => OnClickGrid(index)));
        }
        WateringBtn.onClick.AddListener(OnClickWatering);
        FertilizeBtn.onClick.AddListener(OnClickFertilize);
        ReceiveBtn.onClick.AddListener(OnClickReceive);
    }

    private void OnClickReceive()
    {
        _farmingBuilding.DoReceive();
    }

    private void OnClickWatering()
    {
        _farmingBuilding.DoWater();
    }

    private void OnClickFertilize()
    {
        _farmingBuilding.DoFertilize();
    }

    public void ShowSeed()
    {
        this.gameObject.SetActive(true);
        SeedRootGo.SetActive(true);
        CropRootGo.SetActive(false);
    }

    public void ShowCrop()
    {
        this.gameObject.SetActive(true);
        SeedRootGo.SetActive(false);
        CropRootGo.SetActive(true);
        ReceiveBtn.gameObject.SetActive(_farmingBuilding.cropGrowState == FarmingBuilding.GrowStateEnum.Ripe);
        WateringBtn.gameObject.SetActive(_farmingBuilding.cropGrowState != FarmingBuilding.GrowStateEnum.Ripe);
        FertilizeBtn.gameObject.SetActive(_farmingBuilding.cropGrowState != FarmingBuilding.GrowStateEnum.Ripe);
    }

    public void Hide()
    {
        this.gameObject.SetActive(false);
        SeedRootGo.SetActive(false);
        CropRootGo.SetActive(false);
    }

    public void AddFollowGo(GameObject buildingGo)
    {
        _followBuilding = buildingGo;
    }

    private void OnClickGrid(int index)
    {
        if (index == 0)
        {
            _farmingBuilding.DoSeed(1);
        }
    }

    public void SetCamera(Camera pCamera)
    {
        this._camera = pCamera;
    }

    private bool IsEnable
    {
        get => _camera != null && _followBuilding != null;
    }
    // Update is called once per frame
    void Update()
    {
        if(!IsEnable)
            return;
        var uiRoot = UIManager.Instance.GetUIRoot();
        var goScreenPos = _camera.WorldToScreenPoint(_followBuilding.transform.position);
        Vector2 resultPos;
        var isSucc =
            RectTransformUtility.ScreenPointToLocalPointInRectangle(uiRoot.transform as RectTransform, goScreenPos, _camera, out resultPos);
        if(!isSucc)
            return;
        this.gameObject.transform.localPosition = new Vector3(resultPos.x, resultPos.y, 0) + Offset;
    }


   
}