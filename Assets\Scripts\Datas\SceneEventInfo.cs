//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;

public partial class SceneEventInfo : ScriptableObject {

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private SceneEventEntry[] _SceneEventEntryItems;

	public SceneEventEntry GetSceneEventEntry(int id) {
		int min = 0;
		int max = _SceneEventEntryItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			SceneEventEntry item = _SceneEventEntryItems[index];
			if (item.id == id) { return item.Init(mVersion, DataGetterObject); }
			if (id < item.id) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		SceneEventEntry GetSceneEventEntry(int id);
	}

	private class DataGetter : IDataGetter {
		private Func<int, SceneEventEntry> _GetSceneEventEntry;
		public SceneEventEntry GetSceneEventEntry(int id) {
			return _GetSceneEventEntry(id);
		}
		public DataGetter(Func<int, SceneEventEntry> getSceneEventEntry) {
			_GetSceneEventEntry = getSceneEventEntry;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(GetSceneEventEntry);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class SceneEventEntry {

	[SerializeField]
	private int _Id;
	public int id { get { return _Id; } }

	[SerializeField]
	private string _SceneName;
	public string sceneName { get { return _SceneName; } }

	[SerializeField]
	private int[] _Ratio;
	public int[] ratio { get { return _Ratio; } }

	[SerializeField]
	private int[] _EventIds_1;
	public int[] eventIds_1 { get { return _EventIds_1; } }

	[SerializeField]
	private int[] _EventIds_2;
	public int[] eventIds_2 { get { return _EventIds_2; } }

	[SerializeField]
	private int[] _EventIds_3;
	public int[] eventIds_3 { get { return _EventIds_3; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private SceneEventInfo.IDataGetter mGetter;

	public SceneEventEntry Init(int version, SceneEventInfo.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[SceneEventEntry]{{id:{0}, sceneName:{1}, ratio:{2}, eventIds_1:{3}, eventIds_2:{4}, eventIds_3:{5}}}",
			id, sceneName, array2string(ratio), array2string(eventIds_1), array2string(eventIds_2), array2string(eventIds_3));
	}

	private string array2string(Array array) {
		int len = array.Length;
		string[] strs = new string[len];
		for (int i = 0; i < len; i++) {
			strs[i] = string.Format("{0}", array.GetValue(i));
		}
		return string.Concat("[", string.Join(", ", strs), "]");
	}

}

