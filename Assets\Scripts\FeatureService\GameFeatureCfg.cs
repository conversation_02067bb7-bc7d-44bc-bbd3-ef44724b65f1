using System;
using UnityEngine;

///<summary>
/// 游戏功能分支管理 - 废弃
/// </summary>
namespace FeatureService
{
    /// <summary>
    /// 所有可用的功能类型
    /// </summary>
    public enum ParentFeature
    {
        None,
        Story = 1,
        Battle,
        MiniGame,
        Functional
    }

    public enum MiniGame
    {
        None,
        Divination‌ = 1,
        ThrowingHolyCups,
        GamblingDen
    }

    public enum FunctionalFeature
    {
        None,
        Shop = 1,
    }


    [System.Serializable]
    public class FeatureConfig
    {
        public ParentFeature parentFeature;
        public MiniGame miniGame;
        public FunctionalFeature functionalFeature;

    }
}