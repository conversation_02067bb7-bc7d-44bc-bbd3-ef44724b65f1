﻿using System;
using System.Collections.Generic;
using Common;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.TextCore;

public class CameraEdgeScroll : MonoBehaviour
{
    private Camera _camera;
    public RectTransform referenceObject;
    public RectTransform observeWindow;

    public Vector2 distance;

    public float zoomUpperBound = 5.0f;
    public float zoomLowerBound = 2.5f;
    public float zoomSpeed = 0.1f;

    private GlobalTouchTrigger _globalTouchTrigger;
    private Vector3 _startTouchPos = default;
    private Vector3 _startCameraPos = default;
    private bool isDraging = false;

    private void Awake()
    {
        _camera = GetComponent<Camera>();
    }

    public void OnTouchDownEvent(Vector3 mousePos)
    {
        isDraging = true;
        _startTouchPos = mousePos;
        _startCameraPos = this.transform.position;
    }

    public void OnDragEvent(Vector3 mousePos)
    {
        if (!isDraging)
        {
            return;
        }
        var startWorldPos = _camera.ScreenToWorldPoint(_startTouchPos);
        var touchWorldPos = _camera.ScreenToWorldPoint(mousePos);
        var movePos = startWorldPos - touchWorldPos;
        var targetPos = _startCameraPos + movePos;
        transform.position = targetPos;
    }

    public void OnDragEndEvent(Vector3 mousePos)
    {
        isDraging = false;
        _startTouchPos = default;
    }

    private void LateUpdate()
    {
        HandleZoom();
        // HandleEdgeScroll();
        Adjust();
    }

    private void HandleZoom()
    {
        var size = _camera.orthographicSize;
        if (Input.mouseScrollDelta.y < 0 && size < zoomUpperBound)
        {
            var orthographicSize = _camera.orthographicSize;
            orthographicSize += zoomSpeed;
            _camera.orthographicSize =
                orthographicSize > zoomUpperBound ? zoomUpperBound : orthographicSize;
        }

        if (Input.mouseScrollDelta.y > 0 && size > zoomLowerBound)
        {
            var orthographicSize = _camera.orthographicSize;
            orthographicSize -= zoomSpeed;
            _camera.orthographicSize =
                orthographicSize < zoomLowerBound ? zoomLowerBound : orthographicSize;
        }
    }

    private void HandleEdgeScroll()
    {
        Vector2 mousePos = Utils.GetMouseScreenPos();
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;

        if (mousePos.x > screenWidth * 0.9 && mousePos.x < screenWidth)
        {
            transform.Translate(5 * Time.deltaTime * Vector2.right);
        }

        if (mousePos.x < screenWidth * 0.1 && mousePos.x > 0)
        {
            transform.Translate(5 * Time.deltaTime * Vector2.left);
        }

        if (mousePos.y > screenHeight * 0.9 && mousePos.y < screenHeight)
        {
            transform.Translate(5 * Time.deltaTime * Vector2.up);
        }

        if (mousePos.y < screenHeight * 0.1 && mousePos.y > 0)
        {
            transform.Translate(5 * Time.deltaTime * Vector2.down);
        }
    }


    private void Adjust()
    {
        Rect screenRefRect = Utils.GetScreenRectOfWorldUI(referenceObject, _camera);
        Rect screenObsRect = Utils.GetScreenRectOfScreenUI(observeWindow);
        float dx = Mathf.Max(0, screenRefRect.xMin - screenObsRect.xMin) -
                   Mathf.Max(0, screenObsRect.xMax - screenRefRect.xMax);
        float dy = Mathf.Max(0, screenRefRect.yMin - screenObsRect.yMin) -
                   Mathf.Max(0, screenObsRect.yMax - screenRefRect.yMax);
        distance = new Vector2(dx, dy);
        distance = _camera.ScreenToWorldPoint(distance) - _camera.ScreenToWorldPoint(new Vector2(0, 0));
        _camera.transform.Translate(distance);
    }
}