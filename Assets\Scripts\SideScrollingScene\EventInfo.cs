using Cysharp.Threading.Tasks;
using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    enum EventStat
    {
        Upspawn,
        Initializing,
        Exist,
        Destroy
    }

    class EventInfo
    {
        private BattleSceneManager _owner;
        private EventStat _eventStat;
        public EventStat EventState
        {
            get
            {
                return _eventStat;
            }
        }

        EventSpawnPoint _spawnPoint;
        public Vector3 Position
        {
            get
            {
                return _spawnPoint.transform.position;
            }
        }
        private int _eventId;
        public int EventId
        {
            get
            {
                return _eventId;
            }
        }
        IEventObj _eventObj;
        public IEventObj EventObj
        {
            get
            {
                return _eventObj;
            }
        }

        public EventInfo(EventSpawnPoint point, BattleSceneManager owner)
        {
            _spawnPoint = point;
            _owner = owner;
            _eventStat = EventStat.Upspawn;
            _eventObj = null;
        }

        public void InitRandomEvent(int eventId)
        {
            _eventId = eventId;

            EventObjType type;
            if (_spawnPoint.EventObjType != EventObjType.Default)
            {
                type = _spawnPoint.EventObjType;
            }
            else
            {
                type = _owner.GetDefaultEventObjType();
            }

            _eventStat = EventStat.Exist;

            switch (type)
            {
                case EventObjType.Npc:
                    EventObjManager.Instance.CreateEventNpc(eventId, _spawnPoint.NpcId).ContinueWith((obj) =>
                    {
                        InitEventObj(obj);
                    });
                    break;
                case EventObjType.Card:
                    EventObjManager.Instance.CreateEventCard(eventId).ContinueWith((obj) =>
                      {
                          InitEventObj(obj);
                      });
                    break;
                default:
                    break;
            }
        }

        void InitEventObj(IEventObj obj)
        {
            _eventStat = EventStat.Exist;
            _eventObj = obj;
            _eventObj.transform.parent = _spawnPoint.transform.parent;
            _eventObj.transform.position = _spawnPoint.transform.position;
            _eventObj.IsAutoTrigger = false;
            _eventObj.gameObject.SetActive(true);
            Debug.Log($"________InitEventObj:{_eventId}");
        }

        public void DestoryEvent()
        {
            _eventStat = EventStat.Destroy;
            EventObjManager.Instance.DestroyEventObj(_eventObj.gameObject);
            _eventObj = null;
            _owner = null;
            Debug.Log($"________DestroyEventNpc:{_eventId}");
        }

        public bool IsByPlayerLeft(Vector3 pos)
        {
            return pos.x > _spawnPoint.transform.position.x;
        }

        public float Distance(Vector3 pos)
        {
            return Mathf.Abs(pos.x - _spawnPoint.transform.position.x);
        }
    }

}