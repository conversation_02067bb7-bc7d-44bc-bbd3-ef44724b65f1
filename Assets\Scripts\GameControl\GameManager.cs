
using UnityEngine;
using GameUtils;
using Common;

namespace GameControl
{
    /// <summary>
    /// 游戏管理器 - 集成了按键配置系统
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("UI引用")]
        [SerializeField] private GameObject keyBindingUI;
        [SerializeField] private GameObject pauseMenu;

        private bool _isPaused = false;

        private void Start()
        {
            // 注册输入事件监听
            RegisterInputEvents();
        }

        private void OnDestroy()
        {
            // 取消输入事件监听
            UnregisterInputEvents();
        }

        /// <summary>
        /// 注册输入事件监听
        /// </summary>
        private void RegisterInputEvents()
        {
            EventDispatcher.GameEvent.Regist<InputActionType>(EventName.InputActionTriggered, OnInputActionTriggered);
        }

        /// <summary>
        /// 取消输入事件监听
        /// </summary>
        private void UnregisterInputEvents()
        {
            EventDispatcher.GameEvent.UnRegist<InputActionType>(EventName.InputActionTriggered, OnInputActionTriggered);
        }

        /// <summary>
        /// 处理输入动作
        /// </summary>
        private void OnInputActionTriggered(InputActionType actionType)
        {
            switch (actionType)
            {
                case InputActionType.Escape:
                    HandleEscapeAction();
                    break;
                case InputActionType.Menu:
                    HandleMenuAction();
                    break;
                case InputActionType.Pause:
                    HandlePauseAction();
                    break;
                case InputActionType.QuickSave:
                    HandleQuickSave();
                    break;
                case InputActionType.QuickLoad:
                    HandleQuickLoad();
                    break;
                case InputActionType.Screenshot:
                    HandleScreenshot();
                    break;
            }
        }

        /// <summary>
        /// 处理退出动作
        /// </summary>
        private void HandleEscapeAction()
        {
            // 如果按键配置界面打开，则关闭它
            if (keyBindingUI != null && keyBindingUI.activeSelf)
            {
                keyBindingUI.SetActive(false);
                return;
            }

            // 如果暂停菜单打开，则关闭它
            if (pauseMenu != null && pauseMenu.activeSelf)
            {
                pauseMenu.SetActive(false);
                ResumeGame();
                return;
            }

            // 否则打开暂停菜单
            HandlePauseAction();
        }

        /// <summary>
        /// 处理菜单动作
        /// </summary>
        private void HandleMenuAction()
        {
            if (keyBindingUI != null)
            {
                keyBindingUI.SetActive(!keyBindingUI.activeSelf);
            }
        }

        /// <summary>
        /// 处理暂停动作
        /// </summary>
        private void HandlePauseAction()
        {
            if (_isPaused)
            {
                ResumeGame();
            }
            else
            {
                PauseGame();
            }
        }

        /// <summary>
        /// 处理快速保存
        /// </summary>
        private void HandleQuickSave()
        {
            Debug.Log("执行快速保存");
            // 这里可以调用数据服务进行保存
            // DataService.DataService.Ins().SaveAllData();
        }

        /// <summary>
        /// 处理快速加载
        /// </summary>
        private void HandleQuickLoad()
        {
            Debug.Log("执行快速加载");
            // 这里可以调用数据服务进行加载
        }

        /// <summary>
        /// 处理截图
        /// </summary>
        private void HandleScreenshot()
        {
            string fileName = $"Screenshot_{System.DateTime.Now:yyyyMMdd_HHmmss}.png";
            string path = System.IO.Path.Combine(Application.persistentDataPath, fileName);
            ScreenCapture.CaptureScreenshot(path);
            Debug.Log($"截图已保存到: {path}");
        }

        public void GameStart()
        {
            Debug.Log("游戏开始");
            ResumeGame();
        }

        public void GameContinue()
        {
            Debug.Log("继续游戏");
            ResumeGame();
        }

        public void GameEnd()
        {
            Debug.Log("游戏结束");
            PauseGame();
        }

        /// <summary>
        /// 暂停游戏
        /// </summary>
        private void PauseGame()
        {
            _isPaused = true;
            Time.timeScale = 0f;

            if (pauseMenu != null)
            {
                pauseMenu.SetActive(true);
            }

            Debug.Log("游戏已暂停");
        }

        /// <summary>
        /// 恢复游戏
        /// </summary>
        private void ResumeGame()
        {
            _isPaused = false;
            Time.timeScale = 1f;

            if (pauseMenu != null)
            {
                pauseMenu.SetActive(false);
            }

            Debug.Log("游戏已恢复");
        }
    }
}