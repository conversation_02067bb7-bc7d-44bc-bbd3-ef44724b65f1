using System;
using System.Collections;
using System.Collections.Generic;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class Building : MonoBeh<PERSON>our, IPointerClick<PERSON><PERSON>ler, IBeginDrag<PERSON><PERSON><PERSON>,<PERSON>rag<PERSON><PERSON>ler,IEndDragHandler
{
    public BoundsInt BuildingArea;
    private BoundsInt _beforeEditBuildingArea;
    private Vector3? _beforeEditPos = null; 
    public SpriteRenderer BuildingSpriteRenderer;
    public GameObject BuildingOperatePrefab;
    public GameObject SpriteRoot;
    [SerializeField] private GameObject EditBuildingGo;
    [SerializeField] private GameObject RealBuildingGo;
    private Vector3Int _cellPos;
    private BuildingOperateItem _buildingOperateItem;
    private GameObject namebarFollowGo;
    // private readonly Vector3 EditOffset = new Vector3(0, 0.1f, 0);

    public void RotateBuilding()
    {
        var curScale = SpriteRoot.transform.localScale;
        curScale.x = -curScale.x;
        SpriteRoot.transform.localScale = curScale;
        
        var curBuildingArea = BuildingArea;
        var beforeSizeX = curBuildingArea.size.x;
        var beforeSizeY = curBuildingArea.size.y;
        Vector3Int newSize = new Vector3Int(beforeSizeY, beforeSizeX, curBuildingArea.size.z);
        BuildingArea.size = newSize;
    }

    private void Awake()
    {
        namebarFollowGo = new GameObject("nameBarFollowGo")
        {
            transform =
            {
                parent = this.transform,
                localPosition = new Vector3(0, 3.5f, 0)
            }
        };
    }

    private void Start()
    {
        var uiRoot = UIManager.Instance.GetUIRoot();
        var buildingGo = Instantiate<GameObject>(BuildingOperatePrefab, uiRoot.transform);
        var buildingOperate = buildingGo.GetComponent<BuildingOperateItem>();
        _buildingOperateItem = buildingOperate;
        buildingOperate.AddFollowGo(namebarFollowGo);
        buildingOperate.SetCamera(Camera.main);
        buildingOperate.gameObject.SetActive(false);
    }

    public void EnterEditMode()
    {
        _beforeEditBuildingArea = BuildingArea;
        _beforeEditPos = transform.position;
        _buildingOperateItem.gameObject.SetActive(true);
        EditBuildingGo.SetActive(false);
        var spriteRender = RealBuildingGo.GetComponent<SpriteRenderer>();
        spriteRender.color = new Color(1.0f, 1.0f, 1.0f, 0.75f);
    }

    public int SetSpriteOrder(int order)
    {
        var allSpriteComp = this.GetComponentsInChildren<SpriteRenderer>(true);
        foreach (SpriteRenderer spriteRenderer in allSpriteComp)
        {
            order += 1;
            spriteRenderer.sortingOrder = order;
        }

        return order;
    }

    public void ExitEditMode(bool isSave)
    {
        _buildingOperateItem.gameObject.SetActive(false);
        EditBuildingGo.SetActive(false);
        var spriteRender = RealBuildingGo.GetComponent<SpriteRenderer>();
        spriteRender.color = new Color(1.0f, 1.0f, 1.0f, 1.5f);
        if (!isSave)
        {
            if (_beforeEditPos != null)
            {
                BuildingArea = _beforeEditBuildingArea;
                transform.position = _beforeEditPos.Value;
            }
        }
        _beforeEditPos = null;
        
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        var isEditMode = GridBuildingSystem.Instance.IsEditMode;
        if (!isEditMode)
        {
            var farmingComp = GetComponent<FarmingBuilding>();
            farmingComp?.OnClickFraming();
        }
        else
        {
            GridBuildingSystem.Instance.OnClickBuilding(this);
        }
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        GridBuildingSystem.Instance.OnBeginDragBuilding(this, eventData);
    }

    public void OnDrag(PointerEventData eventData)
    {
        GridBuildingSystem.Instance.OnDragBuilding(this, eventData);
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        GridBuildingSystem.Instance.OnDragEndBuilding(this);
    }
}
