using System;
using System.Collections;
using System.Collections.Generic;
using UI;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class BuildingOperateItem : MonoBehaviour
{
    private Camera _camera;

    private GameObject _followBuilding = null;

    public Vector3 Offset;
    
    
    public Button CancelBtn;
    public Button RotateBtn;
    public Button OkBtn;
    public Button AddBtn;

    private void Awake()
    {
        CancelBtn.onClick.AddListener(OnClickCancel);
        RotateBtn.onClick.AddListener(OnClickRotate);
        OkBtn.onClick.AddListener(OnClickOk);
        AddBtn.onClick.AddListener(OnClickAdd);
    }

    private void OnClickAdd()
    {
        throw new NotImplementedException();
    }

    private void OnClickOk()
    {
        GridBuildingSystem.Instance.ExitEditMode(true, true);
    }

    private void OnClickRotate()
    {
        GridBuildingSystem.Instance.RotateCurMovingBuilding();
    }

    private void OnClickCancel()
    {
        GridBuildingSystem.Instance.ExitEditMode(false, true);
    }


    public void AddFollowGo(GameObject buildingGo)
    {
        _followBuilding = buildingGo;
    }

    public void SetCamera(Camera pCamera)
    {
        this._camera = pCamera;
    }

    private bool IsEnable
    {
        get => _camera != null && _followBuilding != null;
    }
    // Update is called once per frame
    void Update()
    {
        if(!IsEnable)
            return;
        var uiRoot = UIManager.Instance.GetUIRoot();
        var goScreenPos = _camera.WorldToScreenPoint(_followBuilding.transform.position);
        Vector2 resultPos;
        var isSucc =
            RectTransformUtility.ScreenPointToLocalPointInRectangle(uiRoot.transform as RectTransform, goScreenPos, _camera, out resultPos);
        if(!isSucc)
            return;
        this.gameObject.transform.localPosition = new Vector3(resultPos.x, resultPos.y, 0) + Offset;
    }
}
