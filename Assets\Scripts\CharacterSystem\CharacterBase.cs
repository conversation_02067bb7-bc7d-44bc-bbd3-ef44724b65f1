using AttributeSystem;
using Common;
using Cysharp.Threading.Tasks;
using Spine.Unity;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.Events;

namespace CharacterSystem
{
    /// <summary>
    /// 角色基础类
    /// </summary>
    public class CharacterBase : MonoBehaviour, IAttributeHolder
    {

        public int CharacterID = -1;
        public int GetAttributeUniqueID()
        {
            return CharacterID;
        }

        public AttributeComp GetAttributeComp()
        {
            return gameObject.GetComponent<AttributeComp>();
        }

        private NpcData _npcData;
        public NpcData NpcData
        {
            get
            {
                return _npcData;
            }
        }


        private SkeletonMecanim _skeletonMecanim;
        private Animator _animator;

        /// <summary>
        /// 初始化角色
        /// </summary>
        public virtual void Initialize(int characterId)
        {
            CharacterID = characterId;

            _npcData = ExcelDataMgr.Instance.GetTable<NpcDatas>().GetNpcData(characterId);

            _animator = gameObject.GetComponent<Animator>();
            string animPath = string.Format(ResourcePathConst.Q_CharacterAnimData, _npcData.qSpine);
            var animControl = ResourceMgr.Instance.LoadResource<RuntimeAnimatorController>(animPath);
            _animator.runtimeAnimatorController = animControl;

            string spinePath = string.Format(ResourcePathConst.Q_CharacterSpineData, _npcData.qSpine);
            var spineData = ResourceMgr.Instance.LoadResource<SkeletonDataAsset>(spinePath);
            Debug.Log($"data:{spineData}");
            _skeletonMecanim = gameObject.GetComponent<SkeletonMecanim>();
            _skeletonMecanim.skeletonDataAsset = spineData;
            _skeletonMecanim.Initialize(true);
            Debug.Log($"_______CreateNpc:{CharacterID}________");
        }

        public void PlayAnimation(string animName)
        {
            _animator.Play(animName);
        }

        public void SetPosition(Vector3 pos)
        {
            gameObject.transform.position = pos;
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

    }
}