{"skeleton": {"hash": "QU0Ihv6KPwg", "spine": "4.2.38", "x": 101.87, "y": -4953.52, "width": 2329.2, "height": 5209.11, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root", "x": 100.62, "y": 264.06, "scaleX": 1.21, "scaleY": 1.21}, {"name": "body up", "parent": "root", "x": 765.58, "y": -1560.85}, {"name": "body up2", "parent": "body up", "length": 390.12, "rotation": 83.9, "x": 2.96, "y": 35.53}, {"name": "body up3", "parent": "body up2", "length": 185.33, "rotation": -6.83, "x": 390.12}, {"name": "body up4", "parent": "body up3", "length": 140.77, "rotation": -1.69, "x": 185.33}, {"name": "body up5", "parent": "body up4", "length": 91.8, "rotation": 14.62, "x": 140.77}, {"name": "face", "parent": "body up5", "length": 372.14, "rotation": 3.58, "x": 111.57, "y": 24.12}, {"name": "body up6", "parent": "body up4", "length": 189.33, "rotation": 116.98, "x": 96.71, "y": 81.48}, {"name": "body up7", "parent": "body up6", "length": 303.04, "rotation": 57.08, "x": 189.33}, {"name": "hand left", "parent": "body up7", "length": 341.05, "rotation": -172.76, "x": 545.9, "y": 88.36}, {"name": "hand left2", "parent": "hand left", "length": 149.56, "rotation": -25.5, "x": 341.05}, {"name": "body up8", "parent": "body up4", "length": 212.24, "rotation": -86.58, "x": 179.53, "y": -160.06}, {"name": "body up9", "parent": "body up8", "length": 639.14, "rotation": -73.32, "x": 227.26, "y": -35.38}, {"name": "body up10", "parent": "body up9", "length": 529.08, "rotation": -0.75, "x": 687.69, "y": 4.1}, {"name": "body up11", "parent": "body up10", "length": 220.48, "rotation": -23.16, "x": 529.08}, {"name": "cloth down", "parent": "body up", "length": 523.66, "rotation": -90.79, "x": 4.92, "y": -30.59}, {"name": "foot right1", "parent": "cloth down", "length": 1018.43, "rotation": 3.85, "x": 255.49, "y": 130}, {"name": "foot right3", "parent": "foot right1", "length": 1018.43, "x": 1018.43}, {"name": "foot right2", "parent": "foot right3", "length": 415.31, "rotation": -10.99, "x": 1018.43, "y": -0.5}, {"name": "foot left3a", "parent": "cloth down", "length": 939.68, "rotation": 3.29, "x": 239.82, "y": -6.94}, {"name": "foot left3b", "parent": "foot left3a", "length": 939.68, "x": 939.68}, {"name": "foot left4", "parent": "foot left3b", "length": 271.7, "rotation": -32.24, "x": 939.68}, {"name": "bone-left-leg", "parent": "root", "rotation": -87.5, "x": 842.09, "y": -3708.71, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone-right-leg", "parent": "root", "rotation": -86.94, "x": 1005.52, "y": -3882.67, "color": "ff3f00ff", "icon": "ik"}, {"name": "target-left-foot", "parent": "bone-left-leg", "rotation": 87.5, "x": 229.8, "y": -144.94, "color": "ff3f00ff", "icon": "ik"}, {"name": "target-right-foot", "parent": "bone-right-leg", "rotation": 86.94, "x": 407.72, "y": -79.57, "color": "ff3f00ff", "icon": "ik"}, {"name": "ponytail25", "parent": "face", "rotation": -149.29, "x": 472.53, "y": -172.61}, {"name": "ponytail", "parent": "ponytail25", "length": 292.68, "rotation": -0.1, "x": 160.74, "y": 5.65}, {"name": "ponytail2", "parent": "ponytail", "length": 184.5, "rotation": -2.86, "x": 326.22, "y": -3.46}, {"name": "ponytail3", "parent": "ponytail2", "length": 171.62, "rotation": -8.4, "x": 195.4, "y": 2.25}, {"name": "ponytail4", "parent": "ponytail3", "length": 157.38, "rotation": -11.04, "x": 177.73, "y": 4.02}, {"name": "ponytail5", "parent": "ponytail4", "length": 206.7, "rotation": -12.45, "x": 159.37, "y": -0.42}, {"name": "ponytail6", "parent": "ponytail5", "length": 243.89, "rotation": -8.04, "x": 239.11, "y": 2.34}, {"name": "ponytail7", "parent": "ponytail6", "length": 308.8, "rotation": -11.72, "x": 267.8, "y": -6.69}, {"name": "ponytail8", "parent": "ponytail7", "length": 328.14, "rotation": -3.45, "x": 348.53, "y": -4.35}, {"name": "ponytail9", "parent": "ponytail8", "length": 295.89, "rotation": -4.34, "x": 366.23, "y": -6.66}, {"name": "ponytail10", "parent": "ponytail9", "length": 382.73, "rotation": -12.72, "x": 358.56, "y": -11.1}, {"name": "ponytail11", "parent": "ponytail10", "length": 286.2, "rotation": -11.29, "x": 438.89, "y": -16.19}, {"name": "ponytail12", "parent": "ponytail11", "length": 314.97, "rotation": 22.2, "x": 304.47, "y": -3.56}, {"name": "ponytail13", "parent": "ponytail12", "length": 162.01, "rotation": 23.66, "x": 329.83, "y": 1.75}, {"name": "ponytail14", "parent": "ponytail13", "length": 130.57, "rotation": 13.17, "x": 172.2, "y": -2.83}, {"name": "ponytail15", "parent": "ponytail14", "length": 68.17, "rotation": 41.59, "x": 130.57}, {"name": "ponytail16", "parent": "ponytail11", "length": 452.79, "rotation": 19.88, "x": 373.88, "y": -10.27}, {"name": "ponytail17", "parent": "ponytail16", "length": 149.18, "rotation": 11.42, "x": 472.54, "y": 5.49}, {"name": "ponytail18", "parent": "ponytail17", "length": 159.27, "rotation": 23.65, "x": 167.36, "y": -3.59}, {"name": "ponytail19", "parent": "ponytail11", "length": 146.09, "rotation": 21.07, "x": 282.84, "y": 84.68}, {"name": "ponytail20", "parent": "ponytail19", "length": 100.03, "rotation": 11.4, "x": 147.94, "y": -0.46}, {"name": "ponytail21", "parent": "ponytail20", "length": 66.43, "rotation": 12.67, "x": 100.03}, {"name": "ponytail22", "parent": "ponytail17", "length": 162.2, "rotation": 3.54, "x": 116.86, "y": -29.61}, {"name": "ponytail23", "parent": "ponytail22", "length": 89.78, "rotation": 29.45, "x": 178.63, "y": 5.11}, {"name": "ponytail24", "parent": "ponytail23", "length": 91.64, "rotation": 22.12, "x": 93.51, "y": 0.96}, {"name": "hairbond right8", "parent": "face", "rotation": -151.97, "x": 83.95, "y": -418.19}, {"name": "hairbond right", "parent": "hairbond right8", "length": 128.92, "rotation": 22.37, "x": 56.16, "y": 12.57}, {"name": "hairbond right2", "parent": "hairbond right", "length": 152.47, "rotation": 8.17, "x": 142.65, "y": 3.74}, {"name": "hairbond right3", "parent": "hairbond right2", "length": 162.73, "rotation": -11.86, "x": 173.29, "y": -6.42}, {"name": "hairbond right4", "parent": "hairbond right3", "length": 169.07, "rotation": -13.15, "x": 173.57, "y": -1}, {"name": "hairbond right5", "parent": "hairbond right4", "length": 97.45, "rotation": -7.53, "x": 178.7, "y": -0.04}, {"name": "hairbond right6", "parent": "hairbond right5", "length": 63.58, "rotation": 5.53, "x": 103.65, "y": 3.12}, {"name": "hairbond right7", "parent": "hairbond right6", "length": 29.39, "rotation": -3.53, "x": 67.83, "y": -0.64}, {"name": "hair bond left", "parent": "face", "x": 255.05, "y": -239.53}, {"name": "hair bond left2", "parent": "hair bond left", "length": 116.4, "rotation": -149.89, "x": -64.12, "y": -40.86}, {"name": "hair bond left3", "parent": "hair bond left2", "length": 150.49, "rotation": -0.55, "x": 128.82, "y": -2.02}, {"name": "hair bond left4", "parent": "hair bond left3", "length": 126.03, "rotation": 14.2, "x": 164.94, "y": -0.73}, {"name": "hair bond left5", "parent": "hair bond left4", "length": 99.2, "rotation": 15.82, "x": 143.29, "y": 7.4}, {"name": "hair bond left6", "parent": "hair bond left5", "length": 43.32, "rotation": -0.35, "x": 106.17, "y": 4.69}, {"name": "fringe left left12", "parent": "body up7", "rotation": 110.56, "x": 614.65, "y": -168.31}, {"name": "fringe left left13", "parent": "fringe left left12", "length": 55.48, "rotation": -101.23, "x": -2.6, "y": -6.88}, {"name": "fringe left left2", "parent": "fringe left left13", "length": 144.79, "rotation": -4.43, "x": 65.38, "y": 1.21}, {"name": "fringe left left3", "parent": "fringe left left2", "length": 79.32, "rotation": -32, "x": 146.4, "y": -0.9}, {"name": "fringe left left4", "parent": "fringe left left3", "length": 53.44, "rotation": -40.94, "x": 79.32}, {"name": "fringe left left5", "parent": "fringe left left4", "length": 40.89, "rotation": -23.88, "x": 54.74, "y": -0.03}, {"name": "fringe left left6", "parent": "fringe left left3", "length": 53.82, "rotation": -17.71, "x": 86.82, "y": 1.46}, {"name": "fringe left left7", "parent": "fringe left left6", "length": 25.17, "rotation": -3.25, "x": 53.82}, {"name": "fringe left left8", "parent": "fringe left left3", "length": 47.76, "rotation": -2.53, "x": 82.92, "y": 7.77}, {"name": "fringe left left9", "parent": "fringe left left8", "length": 16.72, "rotation": -2.24, "x": 52.85, "y": -0.26}, {"name": "fringe left left10", "parent": "fringe left left3", "length": 45.58, "rotation": 21.1, "x": 83.2, "y": 17.16}, {"name": "fringe left left11", "parent": "fringe left left10", "length": 31.07, "rotation": -22.43, "x": 45.58}, {"name": "fringe left right2", "parent": "body up7", "rotation": 9.32, "x": 627.01, "y": -99.49}, {"name": "fringe left right3", "parent": "fringe left right2", "length": 65.28, "rotation": 7.18, "x": 4.2, "y": 0.64}, {"name": "fringe left right4", "parent": "fringe left right3", "length": 85.57, "rotation": 2.36, "x": 72.9, "y": -0.82}, {"name": "fringe left right5", "parent": "fringe left right4", "length": 45.9, "rotation": -24.51, "x": 90.62, "y": 0.78}, {"name": "fringe left right6", "parent": "fringe left right5", "length": 25.22, "rotation": -52.2, "x": 52.95, "y": -5.71}, {"name": "fringe left right7", "parent": "fringe left right6", "length": 14, "rotation": -6.4, "x": 25.85, "y": -0.13}, {"name": "fringe left right8", "parent": "fringe left right5", "length": 22.04, "rotation": -45.35, "x": 60.34, "y": -2.07}, {"name": "fringe left right9", "parent": "fringe left right8", "length": 20.28, "rotation": -18.44, "x": 25.25, "y": -2.4}, {"name": "fringe left right10", "parent": "fringe left right5", "length": 35.53, "rotation": -41.8, "x": 68.89, "y": 4.96}, {"name": "fringe left right11", "parent": "fringe left right10", "length": 17.84, "rotation": -28.11, "x": 42.12, "y": -0.61}, {"name": "fringe left right12", "parent": "fringe left right5", "length": 31.81, "rotation": -17.98, "x": 73.19, "y": 13.43}, {"name": "fringe left right13", "parent": "fringe left right12", "length": 21.51, "rotation": -32.18, "x": 31.81}, {"name": "fringe left right14", "parent": "fringe left right13", "length": 16.52, "rotation": -18.03, "x": 24.76, "y": -2.74}, {"name": "fringe right left2", "parent": "body up9", "rotation": 84.52, "x": 605.53, "y": 202.03}, {"name": "fringe right left3", "parent": "fringe right left2", "length": 60.07, "rotation": -103.94, "x": 0.42, "y": -4.19}, {"name": "fringe right left4", "parent": "fringe right left3", "length": 77.99, "rotation": 20.67, "x": 63.89, "y": 1.8}, {"name": "fringe right left5", "parent": "fringe right left4", "length": 124.16, "rotation": 27.05, "x": 81.58, "y": 0.87}, {"name": "fringe right left6", "parent": "fringe right left5", "length": 33.76, "rotation": 34.85, "x": 124.55, "y": 5.5}, {"name": "fringe right left7", "parent": "fringe right left5", "length": 34.16, "rotation": 36.34, "x": 134.4, "y": 0.55}, {"name": "fringe right left8", "parent": "fringe right left5", "length": 39.73, "rotation": 15.41, "x": 150.51, "y": 3.75}, {"name": "fringe right left9", "parent": "fringe right left4", "length": 66.65, "rotation": 6.31, "x": 99.5, "y": -4.69}, {"name": "fringe right left10", "parent": "fringe right left9", "length": 34.4, "rotation": 27.93, "x": 64.5, "y": 8.92}, {"name": "fringe right right2", "parent": "body up9", "rotation": 84.52, "x": 588.15, "y": 284.77}, {"name": "fringe right right3", "parent": "fringe right right2", "length": 65.6, "rotation": -73.86, "x": 1.33, "y": -5.31}, {"name": "fringe right right4", "parent": "fringe right right3", "length": 56.99, "rotation": 0.08, "x": 66.6, "y": -1.32}, {"name": "fringe right right5", "parent": "fringe right right4", "length": 33.58, "rotation": 16.5, "x": 68.35, "y": 10.87}, {"name": "fringe right right5b", "parent": "fringe right right5", "length": 33.58, "x": 33.58}, {"name": "fringe right right5c", "parent": "fringe right right5b", "length": 33.58, "x": 33.58}, {"name": "fringe right right5d", "parent": "fringe right right5c", "length": 33.58, "x": 33.58}, {"name": "fringe right right5e", "parent": "fringe right right5d", "length": 33.58, "x": 33.58}, {"name": "fringe right right5f", "parent": "fringe right right5e", "length": 33.58, "x": 33.58}, {"name": "fringe right right5g", "parent": "fringe right right5f", "length": 33.58, "x": 33.58}, {"name": "face2", "parent": "face", "x": 380.88, "y": -28.19}, {"name": "hair left", "parent": "face2", "length": 94.96, "rotation": 175.15, "x": -45.42, "y": 133.24}, {"name": "hair left2", "parent": "hair left", "length": 33.67, "rotation": 29.98, "x": 71.62, "y": 37.46}, {"name": "hair left3", "parent": "hair left2", "length": 15.02, "rotation": 32.24, "x": 36.83, "y": 1.19}, {"name": "hair left4", "parent": "hair left3", "length": 10.97, "rotation": 16.25, "x": 15.87, "y": 0.47}, {"name": "hair left5", "parent": "hair left", "length": 44.36, "rotation": 28.68, "x": 86.02, "y": 29.77}, {"name": "hair left6", "parent": "hair left5", "length": 22.48, "rotation": 23.74, "x": 44.14, "y": -0.43}, {"name": "hair left7", "parent": "hair left6", "length": 15.44, "rotation": 10.67, "x": 23.77, "y": -0.83}, {"name": "hair left8", "parent": "hair left", "length": 47, "rotation": 25.72, "x": 99.51, "y": 13.54}, {"name": "hair left9", "parent": "hair left8", "length": 22.37, "rotation": 17.91, "x": 47.2, "y": 0.44}, {"name": "hair left10", "parent": "hair left9", "length": 14.36, "rotation": 19.34, "x": 22.7, "y": 0.36}, {"name": "hair left11", "parent": "hair left", "length": 43.74, "rotation": 25.56, "x": 107.51, "y": 3.5}, {"name": "hair left12", "parent": "hair left11", "length": 15.13, "rotation": 20.71, "x": 45.02, "y": 0.49}, {"name": "hair left13", "parent": "hair left12", "length": 11.77, "rotation": 6.71, "x": 25.44}, {"name": "hair left14", "parent": "hair left", "length": 59.93, "rotation": 0.68, "x": 99.04, "y": -1.28}, {"name": "hair left15", "parent": "hair left14", "length": 40.16, "rotation": -0.29, "x": 60.55, "y": -0.61}, {"name": "hair left16", "parent": "hair left14", "length": 34.87, "rotation": 30.34, "x": 68.49, "y": 8.74}, {"name": "hair left17", "parent": "hair left16", "length": 13.56, "rotation": 0.32, "x": 34.87}, {"name": "hair left18", "parent": "hair left", "length": 40.76, "rotation": -2.09, "x": 118.68, "y": -53.09}, {"name": "hair left19", "parent": "hair left18", "length": 32.36, "rotation": 11.02, "x": 40.76}, {"name": "hair left20", "parent": "hair left19", "length": 28.68, "rotation": 25.77, "x": 32.83, "y": -0.06}, {"name": "hair left21", "parent": "hair left", "length": 50.82, "rotation": 4.51, "x": 149.97, "y": -39.47}, {"name": "hair left22", "parent": "hair left21", "length": 31.88, "rotation": 4.53, "x": 50.82}, {"name": "hair left23", "parent": "hair left22", "length": 23.81, "rotation": 22.41, "x": 34.45, "y": 1.1}, {"name": "hair left24", "parent": "hair left23", "length": 12.08, "rotation": 26.14, "x": 25.43, "y": 1.83}, {"name": "hair left25", "parent": "hair left14", "length": 42.38, "rotation": 22.39, "x": 68.2, "y": -30.28}, {"name": "hair left26", "parent": "hair left25", "length": 21.18, "rotation": 37.23, "x": 45.97, "y": 2.47}, {"name": "hair left27", "parent": "hair left26", "length": 23.17, "rotation": 11.11, "x": 22.74, "y": 0.93}, {"name": "hair right", "parent": "face2", "length": 123.44, "rotation": -134.46, "x": -38.95, "y": 5.12}, {"name": "hair right2", "parent": "hair right", "length": 61.72, "x": 123.44}, {"name": "hair right3", "parent": "hair right", "length": 54.12, "rotation": 21.16, "x": 88.75, "y": -67.22}, {"name": "hair right4", "parent": "hair right3", "length": 32.74, "rotation": 16.36, "x": 54.57, "y": 0.16}, {"name": "hair right5", "parent": "hair right4", "length": 17.41, "rotation": 9.71, "x": 38.89, "y": 1.81}, {"name": "hair right6", "parent": "hair right", "length": 43.42, "rotation": 14.6, "x": 102.86, "y": -74.71}, {"name": "hair right7", "parent": "hair right6", "length": 22.16, "rotation": 13.75, "x": 43.42}, {"name": "hair right8", "parent": "hair right7", "length": 16.4, "rotation": 17.57, "x": 22.16}, {"name": "hair right9", "parent": "hair right", "length": 36.34, "rotation": -11.64, "x": 71.72, "y": -90.87}, {"name": "hair right10", "parent": "hair right9", "length": 17.6, "rotation": 17.53, "x": 38.83, "y": -0.7}, {"name": "hair right11", "parent": "hair right10", "length": 10.8, "rotation": 14.14, "x": 17.6}, {"name": "hair right12", "parent": "hair right", "length": 39.13, "rotation": 15.65, "x": 193.54, "y": 16.98}, {"name": "hair right13", "parent": "hair right12", "length": 28.21, "rotation": 31.03, "x": 39.13}, {"name": "hair right14", "parent": "hair right13", "length": 16.15, "rotation": 35.12, "x": 28.21}, {"name": "hair right15", "parent": "hair right", "length": 34.69, "rotation": -9.83, "x": 202.44, "y": 0.47}, {"name": "hair right16", "parent": "hair right15", "length": 27.56, "rotation": 42.22, "x": 34.69}, {"name": "hair right17", "parent": "hair right16", "length": 15.14, "rotation": 14.66, "x": 27.5, "y": 0.4}, {"name": "<PERSON><PERSON><PERSON> left", "parent": "face", "length": 59.33, "rotation": -156.35, "x": 155.41, "y": 114.73}, {"name": "sidehair left2", "parent": "<PERSON><PERSON><PERSON> left", "length": 35.49, "rotation": -6.96, "x": 64.42, "y": -14.72}, {"name": "sidehair left3", "parent": "sidehair left2", "length": 19.22, "rotation": 21.49, "x": 37.46, "y": 0.91}, {"name": "sidehair left4", "parent": "sidehair left3", "length": 12.93, "rotation": 14.55, "x": 19.22}, {"name": "sidehair left5", "parent": "<PERSON><PERSON><PERSON> left", "length": 28.84, "rotation": -7.51, "x": 62.98, "y": -0.73}, {"name": "sidehair left6", "parent": "sidehair left5", "length": 17.52, "rotation": 18.15, "x": 28.84}, {"name": "sidehair left7", "parent": "sidehair left6", "length": 9.4, "rotation": 22.77, "x": 17.52}, {"name": "sidehair left8", "parent": "<PERSON><PERSON><PERSON> left", "length": 283.81, "rotation": -39.16, "x": 118.5, "y": 12.87}, {"name": "sidehair left9", "parent": "sidehair left8", "length": 52.52, "rotation": 3.28, "x": 283.81}, {"name": "sidehair left10", "parent": "sidehair left9", "length": 42.17, "rotation": -6.87, "x": 52.52}, {"name": "sidehair right", "parent": "face", "length": 79.81, "rotation": -179.29, "x": 206.32, "y": -131.07}, {"name": "sidehair right2", "parent": "sidehair right", "length": 105.41, "rotation": -3.7, "x": 89.4, "y": 6.08}, {"name": "sidehair right3", "parent": "sidehair right2", "length": 226.87, "rotation": -5.54, "x": 105.41}, {"name": "sidehair right4", "parent": "sidehair right3", "length": 39.66, "rotation": -4.52, "x": 233.31, "y": -11.44}, {"name": "sidehair right5", "parent": "sidehair right4", "length": 27.17, "rotation": -6.8, "x": 39.66}, {"name": "sidehair right6", "parent": "sidehair right5", "length": 14.5, "rotation": 3.27, "x": 30.6, "y": -0.13}, {"name": "sidehair right7", "parent": "sidehair right3", "length": 48.9, "rotation": 4.95, "x": 224.52, "y": 1.98}, {"name": "sidehair right8", "parent": "sidehair right7", "length": 25.37, "rotation": -9.87, "x": 49.99}, {"name": "sidehair right9", "parent": "sidehair right8", "length": 24.52, "rotation": -2.94, "x": 29.65, "y": 0.74}, {"name": "sidehair right10", "parent": "sidehair right", "length": 34.53, "rotation": -12.57, "x": 58.34, "y": -25.97}, {"name": "sidehair right11", "parent": "sidehair right10", "length": 24.37, "rotation": -11.08, "x": 35.06, "y": 0.7}, {"name": "sidehair right12", "parent": "sidehair right11", "length": 15.43, "rotation": -20.74, "x": 26.92, "y": 0.24}, {"name": "sidehair right13", "parent": "sidehair right", "length": 21.42, "rotation": -21.15, "x": 100.54, "y": -19.79}, {"name": "sidehair right14", "parent": "sidehair right13", "length": 9.72, "rotation": -9.71, "x": 21.42}, {"name": "sidehair right15", "parent": "sidehair right14", "length": 6.21, "rotation": -10.31, "x": 14.17, "y": -0.56}, {"name": "sidehair right16", "parent": "sidehair right", "length": 36.02, "rotation": -19.28, "x": 101.29, "y": -9.88}, {"name": "sidehair right17", "parent": "sidehair right16", "length": 15.84, "rotation": -10.56, "x": 37.38, "y": -0.28}, {"name": "sidehair right18", "parent": "sidehair right17", "length": 20.16, "rotation": -8.13, "x": 15.84}, {"name": "sidehair right19", "parent": "sidehair right16", "length": 14.27, "rotation": -9.63, "x": 39.54, "y": 7.59}, {"name": "sidehair right20", "parent": "sidehair right19", "length": 10, "rotation": -13.79, "x": 14.51, "y": 0.11}, {"name": "sidehair right21", "parent": "sidehair right18", "length": 14.56, "rotation": -8.41, "x": 21.23, "y": 0.24}, {"name": "cloth strip right", "parent": "cloth down", "rotation": 90.79, "x": -55.41, "y": 228.87}, {"name": "cloth strip right2", "parent": "cloth strip right", "length": 164.04, "rotation": -83.02, "x": 3.32, "y": -13.29}, {"name": "cloth strip right3", "parent": "cloth strip right2", "length": 62.18, "rotation": 2.88, "x": 168.98, "y": -0.62}, {"name": "cloth strip right4", "parent": "cloth strip right3", "length": 496.08, "rotation": 4.71, "x": 63.21, "y": 0.62}, {"name": "cloth strip right5", "parent": "cloth strip right4", "length": 346.78, "rotation": 4.16, "x": 496.08}, {"name": "cloth strip right6", "parent": "cloth strip right5", "length": 236.87, "rotation": -8.39, "x": 341.56, "y": -2.8}, {"name": "cloth strip right7", "parent": "cloth strip right6", "length": 144.22, "rotation": -6.32, "x": 236.87}, {"name": "cloth strip right8", "parent": "cloth strip right7", "length": 117.59, "rotation": -2.05, "x": 144.22}, {"name": "cloth strip left", "parent": "root", "x": 638.44, "y": -1530.97}, {"name": "cloth strip left2", "parent": "cloth strip left", "length": 127.31, "rotation": -94.09, "x": -3.63, "y": -19.95}, {"name": "cloth strip left3", "parent": "cloth strip left2", "length": 78.03, "rotation": 2.75, "x": 132.74, "y": 0.39}, {"name": "cloth strip left4", "parent": "cloth strip left3", "length": 395.86, "rotation": -0.46, "x": 58.52, "y": -3.64}, {"name": "cloth strip left5", "parent": "cloth strip left4", "length": 265.95, "rotation": -3.16, "x": 395.86}, {"name": "cloth strip left6", "parent": "cloth strip left5", "length": 191.17, "rotation": 1.38, "x": 265.79, "y": 0.65}, {"name": "cloth strip left7", "parent": "cloth strip left6", "length": 162.48, "rotation": 3.58, "x": 191.17}, {"name": "cloth strip left8", "parent": "cloth strip left7", "length": 132.48, "rotation": 0.86, "x": 168.28, "y": -0.12}, {"name": "cloth strip left9", "parent": "cloth strip left8", "length": 93.26, "rotation": 4.01, "x": 132.51, "y": 1.98}, {"name": "cloth strip left10", "parent": "cloth strip left9", "length": 69.9, "rotation": 3.27, "x": 93.42, "y": 1.97}], "slots": [{"name": "hair bond left", "bone": "hair bond left6", "attachment": "hair bond left"}, {"name": "hairbond right", "bone": "hairbond right8", "attachment": "hairbond right"}, {"name": "ponytail", "bone": "ponytail25", "attachment": "ponytail"}, {"name": "head top", "bone": "face", "attachment": "head top"}, {"name": "fringe left right", "bone": "root"}, {"name": "fringe right left", "bone": "root"}, {"name": "fringe right left2", "bone": "fringe right left10", "attachment": "fringe right left"}, {"name": "fringe left right2", "bone": "fringe left right14", "attachment": "fringe left right"}, {"name": "body up", "bone": "body up9", "attachment": "body up"}, {"name": "foot left", "bone": "foot left4", "attachment": "foot left"}, {"name": "foot right", "bone": "foot right2", "attachment": "foot right"}, {"name": "cloth down", "bone": "cloth down", "attachment": "cloth down"}, {"name": "cloth strip left", "bone": "cloth strip left8", "attachment": "cloth strip left"}, {"name": "cloth strip right", "bone": "cloth strip right6", "attachment": "cloth strip right"}, {"name": "fringe right right", "bone": "root"}, {"name": "fringe left left", "bone": "root"}, {"name": "hand left", "bone": "hand left", "attachment": "hand left"}, {"name": "face", "bone": "face", "attachment": "face"}, {"name": "mouth", "bone": "face", "attachment": "mouth"}, {"name": "right eye", "bone": "face", "attachment": "right eye"}, {"name": "left eye", "bone": "face", "attachment": "left eye"}, {"name": "right half close", "bone": "root"}, {"name": "left half close", "bone": "root"}, {"name": "right close", "bone": "root"}, {"name": "left close", "bone": "root"}, {"name": "left eyebrow", "bone": "face", "attachment": "left eyebrow"}, {"name": "right eyebrow", "bone": "face", "attachment": "right eyebrow"}, {"name": "sidehair right", "bone": "sidehair right21", "attachment": "sidehair right"}, {"name": "<PERSON><PERSON><PERSON> left", "bone": "sidehair left8", "attachment": "<PERSON><PERSON><PERSON> left"}, {"name": "hair right", "bone": "hair right15", "attachment": "hair right"}, {"name": "hair left", "bone": "hair left25", "attachment": "hair left"}, {"name": "fringe left left2", "bone": "fringe left left13", "attachment": "fringe left left"}, {"name": "fringe right right2", "bone": "fringe right right5", "attachment": "fringe right right"}], "ik": [{"name": "bone-left-leg", "bones": ["foot left3a", "foot left3b"], "target": "bone-left-leg"}, {"name": "bone-right-leg", "order": 2, "bones": ["foot right1", "foot right3"], "target": "bone-right-leg"}, {"name": "target-left-foot", "order": 1, "bones": ["foot left4"], "target": "target-left-foot"}, {"name": "target-right-foot", "order": 3, "bones": ["foot right2"], "target": "target-right-foot"}], "skins": [{"name": "default", "attachments": {"body up": {"body up": {"type": "mesh", "uvs": [0.5411, 0, 0.54668, 0, 0.5668, 0.07513, 0.60726, 0.09957, 0.79411, 0.14768, 0.82985, 0.34, 0.83704, 0.35242, 0.89511, 0.424, 0.93751, 0.46002, 1, 0.48865, 1, 0.49772, 0.97008, 0.5134, 0.86093, 0.60704, 0.85607, 0.63588, 0.86986, 0.93156, 0.76691, 1, 0.75747, 0.99589, 0.74283, 0.9287, 0.77253, 0.78987, 0.77244, 0.78517, 0.7503, 0.66567, 0.7336, 0.60115, 0.65742, 0.57067, 0.64033, 0.54651, 0.65196, 0.43118, 0.63847, 0.38445, 0.59649, 0.46368, 0.50284, 0.50602, 0.47817, 0.52559, 0.45424, 0.5687, 0.36727, 0.56801, 0.33583, 0.51426, 0.33285, 0.51133, 0.20586, 0.62885, 0.14316, 0.60895, 0.05206, 0.54246, 0, 0.45888, 0, 0.45763, 0.14128, 0.34737, 0.16684, 0.30883, 0.23447, 0.17142, 0.4541, 0.09317, 0.44657, 0.07064, 0.52854, 0.02432], "triangles": [2, 41, 42, 43, 0, 1, 2, 43, 1, 25, 3, 4, 25, 4, 5, 6, 24, 25, 6, 25, 5, 2, 42, 43, 35, 36, 37, 41, 3, 25, 3, 41, 2, 25, 40, 41, 25, 39, 40, 39, 25, 32, 11, 8, 9, 26, 27, 25, 32, 38, 39, 25, 27, 32, 38, 35, 37, 10, 11, 9, 28, 31, 32, 27, 28, 32, 32, 35, 38, 30, 31, 28, 29, 30, 28, 23, 24, 21, 7, 24, 6, 21, 22, 23, 24, 7, 21, 12, 21, 7, 8, 12, 7, 11, 12, 8, 34, 35, 32, 33, 34, 32, 13, 21, 12, 20, 21, 13, 19, 20, 13, 14, 18, 19, 14, 19, 13, 17, 18, 14, 15, 16, 17, 14, 15, 17], "vertices": [-338.27, -255.75, -337.53, -248, -203.53, -232.66, -155.42, -180.56, -46.44, 70.83, 294.49, 88.23, 317.16, 96.12, 450.01, 164.77, 518.62, 217.6, 576.98, 299.59, 592.84, 298.07, 616.27, 253.89, 765.41, 86.62, 815.18, 75.04, 1333.85, 44.64, 1439.78, -109.81, 1431.34, -122.22, 1311.95, -131.29, 1073.22, -66.78, 1065, -66.12, 853.17, -76.83, 738.16, -89.21, 674.74, -189.87, 630.24, -209.56, 430.19, -174.08, 346.71, -184.99, 479.62, -256.56, 541.15, -393.7, 572.07, -431.24, 644.24, -471.69, 631.46, -592.34, 533.31, -626.99, 527.8, -630.64, 716.3, -826.68, 673.18, -910.41, 544.82, -1025.78, 391.8, -1084.06, 389.61, -1083.85, 215.7, -869.18, 151.72, -827.23, -79.45, -710.28, -186.99, -392.18, -227.39, -398.85, -297.43, -277.26], "hull": 44, "edges": [0, 86, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 1395, "height": 1756}}, "cloth down": {"cloth down": {"x": 1025.65, "y": 254.17, "rotation": 90.79, "width": 1171, "height": 2521}}, "cloth strip left": {"cloth strip left": {"type": "mesh", "uvs": [0.85747, 0, 0.96416, 0.00289, 0.96822, 0.00798, 0.97227, 0.01307, 0.97619, 0.01798, 0.98176, 0.02496, 0.98796, 0.03273, 0.99398, 0.04029, 1, 0.04784, 1, 0.06366, 0.95754, 0.14033, 0.92261, 0.15661, 0.77255, 0.16364, 0.99386, 0.22734, 0.86039, 0.32964, 0.51457, 0.53931, 0.42931, 0.64623, 0.41348, 0.6578, 0.39791, 0.76093, 0.41163, 0.7972, 0.51577, 0.92078, 0.45534, 1, 0.39472, 1, 0.39134, 0.99788, 0.12302, 0.94111, 0.03133, 0.82572, 0, 0.71428, 0.09209, 0.62025, 0.25682, 0.53939, 0.4026, 0.43447, 0.4183, 0.41591, 0.44541, 0.28139, 0.35776, 0.21766, 0.59901, 0.15828, 0.58471, 0.15803, 0.50264, 0.13549, 0.59233, 0.07395, 0.66458, 0.04773, 0.68452, 0.04049, 0.70229, 0.03404, 0.72356, 0.02632, 0.74387, 0.01895, 0.76093, 0.01276, 0.77852, 0.00638, 0.7961, 0], "triangles": [21, 22, 23, 20, 21, 23, 24, 25, 20, 23, 24, 20, 25, 19, 20, 18, 26, 17, 25, 26, 18, 25, 18, 19, 16, 27, 15, 17, 27, 16, 26, 27, 17, 36, 37, 9, 10, 36, 9, 14, 29, 30, 14, 15, 29, 15, 28, 29, 15, 27, 28, 35, 36, 10, 35, 33, 34, 35, 10, 33, 11, 33, 10, 12, 33, 11, 32, 33, 12, 32, 12, 13, 31, 32, 13, 14, 31, 13, 30, 31, 14, 37, 8, 9, 38, 7, 8, 37, 38, 8, 40, 5, 6, 44, 0, 1, 43, 44, 1, 43, 1, 2, 42, 43, 2, 3, 42, 2, 4, 41, 42, 4, 42, 3, 41, 4, 5, 40, 41, 5, 39, 40, 6, 39, 6, 7, 38, 39, 7], "vertices": [1, 193, -1.12, 15.97, 1, 1, 193, 9.12, 11.5, 1, 1, 193, 9.51, 3.64, 1, 1, 193, 9.9, -4.22, 1, 1, 193, 10.28, -11.81, 1, 1, 193, 10.81, -22.6, 1, 1, 193, 11.41, -34.6, 1, 1, 193, 11.98, -46.27, 1, 1, 193, 12.56, -57.93, 1, 1, 194, 61.11, 20.6, 1, 2, 196, -10.78, 25.86, 0.82583, 194, 179.55, 24.97, 0.17417, 1, 196, 14.47, 23.3, 1, 1, 196, 25.77, 9.24, 1, 3, 196, 123.48, 33.55, 1, 198, -539.02, 30.77, 0, 200, -893.9, 89.73, 0, 3, 196, 281.85, 25.68, 1, 198, -380.48, 27.85, 0, 201, -861.36, 133.13, 0, 3, 197, 210.35, 14.25, 0.9995, 198, -55.1, 14.92, 0.0005, 200, -412.64, 36.53, 0, 3, 197, 375.62, 20.35, 0, 198, 110.28, 17.06, 1, 200, -247.6, 25.88, 0, 3, 197, 393.57, 20.38, 0, 198, 128.22, 16.65, 1, 200, -229.74, 24.1, 0, 3, 197, 552.44, 32.65, 0, 198, 287.33, 25.1, 0, 199, 97.54, 19.06, 1, 3, 197, 608.14, 38.8, 0, 199, 153.57, 20.37, 0.95752, 200, -14.4, 20.7, 0.04248, 2, 201, 45.86, 22.72, 0.99997, 202, -46.3, 23.43, 3e-05, 1, 202, 74.04, 0.38, 1, 4, 197, 920.45, 64.24, 0, 199, 466.91, 18.75, 0, 200, 298.87, 14.41, 0, 202, 73.21, -5.38, 1, 4, 197, 917.21, 63.63, 0, 199, 463.62, 18.43, 0, 200, 295.58, 14.13, 0, 202, 69.92, -5.24, 1, 4, 197, 832.06, 30.4, 0, 199, 375.93, -7.33, 0, 200, 207.51, -10.32, 0, 201, 73.96, -17.51, 1, 3, 199, 197.64, -16.14, 0.00108, 200, 29.12, -16.46, 0.99892, 202, -198.28, -1.84, 0, 4, 197, 483.93, -11.63, 0, 199, 25.47, -19.14, 1, 201, -276.24, 0.44, 0, 202, -369.15, 19.53, 0, 3, 198, 72.24, -17.76, 1, 201, -420.24, 21.57, 0, 202, -511.71, 48.83, 0, 3, 197, 212.61, -10.4, 1, 201, -543.37, 47.92, 0, 202, -633.15, 82.15, 0, 4, 197, 49.9, -10.45, 1, 198, -216.09, -5.91, 0, 201, -703.71, 75.61, 0, 202, -791.64, 118.93, 0, 4, 197, 21.21, -11.42, 1, 198, -244.8, -6.2, 0, 201, -732.15, 79.54, 0, 202, -819.81, 124.48, 0, 3, 196, 208.6, -16.47, 1, 198, -452.39, -16.56, 0, 200, -811.18, 35.84, 0, 2, 196, 110.44, -27.96, 1, 201, -1037.84, 99.73, 0, 2, 196, 18.01, -7.67, 1, 198, -643.16, -13.71, 0, 3, 196, 17.68, -9.06, 0.99983, 194, 209.39, -8.78, 0.00017, 198, -643.45, -15.11, 0, 3, 196, -16.89, -18.02, 0.74965, 194, 175.21, -19.12, 0.25035, 198, -677.72, -25.14, 0, 1, 194, 79.76, -17.31, 1, 1, 193, -19.64, -57.77, 1, 1, 193, -17.72, -46.58, 1, 1, 193, -16.02, -36.62, 1, 1, 193, -13.98, -24.7, 1, 1, 193, -12.03, -13.31, 1, 1, 193, -10.39, -3.74, 1, 1, 193, -8.7, 6.12, 1, 1, 193, -7.01, 15.97, 1], "hull": 45, "edges": [0, 88, 0, 2, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 84, 6, 82, 84, 6, 8, 82, 8, 88, 2, 84, 86, 86, 88, 2, 4, 4, 6, 86, 4, 80, 82, 8, 10, 80, 10, 78, 80, 10, 12, 78, 12, 76, 78, 12, 14, 14, 16, 76, 14, 72, 74, 74, 76, 74, 16], "width": 96, "height": 1545}}, "cloth strip right": {"cloth strip right": {"type": "mesh", "uvs": [0.05055, 0, 0.05914, 0.00401, 0.06773, 0.00802, 0.08031, 0.01388, 0.09358, 0.02008, 0.11152, 0.02844, 0.12945, 0.03681, 0.14033, 0.04683, 0.15791, 0.06302, 0.18128, 0.08454, 0.1858, 0.0887, 0.1985, 0.12276, 0.20824, 0.14887, 0.2131, 0.16192, 0.21797, 0.17497, 0.20521, 0.18311, 0.33972, 0.22908, 0.35707, 0.29909, 0.3794, 0.33274, 0.537, 0.4873, 0.54939, 0.49483, 0.75276, 0.60724, 0.89936, 0.72545, 0.98736, 0.87083, 0.99907, 0.93997, 0.92197, 1, 0.90299, 1, 0.82448, 0.92902, 0.79802, 0.83527, 0.71464, 0.7193, 0.67286, 0.6754, 0.59419, 0.59718, 0.53979, 0.55718, 0.04877, 0.24923, 0.10424, 0.19107, 0.10514, 0.18593, 0.06357, 0.16747, 0.02201, 0.149, 0, 0.13923, 0, 0.13073, 0, 0.10956, 0.00349, 0.08885, 0.00698, 0.06814, 0.00947, 0.05339, 0.01322, 0.03116, 0.01973, 0.02394, 0.02625, 0.01672, 0.03153, 0.01087, 0.03644, 0.00543, 0.04134, 0], "triangles": [27, 23, 24, 26, 27, 24, 25, 26, 24, 28, 22, 23, 27, 28, 23, 22, 29, 30, 28, 29, 22, 32, 19, 20, 32, 33, 19, 20, 21, 32, 31, 32, 21, 30, 31, 21, 22, 30, 21, 37, 38, 39, 11, 37, 39, 12, 37, 11, 36, 37, 12, 36, 12, 13, 35, 36, 13, 13, 14, 35, 15, 35, 14, 33, 18, 19, 34, 35, 15, 15, 16, 34, 16, 33, 34, 17, 33, 16, 33, 17, 18, 9, 41, 42, 39, 40, 11, 41, 10, 40, 9, 10, 41, 11, 40, 10, 9, 42, 8, 48, 49, 0, 1, 48, 0, 2, 47, 48, 2, 48, 1, 3, 46, 47, 3, 47, 2, 4, 45, 46, 4, 46, 3, 5, 44, 45, 5, 45, 4, 6, 43, 44, 6, 44, 5, 43, 6, 7, 8, 42, 43, 8, 43, 7], "vertices": [1, 185, 0.79, 20.19, 1, 1, 185, 4, 13.92, 1, 1, 185, 7.22, 7.66, 1, 1, 185, 11.92, -1.51, 1, 1, 185, 16.89, -11.19, 1, 1, 185, 23.59, -24.26, 1, 1, 185, 30.3, -37.34, 1, 1, 185, 34.37, -53, 1, 1, 185, 40.94, -78.31, 1, 2, 185, 49.69, -111.95, 0.08014, 186, 103.57, 34.03, 0.91986, 1, 186, 110.22, 34.91, 1, 3, 186, 163.64, 33.16, 0.6052, 188, -63.88, 38.75, 0.322, 187, -3.64, 34, 0.0728, 3, 186, 204.58, 31.81, 0.3026, 188, -23.47, 32.01, 0.56881, 187, 37.19, 30.6, 0.12859, 3, 186, 225.05, 31.14, 0.1513, 188, -3.27, 28.64, 0.69221, 187, 57.6, 28.9, 0.15649, 2, 188, 16.93, 25.27, 0.81561, 187, 78.01, 27.2, 0.18439, 2, 188, 28.04, 17.45, 0.97478, 187, 89.72, 20.31, 0.02522, 1, 188, 110.24, 48.05, 1, 1, 188, 217.77, 26.8, 1, 1, 188, 270.79, 21.65, 1, 1, 189, 24.56, 16.16, 1, 1, 189, 37.21, 16.77, 1, 1, 189, 228.03, 32.36, 1, 2, 189, 420.6, 24.94, 0.0878, 190, 74.15, 38.97, 0.9122, 1, 191, 62.97, 37.69, 1, 2, 191, 171.07, 34.47, 0.05403, 192, 25.59, 35.41, 0.94597, 1, 192, 118.37, 3.35, 1, 1, 192, 118.13, -3.74, 1, 2, 191, 149.41, -29.47, 0.40495, 192, 6.25, -29.26, 0.59505, 2, 190, 236.2, -29.15, 0.4506, 191, 2.54, -29.05, 0.5494, 1, 190, 52.29, -27.26, 1, 2, 189, 319.32, -30.16, 0.50458, 190, -18.01, -30.31, 0.49542, 1, 189, 194.08, -18.74, 1, 1, 189, 128.34, -17.93, 1, 1, 188, 113.34, -65.18, 1, 1, 188, 30.59, -22.23, 1, 2, 188, 22.89, -19.89, 0.96445, 187, 87.66, -17.32, 0.03555, 3, 186, 226.86, -25.42, 0.06737, 188, -8.95, -27.67, 0.58317, 187, 56.56, -27.69, 0.34946, 3, 186, 196.33, -37.34, 0.13473, 188, -40.79, -35.45, 0.20189, 187, 25.46, -38.06, 0.66338, 2, 186, 180.16, -43.66, 0.1704, 187, 9, -43.55, 0.8296, 2, 186, 166.98, -42.04, 0.40403, 187, -4.09, -41.27, 0.59597, 2, 186, 134.13, -38.02, 0.98619, 187, -36.69, -35.6, 0.01381, 3, 185, -16.81, -118.68, 0.26413, 186, 102.16, -32.79, 0.72571, 187, -68.35, -28.77, 0.01016, 1, 185, -15.5, -86.32, 1, 1, 185, -14.57, -63.26, 1, 1, 185, -13.17, -28.51, 1, 1, 185, -10.73, -17.23, 1, 1, 185, -8.29, -5.94, 1, 1, 185, -6.32, 3.21, 1, 1, 185, -4.48, 11.7, 1, 1, 185, -2.65, 20.19, 1], "hull": 50, "edges": [0, 98, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 94, 4, 92, 94, 4, 6, 92, 6, 88, 90, 90, 92, 6, 8, 90, 8, 8, 10, 10, 12, 88, 10, 94, 96, 96, 98, 0, 2, 2, 4, 96, 2, 86, 88, 12, 14, 86, 14, 84, 86, 14, 16, 84, 16, 80, 82, 82, 84, 16, 18, 18, 20, 82, 18, 76, 78, 78, 80, 20, 22, 78, 22, 74, 76, 22, 24, 74, 24, 70, 72, 72, 74, 24, 26, 26, 28, 72, 26, 70, 28], "width": 374, "height": 1563}}, "face": {"face": {"type": "mesh", "uvs": [0.35594, 0, 0.67966, 0.03637, 0.8268, 0.10608, 0.86471, 0.27117, 0.91861, 0.25312, 0.98281, 0.3004, 0.97953, 0.47229, 0.84633, 0.68986, 0.72692, 0.82072, 0.52314, 0.98595, 0.4577, 1, 0.42582, 1, 0.36766, 0.94906, 0.20121, 0.7426, 0.04491, 0.36255, 0.0358, 0.17599, 0.09523, 0.04017, 0.31596, 0], "triangles": [17, 14, 15, 5, 3, 4, 6, 3, 5, 2, 3, 1, 7, 3, 6, 16, 17, 15, 0, 13, 14, 0, 14, 17, 0, 3, 13, 1, 3, 0, 7, 13, 3, 8, 13, 7, 12, 13, 8, 9, 12, 8, 10, 11, 12, 9, 10, 12], "vertices": [295.1, 8.61, 278.15, -84.07, 254.21, -125.18, 203.11, -132.96, 207.65, -148.85, 192.05, -166.47, 139.62, -162.24, 75.57, -119.67, 37.76, -82.73, -9.03, -20.79, -12.14, -1.65, -11.57, 7.54, 5.04, 23.35, 71.09, 67.42, 189.98, 105.24, 247.12, 104.31, 287.53, 84.58, 295.82, 20.14], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 289, "height": 306}}, "foot left": {"foot left": {"x": 15.18, "y": -34.05, "rotation": 119.74, "width": 232, "height": 498}}, "foot right": {"foot right": {"x": 11.12, "y": -23.21, "rotation": 97.91, "width": 210, "height": 843}}, "fringe left left2": {"fringe left left": {"type": "mesh", "uvs": [0.95164, 0, 0.96575, 0, 0.97385, 0, 0.98098, 0.00484, 0.99231, 0.01253, 0.99203, 0.01584, 0.99174, 0.01916, 0.99086, 0.02942, 0.98942, 0.04631, 0.98653, 0.08009, 0.98387, 0.11105, 0.98056, 0.14973, 0.95981, 0.39211, 0.69348, 0.73755, 0.40146, 0.95397, 0.26717, 1, 0.22668, 1, 0, 0.724, 0, 0.68123, 0.28196, 0.72566, 0.45355, 0.69469, 0.64973, 0.54089, 0.75993, 0.48428, 0.65677, 0.38792, 0.79247, 0.35752, 0.81574, 0.31216, 0.88239, 0.14027, 0.89371, 0.11108, 0.90641, 0.07832, 0.92136, 0.03977, 0.92661, 0.02624, 0.93496, 0.00468, 0.93678, 0], "triangles": [7, 30, 6, 30, 31, 6, 25, 26, 11, 26, 27, 11, 11, 27, 10, 27, 28, 10, 10, 28, 9, 28, 29, 9, 9, 29, 8, 29, 30, 8, 8, 30, 7, 6, 31, 5, 15, 16, 14, 16, 19, 14, 16, 17, 19, 14, 19, 20, 19, 17, 18, 14, 20, 13, 20, 21, 13, 13, 22, 12, 13, 21, 22, 22, 24, 12, 22, 23, 24, 24, 25, 12, 12, 25, 11, 5, 31, 32, 32, 0, 5, 5, 0, 4, 4, 0, 3, 0, 1, 3, 1, 2, 3], "vertices": [1, 65, -3.35, 5.58, 1, 1, 65, -0.23, 5.58, 1, 1, 65, 1.56, 5.58, 1, 1, 65, 3.13, 3.84, 1, 1, 65, 5.64, 1.06, 1, 1, 65, 5.58, -0.14, 1, 1, 66, -7.02, 6.87, 1, 2, 65, 5.32, -5.04, 0.51288, 66, -3.35, 7.41, 0.48712, 2, 65, 5, -11.14, 0.04829, 66, 2.69, 8.28, 0.95171, 1, 66, 14.78, 10.03, 1, 1, 66, 25.85, 11.63, 1, 2, 67, -26.57, 10.4, 0.00148, 66, 39.69, 13.63, 0.99852, 1, 67, 58.92, 29.6, 1, 2, 68, 37.15, 32.05, 0.81817, 75, -37.61, 30.47, 0.18183, 2, 75, 61.13, 7.69, 0.00589, 76, 11.44, 13.04, 0.99411, 2, 74, 31.99, 36.24, 0.04089, 76, 44.74, 6.1, 0.95911, 2, 74, 39.08, 30.79, 0.10545, 76, 51.49, 0.23, 0.89455, 2, 70, 46.47, 7.42, 0.99143, 72, 29.87, -52.67, 0.00857, 1, 70, 52.37, -6.84, 1, 1, 69, 37.95, -9.93, 1, 2, 68, 65.92, -15.1, 0.74563, 69, -0.23, -20.19, 0.25437, 2, 67, 129.14, -21.88, 0.6518, 68, -3.52, -26.94, 0.3482, 2, 67, 102.89, -3.95, 0.99996, 68, -35.29, -25.64, 4e-05, 1, 67, 75.55, -35.29, 1, 1, 67, 56.88, -9.38, 1, 1, 67, 39.73, -8.85, 1, 1, 66, 40.57, -8.31, 1, 1, 66, 29.75, -7.91, 1, 1, 66, 17.6, -7.46, 1, 1, 66, 3.3, -6.93, 1, 2, 65, -8.88, -3.89, 0.24779, 66, -1.71, -6.75, 0.75221, 1, 66, -9.7, -6.45, 1, 1, 65, -6.63, 5.58, 1], "hull": 33, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 56, 18, 54, 56, 18, 20, 54, 20, 50, 52, 52, 54, 20, 22, 22, 24, 52, 22, 16, 18, 60, 16, 56, 58, 58, 60, 58, 16, 14, 16, 60, 14, 60, 62, 62, 64, 12, 14, 62, 12, 0, 64, 0, 8, 8, 10, 10, 12, 64, 10, 0, 2, 2, 4, 4, 6, 6, 8, 2, 6], "width": 221, "height": 361}}, "fringe left right2": {"fringe left right": {"type": "mesh", "uvs": [0.80298, 0, 0.9924, 0.17667, 0.81578, 0.22553, 0.81726, 0.28418, 0.83737, 0.59222, 0.67851, 0.80149, 0.472, 0.95892, 0.27134, 1, 0.26907, 1, 0.06247, 0.9822, 0, 0.86762, 0, 0.83646, 0.14579, 0.77323, 0.28513, 0.78701, 0.4451, 0.75193, 0.54271, 0.71004, 0.59994, 0.65658, 0.61848, 0.3555, 0.66548, 0.25, 0.53554, 0.21164, 0.53566, 0.1588, 0.73558, 0.01481], "triangles": [6, 7, 13, 8, 13, 7, 9, 10, 8, 12, 13, 11, 6, 14, 5, 13, 8, 10, 13, 14, 6, 14, 15, 5, 15, 16, 5, 5, 16, 4, 21, 0, 1, 13, 10, 11, 16, 17, 4, 17, 3, 4, 17, 18, 3, 18, 2, 3, 2, 18, 20, 21, 1, 2, 20, 18, 19, 2, 20, 21], "vertices": [1, 77, -8.94, 2.31, 1, 1, 78, 35.23, 30.06, 1, 1, 78, 50.51, 9.36, 1, 2, 78, 66.99, 10.71, 0.66367, 79, -5.43, 11.77, 0.33633, 2, 79, 81.32, 16.81, 0.75728, 80, -15.11, 10.73, 0.24272, 4, 80, 46.47, 19.26, 0.75908, 83, -24.93, 5.12, 0.09343, 85, -26.25, -4.28, 0.09055, 87, -27.22, -2.71, 0.05695, 2, 87, 22.32, 10.03, 0.99949, 88, -13.38, 3.43, 0.00051, 1, 88, 13.34, 8.87, 1, 2, 87, 48.02, 0.21, 2e-05, 88, 13.61, 8.81, 0.99998, 2, 88, 37.12, -2.06, 0.00305, 89, 11.54, 4.48, 0.99695, 2, 84, 33.48, 9.92, 0.43266, 86, 22.42, -8.02, 0.56734, 3, 84, 33.48, 1.13, 0.74972, 82, 33.61, 6.98, 0.00058, 86, 23.36, -16.75, 0.2497, 2, 84, 15.54, -16.7, 0.00036, 82, 14.14, -9.15, 0.99964, 1, 81, 22.87, -3.54, 1, 2, 80, 46.61, -12.67, 0.26167, 81, 1.61, -9.28, 0.73833, 2, 80, 30.7, -7.12, 0.97817, 81, -12.53, -18.44, 0.02183, 2, 79, 100.33, -11.84, 0.04152, 80, 14.07, -7.46, 0.95848, 1, 79, 15.39, -12.08, 1, 2, 78, 58.7, -8.59, 0.98588, 79, -14.52, -7.18, 0.01412, 1, 78, 49.03, -25.3, 1, 1, 78, 34.17, -26.34, 1, 1, 77, -3.23, -5.01, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 123, "height": 282}}, "fringe right left2": {"fringe right left": {"type": "mesh", "uvs": [0.22231, 0, 0.23661, 0.02414, 0.25021, 0.0471, 0.3316, 0.1845, 0.16725, 0.27129, 0.2266, 0.30782, 0.24942, 0.47945, 0.30773, 0.52074, 0.52779, 0.69183, 0.68961, 0.78467, 0.90036, 0.84179, 1, 0.98958, 0.99183, 1, 0.52043, 0.89427, 0.21796, 0.75284, 0.13585, 0.61244, 0.126, 0.47567, 0.11093, 0.39289, 0, 0.21543, 0, 0.20444, 0.05629, 0.16309, 0.16384, 0.04786, 0.18688, 0.02317, 0.20851, 0], "triangles": [12, 13, 10, 12, 10, 11, 13, 9, 10, 14, 15, 8, 6, 17, 5, 6, 16, 17, 17, 4, 5, 17, 18, 4, 18, 20, 4, 4, 20, 3, 3, 21, 2, 21, 3, 20, 18, 19, 20, 2, 22, 1, 2, 21, 22, 22, 23, 1, 13, 8, 9, 13, 14, 8, 15, 7, 8, 7, 16, 6, 7, 15, 16, 23, 0, 1], "vertices": [1, 90, 2.06, 3.8, 1, 2, 90, 4.29, -3.66, 0.19271, 91, -1.45, 3.63, 0.80729, 1, 91, 4.93, 7.4, 1, 2, 91, 43.08, 29.95, 0.78832, 92, -9.54, 33.69, 0.21168, 1, 92, 14.09, 5.08, 1, 1, 92, 26.38, 12.95, 1, 2, 93, 2.4, 9.34, 0.65233, 92, 79.47, 10.27, 0.34767, 1, 93, 18.06, 9.8, 1, 1, 93, 81.09, 8.94, 1, 2, 93, 118.97, 13.98, 0.14899, 94, 0.27, 10.14, 0.85101, 1, 94, 37.32, 5.69, 1, 1, 96, 50.34, 1.97, 1, 1, 96, 51.48, -1.3, 1, 4, 98, 55.2, -6.14, 0.36356, 93, 132.45, -26.79, 0.20874, 95, -17.77, -20.87, 0.32987, 96, -25.53, -24.64, 0.09782, 1, 97, 62.93, -6.77, 1, 1, 97, 17.77, -9.46, 1, 3, 93, -9.27, -6.02, 0.04498, 92, 76.05, -8.71, 0.92115, 97, -23.75, -1.42, 0.03386, 1, 92, 50.37, -8.05, 1, 2, 91, 64.81, -17.95, 0.78736, 92, -6.11, -18.81, 0.21264, 2, 91, 61.51, -18.77, 0.85975, 92, -9.48, -18.41, 0.14025, 1, 91, 47, -13.33, 1, 1, 91, 8.4, -5.62, 1, 2, 90, -3.46, -3.37, 0.24876, 91, 0.13, -3.97, 0.75124, 1, 90, -0.09, 3.8, 1], "hull": 24, "edges": [0, 46, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 0, 2, 44, 2, 40, 42, 42, 44, 2, 4, 4, 6, 42, 4], "width": 156, "height": 309}}, "fringe right right2": {"fringe right right": {"type": "mesh", "uvs": [0, 0, 0.01244, 0, 0.06703, 0.01118, 0.07655, 0.04405, 0.11155, 0.16499, 0.12599, 0.20592, 0.18508, 0.20626, 0.18129, 0.2871, 0.17161, 0.33227, 0.30012, 0.40384, 0.36298, 0.5572, 0.49062, 0.68018, 0.68794, 0.77593, 0.78736, 0.77062, 0.95028, 0.8101, 1, 0.81925, 1, 0.85275, 0.75483, 0.94734, 0.52737, 0.99967, 0.35703, 0.90218, 0.26944, 0.78603, 0.18043, 0.45319, 0.13304, 0.37734, 0.06252, 0.37481, 0.05096, 0.28663, 0.07607, 0.24373, 0.00584, 0.04879, 0, 0.03258], "triangles": [14, 15, 16, 17, 12, 13, 17, 13, 14, 18, 19, 12, 17, 14, 16, 17, 18, 12, 19, 11, 12, 20, 10, 11, 19, 20, 11, 20, 21, 10, 21, 22, 9, 21, 9, 10, 7, 5, 6, 25, 5, 7, 24, 25, 7, 8, 24, 7, 23, 24, 8, 22, 23, 8, 22, 8, 9, 27, 1, 2, 26, 27, 2, 3, 26, 2, 4, 25, 26, 4, 26, 3, 25, 4, 5, 27, 0, 1], "vertices": [1, 99, -6.32, 3.6, 1, 1, 99, -2.91, 3.6, 1, 1, 100, -1.83, 11.69, 1, 2, 99, 14.65, -11.77, 2e-05, 100, 9.92, 11, 0.99998, 1, 100, 53.13, 8.48, 1, 2, 100, 67.95, 8.31, 0.66186, 101, 1.37, 9.63, 0.33814, 2, 100, 72.57, 23.83, 0.28931, 101, 6.01, 25.14, 0.71069, 2, 100, 99.38, 14.98, 0.0225, 101, 32.8, 16.27, 0.9775, 2, 101, 47.2, 9.32, 0.91864, 102, -20.72, 4.52, 0.08136, 2, 102, 19.33, 20.64, 0.97224, 103, -14.26, 20.64, 0.02776, 2, 103, 40.08, 6.19, 0.01075, 104, 6.5, 6.19, 0.98925, 2, 105, 27.93, 12.42, 0.74503, 106, -5.66, 12.42, 0.25497, 3, 106, 51.68, 39.83, 0.06009, 107, 18.1, 39.83, 0.55892, 108, -15.49, 39.83, 0.38099, 3, 106, 64.85, 63.75, 0.00029, 107, 31.27, 63.75, 0.26478, 108, -2.32, 63.75, 0.73494, 2, 107, 66.99, 93.86, 0.08264, 108, 33.41, 93.86, 0.91736, 2, 107, 77.04, 103.59, 0.07386, 108, 43.46, 103.59, 0.92614, 2, 107, 86.88, 97.27, 0.07349, 108, 53.29, 97.27, 0.92651, 2, 107, 78.34, 22.91, 0.02575, 108, 44.75, 22.91, 0.97425, 3, 106, 93.59, -39.39, 0.01173, 107, 60, -39.39, 0.19019, 108, 26.42, -39.39, 0.79808, 5, 104, 106.91, -60.27, 0.00408, 105, 73.32, -60.27, 0.08888, 106, 39.74, -60.27, 0.36319, 107, 6.15, -60.27, 0.46177, 108, -27.43, -60.27, 0.08208, 5, 103, 93.41, -58.54, 6e-05, 104, 59.83, -58.54, 0.13489, 105, 26.24, -58.54, 0.44361, 106, -7.34, -58.54, 0.32987, 107, -40.93, -58.54, 0.09156, 3, 101, 88.4, -0.15, 0.00518, 102, 16.09, -16.26, 0.9497, 103, -17.49, -16.26, 0.04512, 1, 101, 59.35, -5.23, 1, 1, 101, 53.11, -23.53, 1, 1, 101, 22.67, -17.98, 1, 1, 101, 10.22, -7.19, 1, 1, 100, 6.12, -8.07, 1, 1, 100, 0.24, -8.03, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 4, 50, 52, 52, 54, 4, 6, 6, 8, 52, 6], "width": 274, "height": 349}}, "hair bond left": {"hair bond left": {"type": "mesh", "uvs": [0, 0.00243, 0.06728, 0.0083, 0.09147, 0.03733, 0.11567, 0.06636, 0.13987, 0.09539, 0.16406, 0.12442, 0.26085, 0.24054, 0.32039, 0.35583, 0.37994, 0.47111, 0.51567, 0.62151, 0.57758, 0.66281, 0.63949, 0.70411, 0.78116, 0.75852, 0.852, 0.78572, 0.92283, 0.81293, 0.99718, 0.97616, 1, 1, 0.99446, 1, 0.90389, 0.94301, 0.81331, 0.88601, 0.73924, 0.8394, 0.65028, 0.78342, 0.54663, 0.71819, 0.39711, 0.52733, 0.24758, 0.33647, 0.16871, 0.2358, 0.10943, 0.16013, 0.08208, 0.12521, 0.05472, 0.09029, 0.02736, 0.05536, 0, 0.02044], "triangles": [17, 15, 16, 17, 18, 15, 18, 14, 15, 18, 19, 14, 19, 20, 13, 19, 13, 14, 13, 20, 12, 20, 21, 12, 22, 11, 21, 21, 11, 12, 23, 9, 22, 22, 10, 11, 22, 9, 10, 23, 24, 8, 24, 7, 8, 9, 23, 8, 24, 6, 7, 24, 25, 6, 26, 5, 25, 6, 25, 5, 26, 4, 5, 26, 27, 4, 27, 3, 4, 27, 28, 3, 28, 2, 3, 28, 29, 2, 29, 1, 2, 29, 30, 1, 30, 0, 1], "vertices": [2, 59, 19.68, 23.84, 1, 62, -389.7, 84.8, 0, 2, 59, 14.41, -8.93, 1, 62, -363.23, 104.82, 0, 1, 59, -2.21, -19.79, 1, 1, 59, -18.82, -30.66, 1, 1, 59, -35.44, -41.52, 1, 2, 59, -52.06, -52.39, 0.03454, 60, -4.65, 16.02, 0.96546, 1, 60, 74.66, 20.26, 1, 2, 60, 143.44, 9.55, 0.00051, 61, 14.51, 11.71, 0.99949, 1, 61, 83.39, 1.65, 1, 1, 62, 26.39, 6.86, 1, 1, 62, 64.07, 10.81, 1, 2, 62, 101.77, 14.77, 0.98745, 63, -37.94, 18.41, 0.01255, 1, 63, 37.59, 23.2, 1, 2, 63, 75.35, 25.6, 0.90124, 64, -30.94, 20.72, 0.09876, 2, 63, 113.12, 28, 0.04188, 64, 6.81, 23.35, 0.95812, 1, 64, 80.15, -39.55, 1, 2, 63, 193.21, -46.37, 1e-05, 64, 87.34, -50.54, 0.99999, 2, 63, 190.78, -47.6, 1e-05, 64, 84.92, -51.78, 0.99999, 2, 63, 137, -39.81, 0.16166, 64, 31.1, -44.31, 0.83834, 2, 63, 83.22, -32.01, 0.98687, 64, -22.73, -36.84, 0.01313, 1, 63, 39.23, -25.64, 1, 2, 62, 135.11, -13.6, 0.69451, 63, -13.59, -17.98, 0.30549, 1, 62, 73.46, -21.8, 1, 1, 61, 113.8, -8.13, 1, 3, 60, 114.79, -14.32, 0.83903, 61, -13.92, -12.43, 0.16097, 62, -176.27, 32.55, 0, 2, 60, 47.4, -15.93, 1, 62, -242.13, 46.88, 0, 3, 59, -69.92, -24.4, 0.03475, 60, -3.24, -17.15, 0.96525, 62, -291.63, 57.65, 0, 1, 59, -49.98, -12.18, 1, 1, 59, -30.04, 0.03, 1, 2, 59, -10.1, 12.24, 0.97347, 60, -73.37, -18.84, 0.02653, 2, 59, 9.83, 24.46, 1, 62, -383.02, 77.54, 0], "hull": 31, "edges": [0, 60, 0, 2, 16, 18, 28, 30, 30, 32, 32, 34, 10, 12, 52, 10, 50, 52, 50, 12, 48, 50, 12, 14, 14, 16, 48, 14, 44, 46, 46, 48, 18, 20, 20, 22, 20, 44, 42, 44, 22, 42, 22, 24, 40, 42, 24, 40, 24, 26, 26, 28, 38, 40, 26, 38, 34, 36, 36, 38, 28, 36, 56, 6, 56, 58, 58, 60, 2, 4, 4, 6, 58, 4, 52, 54, 54, 56, 6, 8, 8, 10, 54, 8], "width": 491, "height": 548}}, "hairbond right": {"hairbond right": {"type": "mesh", "uvs": [0.11733, 0.1078, 0.17584, 0.15694, 0.22861, 0.18734, 0.28137, 0.21773, 0.32085, 0.24178, 0.36, 0.26563, 0.42377, 0.30447, 0.51183, 0.35812, 0.5999, 0.41177, 0.87061, 0.69229, 1, 0.93787, 1, 0.9602, 0.97683, 1, 0.97349, 1, 0.83527, 0.81772, 0.73902, 0.60838, 0.6133, 0.44877, 0.45798, 0.34229, 0.46293, 0.34747, 0.41028, 0.3188, 0.35764, 0.29014, 0.30499, 0.26147, 0.25235, 0.2328, 0.21153, 0.21057, 0.17071, 0.18834, 0.11696, 0.15907, 0.04354, 0.08311, 0, 0.03806, 0, 0.02721, 0.00826, 0, 0.03354, 0.02933, 0.05882, 0.05867], "triangles": [27, 30, 26, 26, 30, 31, 27, 28, 30, 28, 29, 30, 26, 31, 0, 12, 13, 11, 11, 13, 10, 13, 14, 10, 14, 9, 10, 14, 15, 9, 8, 9, 15, 16, 17, 7, 7, 8, 16, 8, 15, 16, 19, 17, 18, 17, 6, 7, 19, 6, 17, 19, 20, 6, 20, 5, 6, 20, 21, 5, 5, 21, 4, 22, 3, 21, 21, 3, 4, 22, 2, 3, 23, 2, 22, 23, 24, 2, 24, 1, 2, 24, 25, 1, 25, 0, 1, 25, 26, 0], "vertices": [1, 52, 22.5, 6.67, 1, 1, 52, 72.88, 3.75, 1, 1, 52, 112.98, 8.45, 1, 1, 53, 11.66, 7.83, 1, 1, 53, 42.25, 6.29, 1, 1, 53, 72.59, 4.77, 1, 1, 53, 122.02, 2.29, 1, 2, 53, 190.27, -1.14, 0.00026, 54, 15.54, 8.66, 0.99974, 1, 54, 83.04, 19.33, 1, 2, 55, 161.8, 32.92, 0.77949, 56, -21.08, 30.47, 0.22051, 2, 57, 58.9, 17.81, 0.94567, 58, -10.05, 17.87, 0.05433, 2, 57, 70.79, 9.45, 0.34794, 58, 2.33, 10.25, 0.65206, 2, 57, 83.08, -18.1, 0.00328, 58, 16.29, -16.49, 0.99672, 1, 58, 15.13, -18.39, 1, 1, 56, 38.27, -30.37, 1, 1, 55, 65.27, -4.08, 1, 1, 54, 105.31, 6.51, 1, 2, 53, 153.69, -8.82, 0.95508, 54, -18.68, -6.37, 0.04492, 2, 53, 158.19, -10.25, 0.93001, 54, -13.98, -6.85, 0.06999, 1, 53, 118.43, -10.16, 1, 1, 53, 78.66, -10.07, 1, 1, 53, 38.89, -9.98, 1, 2, 52, 143.19, -6.18, 0.14041, 53, -0.87, -9.89, 0.85959, 2, 52, 112.66, -10.49, 0.99851, 53, -31.71, -9.82, 0.00149, 2, 52, 82.13, -14.8, 0.99999, 53, -62.54, -9.75, 1e-05, 1, 52, 41.93, -20.47, 1, 1, 51, 34.94, -6.2, 1, 2, 52, -67.49, -2.65, 0.00602, 51, -5.25, -15.56, 0.99398, 1, 51, -11.27, -11.86, 1, 1, 51, -23.47, 2.11, 1, 1, 51, 1.63, 6.47, 1, 1, 51, 26.73, 10.82, 1], "hull": 32, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 54, 56, 56, 58, 50, 52, 52, 54, 62, 52, 2, 0, 0, 62, 0, 50, 48, 50, 2, 48, 6, 44, 44, 46, 46, 48, 2, 4, 4, 6, 46, 4, 58, 60, 60, 62, 60, 54, 60, 52, 6, 8, 8, 10, 10, 40, 40, 42, 42, 44, 8, 42, 10, 12, 36, 38, 38, 40, 12, 38, 12, 14, 14, 16], "width": 667, "height": 651}}, "hair left": {"hair left": {"type": "mesh", "uvs": [0.72811, 0.0184, 0.84382, 0.0858, 0.91923, 0.20485, 0.95545, 0.29852, 0.96793, 0.33079, 0.92682, 0.33152, 0.8857, 0.33225, 0.80346, 0.33372, 0.75551, 0.33457, 0.82132, 0.42679, 0.96097, 0.45104, 0.94057, 0.56268, 0.99081, 0.58353, 0.82714, 0.66298, 0.59994, 0.67611, 0.75566, 0.78604, 0.72099, 0.84844, 0.4397, 0.91128, 0.23677, 0.88799, 0.40242, 1, 0.39125, 1, 0.33934, 0.98574, 0.26074, 0.96209, 0.20469, 0.93295, 0.06086, 0.74663, 0.014, 0.54258, 0.02666, 0.42974, 0.03083, 0.39261, 0.08223, 0.28705, 0.13363, 0.18149, 0.19334, 0.11789, 0.35596, 0.06871, 0.55395, 0.02419, 0.74993, 0.30248, 0.75418, 0.32024], "triangles": [19, 20, 21, 18, 19, 21, 23, 18, 22, 18, 21, 22, 23, 24, 18, 17, 18, 14, 18, 24, 14, 24, 25, 14, 25, 26, 14, 27, 8, 26, 16, 17, 14, 16, 14, 15, 13, 14, 11, 13, 11, 12, 11, 9, 10, 14, 26, 9, 11, 14, 9, 26, 8, 9, 8, 34, 7, 28, 8, 27, 7, 34, 6, 6, 34, 5, 5, 34, 4, 34, 3, 4, 28, 29, 33, 33, 2, 3, 33, 29, 2, 2, 29, 31, 29, 30, 31, 31, 1, 2, 31, 32, 1, 32, 0, 1, 34, 8, 28, 34, 33, 3, 28, 33, 34], "vertices": [1, 109, -33.73, 88.3, 1, 1, 109, -54.06, 72.18, 1, 1, 109, -88.74, 63.01, 1, 1, 109, -115.82, 59.26, 1, 1, 109, -125.15, 57.97, 1, 1, 109, -124.97, 64.14, 1, 1, 109, -124.8, 70.31, 1, 1, 109, -124.44, 82.64, 1, 1, 109, -124.24, 89.84, 1, 4, 111, 36.91, 5.15, 0.00133, 112, 2.18, 3.3, 0.98453, 113, -12.35, 6.55, 0.01413, 129, -12.11, 142.08, 0, 1, 113, 9.61, 4.43, 1, 5, 112, 36.69, -21.98, 0.0035, 113, 13.71, -27.39, 0.04677, 115, 31.47, 5.89, 0.38031, 116, 8.81, 5.18, 0.56942, 127, 15.96, 132.87, 0, 4, 116, 18.27, 3.48, 0.99995, 118, 38.61, 35.53, 0, 119, 26.66, 27.92, 5e-05, 127, 21.46, 140.75, 0, 8, 116, 7.36, -28.14, 0.00029, 119, 15.82, -3.73, 0.96426, 121, 42.69, 12.32, 0.00031, 122, 18.58, 10.22, 0.0349, 125, 29.01, 46.1, 0.00023, 126, -5.59, 46.13, 0, 127, 45.59, 117.57, 0, 129, 44.74, 105.59, 0, 6, 119, -12.41, -23.19, 0.00103, 121, 21.25, -14.43, 0.28258, 122, -5.84, -13.85, 0.18262, 123, 74.35, 29.14, 0.00054, 125, 15.37, 14.65, 0.52029, 126, -19.42, 14.76, 0.01294, 7, 119, 23.07, -39.8, 2e-05, 121, 60, -20.15, 9e-05, 122, 31.97, -24.05, 0.00012, 125, 54.25, 19.33, 0.04246, 126, 19.49, 19.22, 0.94983, 129, 68.21, 77.26, 0, 136, 35.88, 28.33, 0.00748, 4, 119, 26.95, -57.98, 0, 126, 32.33, 5.78, 0.68008, 129, 80.24, 63.09, 0, 136, 37.05, 9.77, 0.31992, 4, 129, 72, 17.97, 0, 132, 23.76, 26.71, 0.14162, 135, 30.27, -19.46, 0.54186, 136, 3.46, -21.46, 0.31652, 3, 132, 2.7, 3.75, 0.91598, 134, 64.36, -20.49, 0.00373, 135, 0.74, -29.41, 0.08029, 2, 132, 42.89, 9.13, 3e-05, 133, 18.89, -1.14, 0.99997, 3, 129, 89.18, -2.07, 0, 132, 42.04, 7.68, 2e-05, 133, 17.49, -2.07, 0.99998, 2, 129, 81.48, -6.32, 0, 133, 8.75, -3, 1, 2, 129, 69.34, -12.44, 0, 132, 22.83, -3.8, 1, 1, 132, 11.4, -6.88, 1, 2, 128, 35.62, -2.62, 0.13975, 129, 1.4, -3.52, 0.86025, 1, 127, 18.38, -6.21, 1, 3, 110, 104.51, -58.79, 0.10614, 123, 4.78, -57.57, 0.06312, 127, -13.95, -6.21, 0.83074, 3, 110, 93.88, -58.4, 0.23312, 123, -5.85, -57.06, 0.11003, 127, -24.59, -6.21, 0.65685, 5, 109, -104.38, 189.79, 0.54286, 110, 63.52, -51.36, 0.30515, 123, -36.11, -49.66, 0.03541, 127, -55.18, -0.29, 0.11658, 129, -106.54, 71.56, 0, 5, 109, -74.72, 180.21, 0.46286, 110, 33.17, -44.32, 0.4974, 123, -66.38, -42.26, 0.00442, 127, -85.77, 5.64, 0.03532, 129, -127.49, 94.63, 0, 1, 109, -57.13, 170.14, 1, 1, 109, -44.61, 144.91, 1, 1, 109, -33.76, 114.48, 1, 2, 109, -115.03, 90.1, 0.93143, 111, 0.58, 12.83, 0.06857, 2, 109, -120.14, 89.78, 0.93143, 111, 5.34, 10.95, 0.06857], "hull": 33, "edges": [0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 58, 60, 60, 62, 62, 64, 62, 2, 58, 4, 54, 56, 56, 58, 50, 52, 52, 54, 52, 18, 64, 0, 14, 16, 12, 14, 8, 10, 10, 12, 4, 6, 6, 8, 66, 6, 8, 68, 68, 56, 66, 68, 68, 16], "width": 150, "height": 286}}, "hair right": {"hair right": {"type": "mesh", "uvs": [0.54276, 0.17166, 0.61268, 0.29588, 0.66588, 0.35077, 0.76018, 0.39406, 0.77547, 0.49275, 0.85631, 0.52549, 0.96765, 0.44253, 0.98582, 0.4277, 0.99731, 0.51367, 0.77542, 0.77816, 0.79664, 0.82154, 0.70205, 1, 0.69727, 1, 0.67965, 0.9886, 0.77388, 0.83078, 0.73282, 0.79698, 0.68395, 0.62992, 0.65806, 0.58277, 0.32329, 0.70966, 0.16774, 0.58983, 0.12232, 0.37171, 0.10235, 0.38835, 0.05601, 0.39524, 0.04971, 0.36313, 0.02758, 0.25042, 0.02723, 0.08087, 0.05184, 0.04284, 0.17691, 0.05706, 0.23309, 0.01441, 0.35922, 0.00034, 0.13805, 0.33618], "triangles": [16, 17, 4, 6, 7, 8, 5, 6, 8, 9, 5, 8, 5, 16, 4, 4, 17, 2, 4, 2, 3, 18, 1, 17, 1, 18, 19, 17, 1, 2, 1, 19, 0, 15, 16, 9, 14, 15, 9, 14, 9, 10, 11, 12, 13, 14, 11, 13, 11, 14, 10, 20, 30, 19, 27, 0, 30, 23, 24, 30, 30, 24, 27, 27, 24, 25, 27, 25, 26, 27, 28, 29, 29, 0, 27, 20, 23, 30, 21, 23, 20, 22, 23, 21, 0, 19, 30, 5, 9, 16], "vertices": [3, 137, 99.04, 50.35, 0.88177, 138, -24.4, 50.35, 0.11613, 148, -81.99, 57.63, 0.0021, 3, 137, 138.5, 39.49, 0.22256, 138, 15.05, 39.49, 0.66357, 148, -46.93, 36.53, 0.11387, 3, 137, 161.41, 39.43, 0.01447, 138, 37.97, 39.43, 0.56442, 148, -24.88, 30.29, 0.4211, 2, 138, 68.9, 50.53, 0.05711, 148, 7.9, 32.63, 0.94289, 2, 148, 23.93, 10.28, 0.92749, 149, -7.72, 16.65, 0.07251, 3, 148, 51.52, 13.37, 0.0048, 149, 17.51, 5.07, 0.96015, 150, -5.84, 10.3, 0.03506, 1, 150, 36.39, 3.79, 1, 1, 150, 43.52, 2.99, 1, 2, 150, 30.91, -17.26, 0.9981, 153, 57.2, 36.25, 0.0019, 1, 109, -242.96, -165.94, 1, 1, 109, -255.25, -172.08, 1, 1, 109, -302.13, -138.35, 1, 1, 109, -302.04, -136.8, 1, 1, 109, -298.56, -131.28, 1, 1, 109, -257.32, -164.54, 1, 1, 109, -247.24, -151.8, 1, 4, 141, 37.29, -28.95, 0.02926, 144, 63.65, -10.11, 0.00012, 150, -66.91, 25.37, 0, 151, 15.84, -12.5, 0.97062, 4, 138, 77.65, -10.29, 0.05534, 141, 30.36, -15.19, 0.26688, 144, 56.41, 3.5, 0.01125, 151, 0.51, -10.83, 0.66653, 2, 146, 30.96, -11.64, 0.04163, 147, 10.11, -14.55, 0.95837, 2, 146, -29.28, -13.73, 0.06065, 145, 15.04, -22.61, 0.93935, 1, 109, -118.56, 38.96, 1, 1, 109, -122.71, 45.72, 1, 1, 109, -123.65, 60.87, 1, 1, 109, -114.75, 62.37, 1, 1, 109, -83.48, 67.62, 1, 1, 109, -37.1, 64.83, 1, 1, 109, -27.2, 56.2, 1, 1, 109, -33.63, 15.87, 1, 1, 109, -23.1, -3.08, 1, 1, 109, -21.81, -44.23, 1, 1, 109, -109.17, 33.25, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 44, 46, 46, 48, 46, 60, 60, 40, 28, 20], "width": 325, "height": 274}}, "hand left": {"hand left": {"x": 233.48, "y": -21.17, "rotation": -76.69, "width": 287, "height": 578}}, "head top": {"head top": {"x": 388.75, "y": -64, "rotation": -93.58, "width": 367, "height": 620}}, "left close": {"left close": {"x": 799, "y": -554.5, "width": 100, "height": 53}}, "left eye": {"left eye": {"x": 157.92, "y": 66.15, "rotation": -93.58, "width": 76, "height": 55}}, "left eyebrow": {"left eyebrow": {"x": 183.59, "y": 68.05, "rotation": -93.58, "width": 75, "height": 18}}, "left half close": {"left half close": {"x": 787.5, "y": -555, "width": 77, "height": 54}}, "mouth": {"mouth": {"x": 49.29, "y": 3.3, "rotation": -93.58, "width": 81, "height": 52}}, "ponytail": {"ponytail": {"type": "mesh", "uvs": [0.6245, 0.00285, 0.66223, 0.01029, 0.67318, 0.01651, 0.69361, 0.02999, 0.74007, 0.06063, 0.77732, 0.0852, 0.8115, 0.10775, 0.85675, 0.1376, 0.89814, 0.1649, 0.93953, 0.1922, 0.97091, 0.21894, 0.97847, 0.23442, 0.98923, 0.25646, 1, 0.2785, 1, 0.30326, 0.99503, 0.31889, 0.99042, 0.33341, 0.98376, 0.35436, 0.97551, 0.36715, 0.95108, 0.39621, 0.93567, 0.41454, 0.92131, 0.43163, 0.90424, 0.45193, 0.88584, 0.47382, 0.85137, 0.51482, 0.8169, 0.55582, 0.80512, 0.5592, 0.75913, 0.58579, 0.72456, 0.59514, 0.61754, 0.65546, 0.56403, 0.68563, 0.51052, 0.71579, 0.50039, 0.72082, 0.44466, 0.74603, 0.40471, 0.76411, 0.36475, 0.78219, 0.28485, 0.81834, 0.25226, 0.85821, 0.2597, 0.86442, 0.219, 0.85154, 0.20505, 0.91937, 0.24529, 0.93496, 0.17088, 0.91995, 0.18809, 0.94966, 0.16289, 0.94191, 0.14591, 0.85677, 0.06473, 0.924, 0.07756, 1, 0.06976, 1, 0.04072, 0.99596, 0.02373, 0.98831, 0.01312, 0.97893, 0.00873, 0.97636, 0, 0.94603, 0, 0.94122, 0.01271, 0.92376, 0.03107, 0.90194, 0.08847, 0.86582, 0.13556, 0.83175, 0.18264, 0.79768, 0.23437, 0.76024, 0.23361, 0.75758, 0.28539, 0.74342, 0.33717, 0.72926, 0.44073, 0.70094, 0.4997, 0.66364, 0.55867, 0.62634, 0.61521, 0.59057, 0.68797, 0.54455, 0.71101, 0.53005, 0.67078, 0.51089, 0.72631, 0.48544, 0.78184, 0.45999, 0.80171, 0.43929, 0.82157, 0.41859, 0.83426, 0.40537, 0.85324, 0.3856, 0.86526, 0.37307, 0.87842, 0.35078, 0.89158, 0.3285, 0.90322, 0.30878, 0.90368, 0.29085, 0.90415, 0.27291, 0.90508, 0.23704, 0.90566, 0.21482, 0.88123, 0.19152, 0.85681, 0.16822, 0.8423, 0.15437, 0.77461, 0.12505, 0.70692, 0.09573, 0.65819, 0.07304, 0.60945, 0.05035, 0.59338, 0.05203, 0.58479, 0.02012, 0.5995, 0.00769, 0.61466, 0.00056], "triangles": [39, 37, 38, 37, 39, 36, 36, 60, 35, 60, 62, 35, 51, 47, 48, 50, 51, 48, 48, 49, 50, 51, 52, 46, 47, 51, 46, 46, 52, 53, 54, 55, 46, 46, 53, 54, 55, 56, 46, 57, 46, 56, 45, 46, 57, 57, 58, 45, 45, 58, 39, 58, 59, 39, 39, 59, 36, 59, 60, 36, 60, 61, 62, 43, 44, 42, 42, 44, 45, 42, 40, 41, 45, 40, 42, 39, 40, 45, 33, 64, 32, 32, 64, 31, 31, 64, 30, 64, 65, 30, 30, 65, 29, 65, 66, 29, 66, 67, 29, 29, 67, 28, 27, 28, 68, 28, 67, 68, 27, 68, 26, 68, 69, 26, 26, 69, 25, 71, 24, 70, 70, 24, 25, 25, 69, 70, 71, 72, 24, 24, 72, 23, 72, 73, 23, 23, 73, 22, 73, 74, 22, 22, 74, 21, 74, 75, 21, 21, 75, 20, 75, 76, 20, 20, 76, 19, 76, 77, 19, 19, 77, 18, 77, 78, 18, 18, 78, 17, 78, 79, 17, 17, 79, 16, 16, 79, 15, 79, 80, 15, 15, 80, 14, 80, 81, 14, 81, 13, 14, 81, 82, 13, 82, 12, 13, 82, 83, 12, 83, 11, 12, 83, 10, 11, 83, 84, 10, 85, 86, 8, 86, 87, 8, 87, 7, 8, 87, 88, 7, 84, 9, 10, 84, 85, 9, 85, 8, 9, 35, 62, 34, 62, 63, 34, 34, 63, 33, 63, 64, 33, 88, 6, 7, 88, 5, 6, 88, 89, 5, 89, 4, 5, 89, 90, 4, 90, 91, 3, 92, 93, 91, 91, 93, 2, 91, 2, 3, 1, 93, 94, 1, 94, 0, 0, 94, 95, 1, 2, 93, 90, 3, 4], "vertices": [1, 26, -96.58, 31.3, 1, 1, 26, -39.49, 63.76, 1, 1, 26, -9.9, 64.3, 1, 1, 26, 51.32, 61.22, 1, 4, 26, 190.5, 54.2, 0.79429, 27, 29.67, 48.6, 0.20571, 28, -298.78, 37.21, 0, 30, -651.3, -169.5, 0, 3, 27, 141.31, 43.16, 0.99973, 28, -187, 37.34, 0.00027, 30, -545.95, -132.17, 0, 1, 27, 243.73, 38.18, 1, 2, 29, -147.74, 13.96, 0.00023, 28, 51.28, 37.64, 0.99977, 3, 29, -24.92, 32.24, 0.18631, 28, 175.45, 37.79, 0.81369, 43, -3040.31, 199.34, 0, 4, 29, 97.89, 50.53, 0.98576, 28, 299.61, 37.94, 0.01424, 48, -3055.04, 516.68, 0, 50, -1696.23, 2885.4, 0, 5, 27, 740.79, 1.75, 0, 28, 413.79, 25.86, 0, 30, 24.41, 56.96, 1, 48, -2970.49, 594.35, 0, 50, -1582.84, 2867.44, 0, 4, 27, 797.13, -22.24, 0, 30, 85.65, 56.15, 1, 48, -2916.62, 623.48, 0, 50, -1526.53, 2843.36, 0, 5, 30, 172.85, 54.98, 0.43634, 31, 1.22, 57.01, 0.56366, 43, -2758.69, 458.76, 0, 48, -2839.92, 664.97, 0, 50, -1446.35, 2809.05, 0, 5, 30, 260.05, 53.82, 0.00168, 31, 86.62, 74.67, 0.99832, 43, -2684.69, 504.91, 0, 48, -2763.21, 706.45, 0, 50, -1366.18, 2774.75, 0, 5, 31, 182.71, 75.61, 0.88985, 32, -66.09, 64.66, 0.11015, 43, -2594.87, 539.06, 0, 48, -2671.45, 735, 0, 50, -1286.78, 2720.62, 0, 5, 31, 243.48, 68.44, 0.2835, 32, -4.91, 66.06, 0.7165, 43, -2535.38, 553.38, 0, 48, -2611.19, 745.62, 0, 50, -1241.01, 2680.01, 0, 5, 27, 1125.51, -222.71, 0, 32, 51.89, 67.36, 1, 43, -2480.14, 566.67, 0, 48, -2555.23, 755.47, 0, 50, -1198.51, 2642.3, 0, 5, 27, 1186.93, -277.01, 0, 32, 133.84, 69.23, 1, 43, -2400.44, 585.85, 0, 48, -2474.51, 769.69, 0, 50, -1137.19, 2587.9, 0, 5, 27, 1220.76, -315.57, 0, 32, 184.86, 63.91, 1, 43, -2349.44, 591.45, 0, 48, -2423.26, 772.13, 0, 50, -1103.42, 2549.28, 0, 3, 27, 1292.65, -410.57, 0, 33, 23.54, 55.66, 1, 43, -2230.39, 595.86, 0, 3, 27, 1337.96, -470.46, 0, 33, 98.61, 57.79, 1, 43, -2155.34, 598.64, 0, 2, 33, 168.61, 59.77, 1, 43, -2085.36, 601.23, 0, 2, 33, 251.79, 62.13, 1, 43, -2002.2, 604.31, 0, 2, 33, 341.46, 64.68, 0.42297, 34, -11.21, 68.47, 0.57703, 1, 34, 156.17, 83.33, 1, 2, 34, 323.55, 98.19, 0.82473, 35, -50.5, 101.32, 0.17527, 2, 34, 342.97, 86.61, 0.66715, 35, -30.26, 91.25, 0.33285, 1, 35, 94.66, 76.49, 1, 1, 35, 152.13, 45.92, 1, 4, 36, 72.64, 36.74, 1, 43, -1104.41, 466.31, 0, 48, -1188.32, 570.37, 0, 50, -493.87, 1456.47, 0, 4, 36, 215.92, 50.02, 1, 43, -965.23, 429.75, 0, 48, -1051.67, 525.29, 0, 50, -444.24, 1321.41, 0, 4, 36, 359.2, 63.3, 1, 43, -826.06, 393.2, 0, 48, -915.02, 480.22, 0, 50, -394.61, 1186.35, 0, 6, 37, -69.24, 68.04, 0.04284, 36, 384.31, 64.09, 0.95686, 43, -802.2, 385.34, 0, 48, -891.69, 470.9, 0, 50, -387.4, 1162.28, 0, 45, -334.52, 111.05, 0.0003, 6, 37, 59.63, 91.83, 0.94188, 36, 515.34, 62.19, 0.00564, 43, -679.73, 338.71, 0, 48, -772.33, 416.8, 0, 50, -355.59, 1035.15, 0, 45, -205.71, 86.93, 0.05248, 5, 37, 152, 108.89, 0.67133, 43, -591.94, 305.29, 0, 48, -686.77, 378.02, 0, 50, -332.79, 944.03, 0, 45, -113.38, 69.63, 0.32867, 5, 37, 244.38, 125.95, 0.07117, 43, -504.15, 271.87, 0, 48, -601.22, 339.24, 0, 50, -309.98, 852.9, 0, 45, -21.05, 52.34, 0.92883, 2, 46, 18.95, 14.75, 0.82413, 45, 163.6, 17.75, 0.17587, 5, 39, 60.84, 121.47, 0.00033, 43, -165.79, 212.42, 0, 48, -267.18, 259.02, 0, 50, -165.19, 541.36, 0, 47, 83.96, 0.42, 0.99967, 2, 46, 200.61, 37.89, 0.00025, 47, 106.45, 14.9, 0.99975, 6, 38, 340.29, 79.46, 0.14653, 39, 40.77, 66.98, 0.39657, 48, -276.45, 201.69, 0, 50, -215.87, 513, 0, 46, 174.95, -38.83, 0.00825, 47, 64.58, -54.32, 0.44865, 4, 39, 304.86, 74.03, 0.00095, 41, 41.67, 22.66, 0.99905, 48, -18.58, 259.09, 0, 50, -10.62, 346.67, 0, 5, 39, 358.18, 143.15, 0.00014, 40, 214.36, 99.75, 0.58006, 41, 128.88, 18.98, 0.4198, 42, 525.77, 330.61, 0, 43, 116.55, 308.14, 0, 4, 40, 142.52, -8.69, 0.16251, 41, 3.17, -14.43, 0.83749, 42, 538.53, 201.16, 0, 43, 103.43, 178.73, 0, 4, 40, 260.24, 4.14, 0.05619, 41, 99.73, -82.98, 0.94381, 42, 621.72, 285.44, 0, 43, 201.66, 244.86, 0, 4, 40, 225.66, -31.35, 0.03022, 41, 50.31, -86.57, 0.96977, 42, 617.31, 236.09, 0, 43, 187.57, 197.36, 0, 4, 39, 73.42, -44.37, 0.47039, 41, -188.57, 143.4, 1e-05, 42, 351.93, 37.3, 0.52957, 43, -111.93, 55.06, 2e-05, 3, 42, 640.35, 69.22, 0, 43, 177.1, 29.23, 0.15071, 44, 22.09, 26.16, 0.84929, 1, 50, 135.76, 5.7, 1, 2, 49, 214.56, 45.42, 0, 50, 128.89, -4.38, 1, 2, 49, 189.66, 4.36, 0.00032, 50, 90.35, -33.05, 0.99968, 2, 49, 155.05, -15.34, 0.0013, 50, 50.87, -38.27, 0.9987, 2, 49, 115.93, -23.88, 0.24287, 50, 11.42, -31.45, 0.75713, 2, 49, 104.75, -28.47, 0.37451, 50, -0.66, -31.5, 0.62549, 2, 48, 175.47, -16.19, 0.30311, 49, -13.23, -16.99, 0.69689, 1, 48, 157.63, -21.74, 1, 2, 42, 682.94, -0.04, 0, 48, 87.02, -22.89, 1, 2, 42, 595.97, -20.97, 7e-05, 48, -2.4, -20.66, 0.99993, 2, 42, 429.51, -19.89, 0.99985, 48, -162.94, 23.35, 0.00015, 3, 42, 278.38, -28.21, 1, 48, -311.09, 54.33, 0, 50, -352.83, 448.54, 0, 2, 42, 127.25, -36.53, 1, 48, -459.25, 85.31, 0, 5, 36, 771.99, -150.43, 0, 38, 21.12, -76.51, 0.23908, 42, -38.79, -45.67, 0.76092, 48, -622.02, 119.34, 0, 50, -495.17, 732.51, 0, 6, 37, 347.53, -75.31, 0.01867, 36, 764.94, -158.1, 0, 38, 12.75, -82.71, 0.26565, 42, -46.9, -52.2, 0.71568, 48, -631.54, 115.13, 0, 50, -504.39, 737.35, 0, 6, 37, 249.9, -69.01, 0.78005, 36, 670.44, -132.8, 3e-05, 38, -75.26, -39.98, 0.09768, 42, -136.57, -13.07, 0.12224, 48, -708.07, 176.08, 0, 50, -504.21, 835.19, 0, 4, 37, 152.27, -62.71, 0.98868, 36, 575.93, -107.5, 0.00014, 42, -226.24, 26.06, 0.01118, 48, -784.6, 237.03, 0, 2, 37, -42.99, -50.11, 0.26202, 36, 386.92, -56.91, 0.73798, 1, 36, 217.09, -81.86, 1, 3, 35, 381.15, -125.69, 0.10783, 36, 47.26, -106.8, 0.89217, 43, -1177.37, 340.12, 0, 5, 35, 217.03, -113.16, 0.95225, 36, -115.58, -130.72, 0.04775, 43, -1338.57, 373.37, 0, 48, -1427.78, 492.07, 0, 50, -704.05, 1595.38, 0, 5, 34, 364.72, -103.88, 0.19941, 35, 5.85, -97.05, 0.80059, 43, -1546, 416.16, 0, 48, -1632.17, 547.58, 0, 50, -787.6, 1790, 0, 6, 27, 1511.61, -1012.9, 0, 34, 298.73, -93.61, 0.84121, 35, -60.73, -91.81, 0.15879, 43, -1611.38, 429.81, 0, 48, -1696.58, 565.24, 0, 50, -813.8, 1851.44, 0, 5, 27, 1414.73, -1023.12, 0, 34, 255.99, -181.14, 1, 43, -1658.57, 344.59, 0, 48, -1748.94, 483.1, 0, 50, -910.7, 1841.39, 0, 5, 27, 1381.77, -895.81, 0, 34, 130.59, -141.54, 1, 43, -1781.77, 390.6, 0, 48, -1869.07, 536.63, 0, 50, -943.43, 1968.76, 0, 6, 27, 1348.81, -768.49, 0, 33, 347.57, -106.41, 0.22744, 34, 5.18, -101.93, 0.77256, 43, -1904.97, 436.61, 0, 48, -1989.19, 590.15, 0, 50, -976.17, 2096.13, 0, 5, 27, 1299.79, -697.65, 0, 33, 261.43, -105.2, 0.83333, 34, -80.88, -105.91, 0.16667, 48, -2075.15, 595.93, 0, 50, -1025.07, 2167.06, 0, 4, 33, 175.29, -103.99, 0.99234, 34, -166.94, -109.88, 0.00766, 48, -2161.1, 601.71, 0, 50, -1073.97, 2237.98, 0, 4, 32, 364.59, -132.19, 0.00318, 33, 120.27, -103.21, 0.99682, 48, -2216, 605.4, 0, 50, -1105.2, 2283.28, 0, 2, 32, 284.26, -114.34, 0.16191, 33, 38, -102.06, 0.83809, 2, 32, 233.36, -103.03, 0.56924, 33, -14.14, -101.32, 0.43076, 2, 32, 144.75, -95.64, 0.94561, 33, -102.41, -112.09, 0.05439, 3, 31, 282.34, -92.88, 0.06506, 32, 56.13, -88.24, 0.93467, 33, -190.68, -122.85, 0.00026, 2, 31, 205.63, -75.44, 0.66136, 32, -22.27, -81.7, 0.33864, 2, 31, 136, -75.4, 0.99895, 32, -91.21, -91.4, 0.00105, 4, 29, 364.9, -122.49, 0.00207, 30, 207.94, -88.31, 0.04971, 31, 66.38, -75.35, 0.94822, 43, -2651.73, 357.16, 0, 4, 29, 237.23, -66.89, 0.21025, 30, 71.98, -58.2, 0.77905, 31, -72.87, -75.26, 0.01069, 43, -2782.4, 309.02, 0, 2, 29, 158.14, -32.45, 1, 43, -2863.34, 279.2, 0, 2, 29, 59.95, -32.36, 1, 43, -2934.33, 211.37, 0, 1, 28, 152.86, -24.08, 1, 1, 28, 95.14, -15.51, 1, 2, 27, 266.87, -47.25, 1, 48, -3227.39, 193.1, 0, 2, 27, 113.27, -70.81, 1, 48, -3304.64, 58.27, 0, 3, 26, 158.2, -78.68, 0.54456, 27, -2.39, -84.34, 0.45544, 48, -3366.11, -40.63, 0, 1, 26, 42.51, -92, 1, 1, 26, 33.76, -116.43, 1, 2, 26, -76.14, -57.75, 0.99898, 27, -236.76, -63.82, 0.00102, 1, 26, -103.08, -11.55, 1, 1, 26, -112.57, 23.6, 1], "hull": 96, "edges": [0, 190, 0, 2, 2, 4, 26, 28, 50, 52, 52, 54, 54, 56, 62, 64, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 120, 122, 136, 138, 182, 184, 184, 186, 186, 188, 188, 190, 18, 20, 4, 6, 182, 6, 178, 180, 180, 182, 6, 8, 180, 8, 8, 10, 178, 10, 174, 176, 176, 178, 10, 12, 176, 12, 12, 14, 174, 14, 172, 174, 14, 16, 16, 18, 172, 16, 168, 170, 170, 172, 170, 18, 168, 20, 166, 168, 20, 22, 166, 22, 164, 166, 22, 24, 24, 26, 164, 24, 160, 162, 162, 164, 162, 28, 28, 30, 160, 30, 158, 160, 30, 32, 158, 32, 154, 156, 156, 158, 32, 34, 156, 34, 34, 36, 154, 36, 152, 154, 36, 38, 152, 38, 150, 152, 38, 40, 150, 40, 148, 150, 40, 42, 148, 42, 144, 146, 146, 148, 42, 44, 146, 44, 44, 46, 144, 46, 140, 142, 142, 144, 46, 48, 48, 50, 142, 48, 134, 136, 134, 56, 132, 134, 56, 58, 132, 58, 128, 130, 130, 132, 58, 60, 60, 62, 130, 60, 128, 62, 126, 128, 64, 66, 126, 66, 70, 72, 120, 70, 118, 120, 118, 72, 138, 140, 140, 50, 122, 124, 124, 126, 66, 68, 68, 70, 124, 68, 114, 116, 116, 118, 116, 78], "width": 1563, "height": 3882}}, "right close": {"right close": {"x": 900, "y": -534, "width": 108, "height": 54}}, "right eye": {"right eye": {"x": 169.93, "y": -46.32, "rotation": -93.58, "width": 105, "height": 59}}, "right eyebrow": {"right eyebrow": {"x": 192.07, "y": -52.71, "rotation": -93.58, "width": 111, "height": 26}}, "right half close": {"right half close": {"x": 898.5, "y": -536, "width": 105, "height": 58}}, "sidehair left": {"sidehair left": {"type": "mesh", "uvs": [0.20877, 0.25958, 0.46281, 0.46135, 0.46583, 0.4643, 0.46584, 0.46898, 0.46587, 0.48046, 0.46589, 0.49076, 0.46591, 0.50076, 0.46617, 0.61587, 0.32165, 0.76244, 0.24733, 0.81064, 0.21642, 0.92961, 0.03748, 0.99728, 0.00907, 1, 0.00539, 1, 0.05055, 0.9708, 0.03429, 0.9192, 0.0249, 0.78184, 0.07988, 0.69915, 0.31769, 0.52621, 0.335, 0.51134, 0.29263, 0.48777, 0.27903, 0.48021, 0.24892, 0.46346, 0.22257, 0.44881, 0.19742, 0.43482, 0.17846, 0.42428, 0, 0.32503, 0, 0.30708, 0.13372, 0.26478, 0.2188, 0.41837, 0.24035, 0.43063, 0.25867, 0.44356, 0.28023, 0.45582, 0.31148, 0.46501, 0.33951, 0.46841, 0.23927, 0.42007, 0.28562, 0.41531, 0.28346, 0.44016, 0.32442, 0.43165, 0.33196, 0.4548, 0.36214, 0.44288, 0.37219, 0.4322, 0.35874, 0.46081, 0.35156, 0.47609, 0.3479, 0.48389, 0.34145, 0.49761], "triangles": [11, 14, 10, 14, 12, 13, 11, 12, 14, 10, 15, 9, 14, 15, 10, 29, 26, 36, 35, 29, 36, 30, 35, 36, 37, 30, 36, 36, 0, 41, 0, 1, 41, 42, 41, 1, 1, 2, 3, 42, 1, 3, 43, 34, 42, 43, 42, 3, 43, 3, 4, 44, 20, 43, 44, 43, 4, 5, 44, 4, 45, 20, 44, 45, 44, 5, 45, 5, 6, 19, 20, 45, 17, 18, 7, 8, 17, 7, 19, 6, 18, 6, 19, 45, 7, 18, 6, 16, 17, 8, 9, 16, 8, 15, 16, 9, 42, 40, 41, 40, 38, 41, 42, 34, 39, 41, 38, 36, 39, 38, 40, 42, 39, 40, 34, 33, 39, 38, 37, 36, 31, 30, 37, 37, 38, 39, 21, 34, 20, 20, 34, 43, 34, 21, 33, 32, 31, 37, 32, 37, 39, 32, 22, 23, 32, 23, 31, 33, 32, 39, 33, 21, 22, 33, 22, 32, 25, 26, 29, 24, 25, 29, 30, 24, 29, 30, 29, 35, 23, 24, 30, 23, 30, 31, 26, 27, 28, 26, 28, 0, 36, 26, 0], "vertices": [2, 154, -16.94, 34.18, 1, 156, -101.69, 80.63, 0, 2, 156, 38.43, 29.43, 0, 161, 5.75, 13.58, 1, 2, 156, 40.38, 28.57, 0, 161, 7.6, 14.64, 1, 3, 156, 42.78, 26.43, 0, 157, 29.44, 19.66, 0, 161, 10.74, 15.31, 1, 3, 156, 48.66, 21.18, 0, 157, 33.81, 13.11, 0, 161, 18.45, 16.94, 1, 3, 156, 53.95, 16.47, 0, 157, 37.75, 7.22, 0, 161, 25.38, 18.41, 1, 3, 156, 59.08, 11.9, 0, 157, 41.56, 1.5, 0, 161, 32.1, 19.84, 1, 2, 157, 85.47, -64.26, 0, 161, 109.45, 36.25, 1, 1, 161, 214.46, 26.39, 1, 1, 161, 250.19, 17.46, 1, 3, 156, 242.78, -224.71, 0, 162, 49.25, 25.02, 0.78011, 163, -6.25, 24.45, 0.21989, 2, 156, 251.6, -284.63, 0, 163, 48.94, -0.52, 1, 2, 156, 248.89, -290.48, 0, 163, 52.39, -5.96, 1, 2, 156, 248.36, -291.08, 0, 163, 52.61, -6.73, 1, 1, 163, 30.65, -2.66, 1, 2, 162, 48.12, -15.13, 0.79086, 163, -2.56, -15.55, 0.20914, 3, 156, 139.38, -188.1, 0, 157, 69.05, -212.25, 0, 161, 240.82, -33.86, 1, 2, 156, 104.95, -141.36, 0, 161, 182.77, -33.93, 1, 3, 156, 50.7, -23.74, 0, 157, 24.5, -30.88, 0.00168, 161, 55.86, -8.01, 0.99832, 1, 161, 45.08, -6.45, 1, 3, 156, 27.37, -10.21, 0.05866, 157, 5.32, -11.93, 0.88725, 161, 31.14, -18.8, 0.05409, 2, 156, 21.53, -8.95, 0.29344, 157, -0.02, -9.24, 0.70656, 1, 156, 8.6, -6.16, 1, 2, 155, 36.3, -3.55, 0.92358, 156, -2.72, -3.72, 0.07642, 2, 155, 25.39, -5.34, 0.99999, 156, -13.52, -1.39, 1e-05, 2, 155, 17.17, -6.69, 1, 156, -21.66, 0.36, 0, 2, 154, 2.31, -26.68, 0.9933, 155, -60.2, -19.4, 0.0067, 2, 154, -8.65, -21.04, 0.99944, 155, -71.77, -15.13, 0.00056, 2, 154, -21.22, 18.06, 1, 156, -109.87, 66.1, 0, 2, 155, 16.4, 2.93, 0.8408, 158, 19.62, -10.6, 0.1592, 4, 155, 25.91, 4.4, 0.74561, 156, -9.47, 7.48, 0.00599, 159, -2.54, -8.68, 0.14131, 158, 29.13, -9.04, 0.10709, 3, 155, 35.63, 5.05, 0.22042, 156, -0.19, 4.52, 0.54543, 159, 6.92, -11, 0.23414, 3, 156, 9.21, 2.4, 0.89302, 159, 16.43, -12.47, 0.08169, 160, -5.83, -11.08, 0.02528, 3, 156, 18.43, 3.26, 0.52256, 157, 0.05, 3.35, 0.32266, 160, 3.18, -13.26, 0.15478, 4, 156, 24.23, 6.24, 0.03144, 157, 6.41, 4.78, 0.66199, 160, 9.63, -12.32, 0.2433, 161, 16.03, -11.59, 0.06327, 2, 155, 19.03, 6.69, 0.54983, 158, 22.22, -6.81, 0.45017, 5, 154, 85.82, 0.05, 0.00083, 159, -4.82, 5.54, 0.11814, 160, -18.45, 13.75, 0.00786, 161, -17.25, -30.58, 0.01229, 158, 22.54, 3.76, 0.86088, 3, 155, 35.3, 10.9, 0.05742, 156, 1.65, 10.09, 0.27432, 159, 8.37, -5.32, 0.66827, 4, 159, 9.21, 5.29, 0.82223, 160, -5.61, 8.09, 0.05156, 161, -8, -20.02, 0.05485, 158, 35.94, 7.9, 0.07136, 3, 156, 16.16, 11.25, 0.13742, 157, -0.14, 11.65, 0.11612, 160, 3.61, -4.97, 0.74645, 4, 159, 20.33, 7.01, 0.02306, 160, 5.31, 5.38, 0.78653, 161, -2.15, -10.42, 0.16536, 158, 45.97, 13, 0.02505, 1, 161, -9.78, -9.8, 1, 1, 161, 10.06, -8.59, 1, 1, 161, 20.65, -7.94, 1, 1, 161, 26.05, -7.61, 1, 1, 161, 35.57, -7.03, 1], "hull": 29, "edges": [0, 2, 2, 4, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 52, 54, 54, 56, 56, 0, 50, 52, 50, 58, 48, 50, 48, 60, 46, 48, 46, 62, 44, 46, 44, 64, 42, 44, 42, 66, 38, 40, 40, 42, 40, 68, 70, 72, 74, 76, 78, 80, 84, 82, 4, 6, 84, 6, 86, 84, 6, 8, 86, 8, 88, 86, 8, 10, 88, 10, 38, 90, 90, 88, 10, 12, 12, 14, 90, 12], "width": 217, "height": 687}}, "sidehair right": {"sidehair right": {"type": "mesh", "uvs": [0.63652, 0.18454, 0.7715, 0.20131, 0.83823, 0.18879, 0.85776, 0.2135, 0.93439, 0.28467, 0.95002, 0.35863, 0.85834, 0.38211, 0.84645, 0.38705, 0.87976, 0.45633, 0.86781, 0.47225, 0.90472, 0.65068, 0.90408, 0.74307, 0.82942, 0.80884, 0.82474, 0.83989, 0.78675, 0.91178, 0.76697, 0.92854, 0.74732, 0.94519, 0.69271, 0.99148, 0.66738, 1, 0.64737, 1, 0.58843, 0.97174, 0.62791, 0.9138, 0.64011, 0.89589, 0.64163, 0.88361, 0.59395, 0.76382, 0.61052, 0.64557, 0.7318, 0.51688, 0.74148, 0.47556, 0.5778, 0.48135, 0.64319, 0.43829, 0.66041, 0.42696, 0.61801, 0.41898, 0.56281, 0.4133, 0.58511, 0.39184, 0.61235, 0.36562, 0.62916, 0.34944, 0.62268, 0.32215, 0.60659, 0.28509, 0.60169, 0.24455, 0.56715, 0.23677, 0.70487, 0.42598, 0.73266, 0.43307, 0.69315, 0.43584, 0.7166, 0.44155, 0.68012, 0.44363, 0.70227, 0.44969, 0.66926, 0.44761, 0.6723, 0.4194, 0.65841, 0.39396, 0.68316, 0.39967, 0.64668, 0.40244, 0.67274, 0.41023, 0.63496, 0.41196, 0.65797, 0.41801, 0.6089, 0.39759, 0.65102, 0.37025, 0.66753, 0.35779, 0.66492, 0.91842, 0.69381, 0.92575, 0.69035, 0.93987], "triangles": [42, 40, 41, 41, 40, 49, 45, 46, 44, 45, 43, 27, 44, 42, 45, 45, 42, 43, 42, 44, 30, 43, 42, 41, 42, 30, 40, 28, 29, 46, 46, 29, 44, 44, 29, 30, 40, 30, 47, 47, 51, 40, 30, 31, 53, 31, 52, 53, 30, 53, 47, 47, 53, 51, 52, 31, 54, 53, 52, 51, 52, 50, 51, 52, 54, 50, 7, 41, 49, 51, 49, 40, 51, 50, 49, 50, 48, 49, 50, 54, 48, 54, 55, 48, 49, 48, 55, 31, 32, 54, 32, 33, 54, 54, 33, 34, 7, 49, 56, 54, 34, 55, 56, 49, 55, 56, 55, 35, 55, 34, 35, 4, 56, 36, 35, 36, 56, 18, 19, 17, 19, 20, 17, 20, 59, 17, 17, 59, 16, 59, 58, 16, 16, 58, 15, 15, 58, 14, 58, 57, 14, 14, 23, 13, 59, 57, 58, 13, 23, 12, 59, 20, 21, 14, 57, 22, 57, 21, 22, 59, 21, 57, 22, 23, 14, 12, 23, 24, 12, 24, 11, 24, 25, 11, 11, 25, 10, 10, 25, 26, 9, 10, 26, 9, 26, 27, 27, 46, 45, 46, 27, 28, 43, 41, 27, 9, 27, 8, 8, 27, 41, 41, 7, 8, 7, 56, 6, 5, 6, 4, 6, 56, 4, 4, 36, 3, 1, 3, 37, 3, 36, 37, 37, 38, 1, 38, 0, 1, 38, 39, 0, 1, 2, 3], "vertices": [1, 164, -19.54, -27.94, 1, 2, 164, -5.44, 7.69, 0.9791, 173, -69.57, 18.98, 0.0209, 2, 164, -12.58, 26.36, 0.99739, 165, -103.07, 13.66, 0.00261, 2, 164, 4.58, 30.38, 0.89014, 165, -86.21, 18.78, 0.10986, 2, 164, 54.39, 47.47, 0.57696, 165, -37.61, 39.05, 0.42304, 2, 164, 104.86, 47.93, 0.24624, 165, 12.73, 42.76, 0.75376, 2, 164, 118.93, 21.96, 0.02016, 165, 28.44, 17.76, 0.97984, 2, 165, 31.76, 14.5, 0.99892, 166, -74.7, 7.33, 0.00108, 2, 165, 78.97, 23.04, 0.97626, 166, -28.54, 20.38, 0.02374, 2, 165, 89.76, 19.69, 0.64293, 166, -17.48, 18.09, 0.35707, 1, 166, 102.54, 38.51, 1, 1, 166, 165.14, 43.76, 1, 2, 166, 211.45, 27.46, 0.4773, 170, -10.82, 26.51, 0.5227, 3, 166, 232.59, 28.01, 0.14997, 170, 10.29, 25.24, 0.54658, 171, -43.44, 18.07, 0.30345, 1, 171, 6.49, 16.3, 1, 2, 171, 18.63, 12.97, 0.78971, 172, -11.63, 11.65, 0.21029, 2, 171, 30.7, 9.67, 0.48001, 172, 0.59, 8.96, 0.51999, 1, 172, 34.57, 1.51, 1, 2, 169, 39.31, 21.32, 0.13571, 172, 41.74, -3.9, 0.86429, 2, 169, 40.53, 16.04, 0.40332, 172, 42.94, -9.19, 0.59668, 1, 169, 25.39, -3.84, 1, 4, 167, 54.51, -5.08, 0.39388, 168, 15.35, -3.28, 0.44874, 169, -15.4, -2.28, 0.08396, 172, -13.05, -27.32, 0.07342, 2, 167, 41.96, -3.82, 0.49143, 168, 2.74, -3.52, 0.50857, 1, 167, 33.66, -4.79, 1, 2, 166, 186.45, -38.76, 0.63471, 167, -44.57, -30.92, 0.36529, 4, 165, 206.89, -51.25, 0.00674, 166, 105.95, -41.22, 0.96123, 167, -124.62, -39.71, 0.03195, 181, 129.45, 65.49, 8e-05, 4, 165, 119.73, -17.48, 0.25042, 166, 15.94, -16.02, 0.66802, 180, 60.12, 38.42, 0.00674, 181, 38.4, 44.3, 0.07482, 1, 165, 91.66, -14.57, 1, 1, 184, 20.59, -1.07, 1, 1, 181, 7.26, -5.33, 1, 5, 174, 48.56, 20.29, 0, 175, 13.14, 26.41, 0.06424, 178, 8.76, 9.6, 0.34372, 180, 13.3, -5.41, 0.21233, 181, -1.74, -5.72, 0.37971, 1, 178, 11.32, -2.85, 1, 1, 175, 23.07, 0.2, 1, 3, 173, 65.91, -12.35, 0.13827, 174, 32.79, -6.88, 0.1978, 175, 8.01, -4.58, 0.66393, 3, 173, 47.21, -7.61, 0.30719, 174, 13.52, -5.83, 0.43946, 175, -10.38, -10.42, 0.25335, 2, 173, 35.66, -4.69, 0.41143, 174, 1.63, -5.18, 0.58857, 2, 164, 73.49, -38.67, 0.03485, 173, 17.55, -9.1, 0.96515, 2, 164, 48.03, -41.14, 0.60571, 173, -6.76, -17.04, 0.39429, 1, 164, 20.44, -40.4, 1, 1, 164, 14.47, -49.34, 1, 2, 180, 7.5, 5.17, 0.50286, 182, 6.78, -3.07, 0.49714, 1, 182, 8.03, 5.78, 1, 1, 183, 0.48, -3.26, 1, 1, 183, -0.43, 4.13, 1, 1, 183, 6.82, -2.74, 1, 1, 183, 6.32, 4.53, 1, 1, 181, 8.61, 4.07, 1, 1, 180, 7.28, -4.72, 1, 1, 177, 4.68, -3.2, 1, 1, 177, 5.16, 4.54, 1, 2, 177, 11.26, -3.46, 0.50286, 178, -2.34, -3.38, 0.49714, 2, 177, 12.84, 5.22, 0.50286, 178, -2.34, 5.45, 0.49714, 1, 178, 4.74, -2.04, 1, 1, 178, 4.3, 5.43, 1, 8, 173, 68.86, -5.41, 0.11708, 165, 38.27, -49.95, 0.02399, 166, -62.01, -56.19, 0.00028, 174, 34.35, 0.5, 0.16749, 175, 6.85, 2.88, 0.39418, 178, 1.17, -13.55, 0.25105, 180, 1.31, -26.62, 0.01647, 181, -10.62, -28.41, 0.02945, 8, 173, 48.81, 3.21, 0.2898, 165, 19.8, -38.34, 0.07781, 166, -81.52, -46.42, 0.00058, 174, 13.02, 5.11, 0.41458, 175, -14.72, -0.37, 0.00988, 178, -20.56, -15.57, 0.11631, 180, -20.39, -24.35, 0.03265, 181, -32.42, -29.22, 0.05839, 1, 174, 3.54, 6.52, 1, 1, 168, 15.56, 7.22, 1, 2, 168, 18.16, 16.14, 0.13714, 171, 20.17, -6.89, 0.86286, 2, 171, 29.79, -6.16, 0.48001, 172, 0.49, -6.89, 0.51999], "hull": 40, "edges": [0, 2, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 62, 64, 70, 72, 72, 74, 74, 76, 2, 4, 78, 0, 76, 78, 80, 82, 84, 86, 88, 90, 56, 58, 58, 60, 58, 92, 94, 80, 96, 98, 100, 102, 104, 106, 64, 66, 66, 108, 66, 68, 68, 70, 68, 110, 70, 112, 40, 42, 42, 44, 42, 114, 28, 30, 116, 30, 30, 32, 32, 34, 118, 32], "width": 271, "height": 680}}}}], "animations": {"idle": {"slots": {"left close": {"attachment": [{"time": 0.4667, "name": "left close"}, {"time": 0.6333}]}, "left eye": {"attachment": [{"time": 0.4}, {"time": 0.7, "name": "left eye"}]}, "left half close": {"attachment": [{"time": 0.4, "name": "left half close"}, {"time": 0.4667}, {"time": 0.6333, "name": "left half close"}, {"time": 0.7}]}, "right close": {"attachment": [{"time": 0.4667, "name": "right close"}, {"time": 0.6333}]}, "right eye": {"attachment": [{"time": 0.4}, {"time": 0.7, "name": "right eye"}]}, "right half close": {"attachment": [{"time": 0.4, "name": "right half close"}, {"time": 0.4667}, {"time": 0.6333, "name": "right half close"}, {"time": 0.7}]}}, "bones": {"body up": {"rotate": [{"curve": [0.533, 0, 1.067, -0.1]}, {"time": 1.6, "value": -0.1, "curve": [2.133, -0.1, 2.667, 0]}, {"time": 3.2}], "translate": [{"curve": [0.533, 0, 1.067, 0, 0.533, 0, 1.067, -3.87]}, {"time": 1.6, "y": -3.87, "curve": [2.133, 0, 2.667, 0, 2.133, -3.87, 2.667, 0]}, {"time": 3.2}]}, "ponytail": {"rotate": [{"curve": [0.533, 0, 1.067, -0.83]}, {"time": 1.6, "value": -0.83, "curve": [2.133, -0.83, 2.667, 0]}, {"time": 3.2}]}, "ponytail2": {"rotate": [{"value": -0.09, "curve": [0.113, -0.04, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -0.83]}, {"time": 1.9333, "value": -0.83, "curve": [2.356, -0.83, 2.78, -0.31]}, {"time": 3.2, "value": -0.09}]}, "ponytail3": {"rotate": [{"value": -0.31, "curve": [0.222, -0.14, 0.444, 0]}, {"time": 0.6667, "curve": [1.2, 0, 1.733, -0.83]}, {"time": 2.2667, "value": -0.83, "curve": [2.578, -0.83, 2.889, -0.55]}, {"time": 3.2, "value": -0.31}]}, "ponytail4": {"rotate": [{"value": -0.57, "curve": [0.335, -0.32, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -0.83]}, {"time": 2.6, "value": -0.83, "curve": [2.801, -0.83, 3.002, -0.71]}, {"time": 3.2, "value": -0.57}]}, "ponytail5": {"rotate": [{"value": -0.76, "curve": [0.444, -0.57, 0.889, 0]}, {"time": 1.3333, "curve": [1.867, 0, 2.4, -0.83]}, {"time": 2.9333, "value": -0.83, "curve": [3.022, -0.83, 3.111, -0.8]}, {"time": 3.2, "value": -0.76}]}, "ponytail6": {"rotate": [{"value": -0.82, "curve": [0.024, -0.82, 0.045, -0.83]}, {"time": 0.0667, "value": -0.83, "curve": [0.6, -0.83, 1.133, 0]}, {"time": 1.6667, "curve": [2.179, 0, 2.691, -0.76]}, {"time": 3.2, "value": -0.82}]}, "ponytail7": {"rotate": [{"value": -0.7, "curve": [0.133, -0.77, 0.267, -0.83]}, {"time": 0.4, "value": -0.83, "curve": [0.933, -0.83, 1.467, 0]}, {"time": 2, "curve": [2.4, 0, 2.8, -0.46]}, {"time": 3.2, "value": -0.7}]}, "ponytail8": {"rotate": [{"value": -0.47, "curve": [0.246, -0.65, 0.49, -0.83]}, {"time": 0.7333, "value": -0.83, "curve": [1.267, -0.83, 1.8, 0]}, {"time": 2.3333, "curve": [2.623, 0, 2.913, -0.24]}, {"time": 3.2, "value": -0.47}]}, "ponytail9": {"rotate": [{"value": -0.47, "curve": [0.246, -0.65, 0.49, -0.83]}, {"time": 0.7333, "value": -0.83, "curve": [1.267, -0.83, 1.8, 0]}, {"time": 2.3333, "curve": [2.623, 0, 2.913, -0.24]}, {"time": 3.2, "value": -0.47}]}, "ponytail10": {"rotate": [{"value": -0.47, "curve": [0.246, -0.65, 0.49, -0.83]}, {"time": 0.7333, "value": -0.83, "curve": [1.267, -0.83, 1.8, 0]}, {"time": 2.3333, "curve": [2.623, 0, 2.913, -0.24]}, {"time": 3.2, "value": -0.47}]}, "ponytail11": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail12": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail13": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail14": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail15": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail16": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail17": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail18": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail22": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail23": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail24": {"rotate": [{"value": -0.22, "curve": [0.356, -0.46, 0.711, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.6, -0.83, 2.133, 0]}, {"time": 2.6667, "curve": [2.844, 0, 3.022, -0.09]}, {"time": 3.2, "value": -0.22}]}, "ponytail20": {"rotate": [{"time": 1.6, "value": -0.83}]}, "hairbond right": {"rotate": [{"value": -0.23, "curve": [0.113, -0.09, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -2.06]}, {"time": 1.9333, "value": -2.06, "curve": [2.356, -2.06, 2.78, -0.77]}, {"time": 3.2, "value": -0.23}], "translate": [{"x": 0.7, "curve": [0.113, 0.28, 0.223, 0, 0.113, 0, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, 6.21, 0.867, 0, 1.4, 0]}, {"time": 1.9333, "x": 6.21, "curve": [2.356, 6.21, 2.78, 2.32, 2.356, 0, 2.78, 0]}, {"time": 3.2, "x": 0.7}]}, "hairbond right2": {"rotate": [{"value": -0.78, "curve": [0.222, -0.36, 0.444, 0]}, {"time": 0.6667, "curve": [1.2, 0, 1.733, -2.06]}, {"time": 2.2667, "value": -2.06, "curve": [2.578, -2.06, 2.889, -1.36]}, {"time": 3.2, "value": -0.78}], "translate": [{"curve": [0.222, 0, 0.444, 0, 0.222, 0, 0.444, 0]}, {"time": 0.6667, "curve": [1.2, 0, 1.733, 0, 1.2, 0, 1.733, 0]}, {"time": 2.2667, "curve": [2.578, 0, 2.889, 0, 2.578, 0, 2.889, 0]}, {"time": 3.2}]}, "hairbond right3": {"rotate": [{"value": -1.41, "curve": [0.335, -0.8, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -2.06]}, {"time": 2.6, "value": -2.06, "curve": [2.801, -2.06, 3.002, -1.77]}, {"time": 3.2, "value": -1.41}], "translate": [{"curve": [0.335, 0, 0.668, 0, 0.335, 0, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, 0, 1.533, 0, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 0, 2.801, 0, 3.002, 0]}, {"time": 3.2}]}, "hairbond right4": {"rotate": [{"value": -1.9, "curve": [0.444, -1.42, 0.889, 0]}, {"time": 1.3333, "curve": [1.867, 0, 2.4, -2.06]}, {"time": 2.9333, "value": -2.06, "curve": [3.022, -2.06, 3.111, -2]}, {"time": 3.2, "value": -1.9}], "translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, 0]}, {"time": 1.3333}], "shear": [{"curve": [0.533, 0, 1.067, 0, 0.533, 0, 1.067, -6.98]}, {"time": 1.6, "y": -6.98, "curve": [2.133, 0, 2.667, 0, 2.133, -6.98, 2.667, 0]}, {"time": 3.2}]}, "hairbond right5": {"rotate": [{"value": -2.04, "curve": [0.024, -2.05, 0.045, -2.06]}, {"time": 0.0667, "value": -2.06, "curve": [0.6, -2.06, 1.133, 0]}, {"time": 1.6667, "curve": [2.179, 0, 2.691, -1.89]}, {"time": 3.2, "value": -2.04}], "translate": [{"time": 0.0667, "curve": [0.6, 0, 1.133, 0, 0.6, 0, 1.133, 0]}, {"time": 1.6667, "curve": [2.179, 0, 2.691, 0, 2.179, 0, 2.691, 0]}, {"time": 3.2}], "shear": [{"curve": [0.533, 0, 1.067, 0, 0.533, 0, 1.067, -6.98]}, {"time": 1.6, "y": -6.98, "curve": [2.133, 0, 2.667, 0, 2.133, -6.98, 2.667, 0]}, {"time": 3.2}]}, "hairbond right6": {"rotate": [{"value": -1.73, "curve": [0.133, -1.93, 0.267, -2.06]}, {"time": 0.4, "value": -2.06, "curve": [0.933, -2.06, 1.467, 0]}, {"time": 2, "curve": [2.4, 0, 2.8, -1.15]}, {"time": 3.2, "value": -1.73}], "translate": [{"curve": [0.133, 0, 0.267, 0, 0.133, 0, 0.267, 0]}, {"time": 0.4, "curve": [0.933, 0, 1.467, 0, 0.933, 0, 1.467, 0]}, {"time": 2, "curve": [2.4, 0, 2.8, 0, 2.4, 0, 2.8, 0]}, {"time": 3.2}], "shear": [{"curve": [0.533, 0, 1.067, 0, 0.533, 0, 1.067, -6.98]}, {"time": 1.6, "y": -6.98, "curve": [2.133, 0, 2.667, 0, 2.133, -6.98, 2.667, 0]}, {"time": 3.2}]}, "hairbond right7": {"rotate": [{"value": -1.16, "curve": [0.246, -1.63, 0.49, -2.06]}, {"time": 0.7333, "value": -2.06, "curve": [1.267, -2.06, 1.8, 0]}, {"time": 2.3333, "curve": [2.623, 0, 2.913, -0.6]}, {"time": 3.2, "value": -1.16}], "translate": [{"curve": [0.246, 0, 0.49, 0, 0.246, 0, 0.49, 0]}, {"time": 0.7333, "curve": [1.267, 0, 1.8, 0, 1.267, 0, 1.8, 0]}, {"time": 2.3333, "curve": [2.623, 0, 2.913, 0, 2.623, 0, 2.913, 0]}, {"time": 3.2}], "shear": [{"curve": [0.533, 0, 1.067, 0, 0.533, 0, 1.067, -6.98]}, {"time": 1.6, "y": -6.98, "curve": [2.133, 0, 2.667, 0, 2.133, -6.98, 2.667, 0]}, {"time": 3.2}]}, "hair bond left2": {"rotate": [{"curve": [0.533, 0, 1.067, -1.13]}, {"time": 1.6, "value": -1.13, "curve": [2.133, -1.13, 2.667, 0]}, {"time": 3.2}], "translate": [{"curve": [0.533, 0, 1.067, -3.82, 0.533, 0, 1.067, -2.23]}, {"time": 1.6, "x": -3.82, "y": -2.23, "curve": [2.133, -3.82, 2.667, 0, 2.133, -2.23, 2.667, 0]}, {"time": 3.2}]}, "hair bond left3": {"rotate": [{"value": -0.26, "curve": [0.169, -0.11, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.13]}, {"time": 2.1, "value": -1.13, "curve": [2.468, -1.13, 2.836, -0.6]}, {"time": 3.2, "value": -0.26}]}, "hair bond left4": {"rotate": [{"value": -0.77, "curve": [0.335, -0.44, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -1.13]}, {"time": 2.6, "value": -1.13, "curve": [2.801, -1.13, 3.002, -0.97]}, {"time": 3.2, "value": -0.77}]}, "hair bond left5": {"rotate": [{"value": -1.11, "curve": [0.501, -0.99, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -1.13]}, {"time": 3.1, "value": -1.13, "curve": [3.134, -1.13, 3.168, -1.12]}, {"time": 3.2, "value": -1.11}], "shear": [{"curve": [0.533, 0, 1.067, 9.61, 0.533, 0, 1.067, 0.4]}, {"time": 1.6, "x": 9.61, "y": 0.4, "curve": [2.133, 9.61, 2.667, 0, 2.133, 0.4, 2.667, 0]}, {"time": 3.2}]}, "hair bond left6": {"rotate": [{"value": -0.95, "curve": [0.133, -1.06, 0.267, -1.13]}, {"time": 0.4, "value": -1.13, "curve": [0.933, -1.13, 1.467, 0]}, {"time": 2, "curve": [2.4, 0, 2.8, -0.63]}, {"time": 3.2, "value": -0.95}], "shear": [{"curve": [0.533, 0, 1.067, 9.61, 0.533, 0, 1.067, 0.4]}, {"time": 1.6, "x": 9.61, "y": 0.4, "curve": [2.133, 9.61, 2.667, 0, 2.133, 0.4, 2.667, 0]}, {"time": 3.2}]}, "fringe left left13": {"rotate": [{"value": 5.52, "curve": [0.533, 5.52, 1.067, -1.59]}, {"time": 1.6, "value": -1.59, "curve": [2.133, -1.59, 2.667, 5.52]}, {"time": 3.2, "value": 5.52}]}, "fringe left right3": {"rotate": [{"value": 4.72, "curve": [0.113, 5.2, 0.223, 5.52]}, {"time": 0.3333, "value": 5.52, "curve": [0.867, 5.52, 1.4, -1.59]}, {"time": 1.9333, "value": -1.59, "curve": [2.356, -1.59, 2.78, 2.86]}, {"time": 3.2, "value": 4.72}]}, "fringe right right3": {"rotate": [{"value": 1.13, "curve": [0.533, 1.13, 1.067, -7.09]}, {"time": 1.6, "value": -7.09, "curve": [2.133, -7.09, 2.667, 1.13]}, {"time": 3.2, "value": 1.13}]}, "fringe right left3": {"rotate": [{"value": 0.2, "curve": [0.113, 0.75, 0.223, 1.13]}, {"time": 0.3333, "value": 1.13, "curve": [0.867, 1.13, 1.4, -7.09]}, {"time": 1.9333, "value": -7.09, "curve": [2.356, -7.09, 2.78, -1.94]}, {"time": 3.2, "value": 0.2}]}, "hair left14": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left16": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left2": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left3": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left5": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left6": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left7": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left8": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left9": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left10": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left11": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left12": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left13": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left15": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left17": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left18": {"rotate": [{"value": -0.31, "curve": [0.169, -0.13, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.31]}, {"time": 2.1, "value": -1.31, "curve": [2.468, -1.31, 2.836, -0.69]}, {"time": 3.2, "value": -0.31}]}, "hair left19": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left20": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left21": {"rotate": [{"curve": [0.533, 0, 1.067, -1.31]}, {"time": 1.6, "value": -1.31, "curve": [2.133, -1.31, 2.667, 0]}, {"time": 3.2}]}, "hair left22": {"rotate": [{"value": -3.44, "curve": [0.335, -1.97, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -5.05]}, {"time": 2.6, "value": -5.05, "curve": [2.801, -5.05, 3.002, -4.34]}, {"time": 3.2, "value": -3.44}]}, "hair left23": {"rotate": [{"value": -4.96, "curve": [0.501, -4.4, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.05]}, {"time": 3.1, "value": -5.05, "curve": [3.134, -5.05, 3.168, -5]}, {"time": 3.2, "value": -4.96}]}, "hair left24": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left25": {"rotate": [{"value": -0.9, "curve": [0.335, -0.51, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -1.31]}, {"time": 2.6, "value": -1.31, "curve": [2.801, -1.31, 3.002, -1.13]}, {"time": 3.2, "value": -0.9}]}, "hair left26": {"rotate": [{"curve": [0.533, 0, 1.067, -5.05]}, {"time": 1.6, "value": -5.05, "curve": [2.133, -5.05, 2.667, 0]}, {"time": 3.2}]}, "hair left27": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair left4": {"rotate": [{"value": -13.38, "curve": [0.501, -11.86, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -13.61]}, {"time": 3.1, "value": -13.61, "curve": [3.134, -13.61, 3.168, -13.48]}, {"time": 3.2, "value": -13.38}]}, "hair right": {"rotate": [{"value": -0.22, "curve": [0.533, -0.22, 1.067, -1.22]}, {"time": 1.6, "value": -1.22, "curve": [2.133, -1.22, 2.667, -0.22]}, {"time": 3.2, "value": -0.22}]}, "hair right2": {"rotate": [{"curve": [0.533, 0, 1.067, -2.6]}, {"time": 1.6, "value": -2.6, "curve": [2.133, -2.6, 2.667, 1.46]}, {"time": 3.2, "value": 1.46}]}, "hair right3": {"rotate": [{"value": -0.61, "curve": [0.169, -0.26, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -2.6]}, {"time": 2.1, "value": -2.6, "curve": [2.468, -2.6, 2.836, -1.37]}, {"time": 3.2, "value": -0.61}]}, "hair right4": {"rotate": [{"value": -4.19, "curve": [0.335, -2.39, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -6.15]}, {"time": 2.6, "value": -6.15, "curve": [2.801, -6.15, 3.002, -5.28]}, {"time": 3.2, "value": -4.19}]}, "hair right5": {"rotate": [{"value": -5.07, "curve": [0.501, -4.5, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.16]}, {"time": 3.1, "value": -5.16, "curve": [3.134, -5.16, 3.168, -5.11]}, {"time": 3.2, "value": -5.07}]}, "hair right6": {"rotate": [{"value": -0.61, "curve": [0.169, -0.26, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -2.6]}, {"time": 2.1, "value": -2.6, "curve": [2.468, -2.6, 2.836, -1.37]}, {"time": 3.2, "value": -0.61}]}, "hair right7": {"rotate": [{"value": -4.19, "curve": [0.335, -2.39, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -6.15]}, {"time": 2.6, "value": -6.15, "curve": [2.801, -6.15, 3.002, -5.28]}, {"time": 3.2, "value": -4.19}]}, "hair right8": {"rotate": [{"value": -5.07, "curve": [0.501, -4.5, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.16]}, {"time": 3.1, "value": -5.16, "curve": [3.134, -5.16, 3.168, -5.11]}, {"time": 3.2, "value": -5.07}]}, "hair right9": {"rotate": [{"value": -0.61, "curve": [0.169, -0.26, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -2.6]}, {"time": 2.1, "value": -2.6, "curve": [2.468, -2.6, 2.836, -1.37]}, {"time": 3.2, "value": -0.61}]}, "hair right10": {"rotate": [{"value": -4.19, "curve": [0.335, -2.39, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -6.15]}, {"time": 2.6, "value": -6.15, "curve": [2.801, -6.15, 3.002, -5.28]}, {"time": 3.2, "value": -4.19}]}, "hair right11": {"rotate": [{"value": -5.07, "curve": [0.501, -4.5, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.16]}, {"time": 3.1, "value": -5.16, "curve": [3.134, -5.16, 3.168, -5.11]}, {"time": 3.2, "value": -5.07}]}, "hair right12": {"rotate": [{"value": -0.61, "curve": [0.169, -0.26, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -2.6]}, {"time": 2.1, "value": -2.6, "curve": [2.468, -2.6, 2.836, -1.37]}, {"time": 3.2, "value": -0.61}]}, "hair right13": {"rotate": [{"value": -4.19, "curve": [0.335, -2.39, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -6.15]}, {"time": 2.6, "value": -6.15, "curve": [2.801, -6.15, 3.002, -5.28]}, {"time": 3.2, "value": -4.19}]}, "hair right14": {"rotate": [{"value": -5.07, "curve": [0.501, -4.5, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.16]}, {"time": 3.1, "value": -5.16, "curve": [3.134, -5.16, 3.168, -5.11]}, {"time": 3.2, "value": -5.07}]}, "hair right15": {"rotate": [{"value": -0.61, "curve": [0.169, -0.26, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -2.6]}, {"time": 2.1, "value": -2.6, "curve": [2.468, -2.6, 2.836, -1.37]}, {"time": 3.2, "value": -0.61}]}, "hair right16": {"rotate": [{"value": -4.19, "curve": [0.335, -2.39, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -6.15]}, {"time": 2.6, "value": -6.15, "curve": [2.801, -6.15, 3.002, -5.28]}, {"time": 3.2, "value": -4.19}]}, "hair right17": {"rotate": [{"value": -5.07, "curve": [0.501, -4.5, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -5.16]}, {"time": 3.1, "value": -5.16, "curve": [3.134, -5.16, 3.168, -5.11]}, {"time": 3.2, "value": -5.07}]}, "sidehair left2": {"rotate": [{"value": 3.79, "curve": [0.169, 4.32, 0.335, 4.72]}, {"time": 0.5, "value": 4.72, "curve": [1.033, 4.72, 1.567, 0.75]}, {"time": 2.1, "value": 0.75, "curve": [2.468, 0.75, 2.836, 2.62]}, {"time": 3.2, "value": 3.79}]}, "sidehair left3": {"rotate": [{"value": 1.58, "curve": [0.335, 3.93, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, -0.99]}, {"time": 2.6, "value": -0.99, "curve": [2.801, -0.99, 3.002, 0.15]}, {"time": 3.2, "value": 1.58}]}, "sidehair left4": {"rotate": [{"value": -0.86, "curve": [0.501, -0.04, 1, 6.36]}, {"time": 1.5, "value": 6.36, "curve": [2.033, 6.36, 2.567, -0.99]}, {"time": 3.1, "value": -0.99, "curve": [3.134, -0.99, 3.168, -0.91]}, {"time": 3.2, "value": -0.86}]}, "sidehair left5": {"rotate": [{"value": 3.79, "curve": [0.169, 4.32, 0.335, 4.72]}, {"time": 0.5, "value": 4.72, "curve": [1.033, 4.72, 1.567, 0.75]}, {"time": 2.1, "value": 0.75, "curve": [2.468, 0.75, 2.836, 2.62]}, {"time": 3.2, "value": 3.79}]}, "sidehair left6": {"rotate": [{"value": 1.58, "curve": [0.335, 3.93, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, -0.99]}, {"time": 2.6, "value": -0.99, "curve": [2.801, -0.99, 3.002, 0.15]}, {"time": 3.2, "value": 1.58}]}, "sidehair left7": {"rotate": [{"value": -0.86, "curve": [0.501, -0.04, 1, 6.36]}, {"time": 1.5, "value": 6.36, "curve": [2.033, 6.36, 2.567, -0.99]}, {"time": 3.1, "value": -0.99, "curve": [3.134, -0.99, 3.168, -0.91]}, {"time": 3.2, "value": -0.86}]}, "sidehair left8": {"rotate": [{"curve": [0.533, 0, 1.067, -0.46]}, {"time": 1.6, "value": -0.46, "curve": [2.133, -0.46, 2.667, 0]}, {"time": 3.2}]}, "sidehair left9": {"rotate": [{"curve": [0.533, 0, 1.067, -6.53]}, {"time": 1.6, "value": -6.53, "curve": [2.133, -6.53, 2.667, 0]}, {"time": 3.2}]}, "sidehair left10": {"rotate": [{"curve": [0.533, 0, 1.067, -6.53]}, {"time": 1.6, "value": -6.53, "curve": [2.133, -6.53, 2.667, 0]}, {"time": 3.2}]}, "sidehair right10": {"rotate": [{"value": -0.32, "curve": [0.167, -0.18, 0.333, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.37]}, {"time": 2.1, "value": -1.37, "curve": [2.467, -1.37, 2.833, -0.63]}, {"time": 3.2, "value": -0.32}]}, "sidehair right11": {"rotate": [{"value": -2.06, "curve": [0.333, -1.43, 0.667, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -3.02]}, {"time": 2.6, "value": -3.02, "curve": [2.8, -3.02, 3, -2.44]}, {"time": 3.2, "value": -2.06}]}, "sidehair right12": {"rotate": [{"value": -5.99, "curve": [0.5, -4.39, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -6.1]}, {"time": 3.1, "value": -6.1, "curve": [3.133, -6.1, 3.167, -6.1]}, {"time": 3.2, "value": -5.99}]}, "sidehair right13": {"rotate": [{"value": -0.32, "curve": [0.167, -0.18, 0.333, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.37]}, {"time": 2.1, "value": -1.37, "curve": [2.467, -1.37, 2.833, -0.63]}, {"time": 3.2, "value": -0.32}]}, "sidehair right14": {"rotate": [{"value": -2.06, "curve": [0.333, -1.43, 0.667, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -3.02]}, {"time": 2.6, "value": -3.02, "curve": [2.8, -3.02, 3, -2.44]}, {"time": 3.2, "value": -2.06}]}, "sidehair right15": {"rotate": [{"value": -5.99, "curve": [0.5, -4.39, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -6.1]}, {"time": 3.1, "value": -6.1, "curve": [3.133, -6.1, 3.167, -6.1]}, {"time": 3.2, "value": -5.99}]}, "sidehair right16": {"rotate": [{"value": -0.32, "curve": [0.167, -0.18, 0.333, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.37]}, {"time": 2.1, "value": -1.37, "curve": [2.467, -1.37, 2.833, -0.63]}, {"time": 3.2, "value": -0.32}]}, "sidehair right17": {"rotate": [{"curve": [0.533, 0, 1.067, -1.37]}, {"time": 1.6, "value": -1.37, "curve": [2.133, -1.37, 2.667, 0]}, {"time": 3.2}]}, "sidehair right18": {"rotate": [{"value": -2.06, "curve": [0.333, -1.43, 0.667, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -3.02]}, {"time": 2.6, "value": -3.02, "curve": [2.8, -3.02, 3, -2.44]}, {"time": 3.2, "value": -2.06}]}, "sidehair right2": {"rotate": [{"curve": [0.533, 0, 1.067, -1.22]}, {"time": 1.6, "value": -1.22, "curve": [2.133, -1.22, 2.667, 0]}, {"time": 3.2}]}, "sidehair right3": {"rotate": [{"curve": [0.533, 0, 1.067, -0.29]}, {"time": 1.6, "value": -0.29, "curve": [2.133, -0.29, 2.667, 0]}, {"time": 3.2}]}, "sidehair right19": {"rotate": [{"value": -2.06, "curve": [0.333, -1.43, 0.667, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -3.02]}, {"time": 2.6, "value": -3.02, "curve": [2.8, -3.02, 3, -2.44]}, {"time": 3.2, "value": -2.06}]}, "sidehair right20": {"rotate": [{"value": -5.99, "curve": [0.5, -4.39, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -6.1]}, {"time": 3.1, "value": -6.1, "curve": [3.133, -6.1, 3.167, -6.1]}, {"time": 3.2, "value": -5.99}]}, "sidehair right21": {"rotate": [{"value": -5.99, "curve": [0.5, -4.39, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -6.1]}, {"time": 3.1, "value": -6.1, "curve": [3.133, -6.1, 3.167, -6.1]}, {"time": 3.2, "value": -5.99}]}, "sidehair right4": {"rotate": [{"curve": [0.533, 0, 1.067, -0.73]}, {"time": 1.6, "value": -0.73, "curve": [2.133, -0.73, 2.667, 0]}, {"time": 3.2}]}, "sidehair right5": {"rotate": [{"value": -0.15, "curve": [0.113, -0.06, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -1.35]}, {"time": 1.9333, "value": -1.35, "curve": [2.356, -1.35, 2.78, -0.5]}, {"time": 3.2, "value": -0.15}]}, "sidehair right6": {"rotate": [{"value": -0.3, "curve": [0.113, -0.12, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -2.62]}, {"time": 1.9333, "value": -2.62, "curve": [2.356, -2.62, 2.78, -0.98]}, {"time": 3.2, "value": -0.3}]}, "sidehair right7": {"rotate": [{"curve": [0.533, 0, 1.067, -0.73]}, {"time": 1.6, "value": -0.73, "curve": [2.133, -0.73, 2.667, 0]}, {"time": 3.2}]}, "sidehair right8": {"rotate": [{"value": -0.15, "curve": [0.113, -0.06, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -1.35]}, {"time": 1.9333, "value": -1.35, "curve": [2.356, -1.35, 2.78, -0.5]}, {"time": 3.2, "value": -0.15}]}, "sidehair right9": {"rotate": [{"value": -0.3, "curve": [0.113, -0.12, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -2.62]}, {"time": 1.9333, "value": -2.62, "curve": [2.356, -2.62, 2.78, -0.98]}, {"time": 3.2, "value": -0.3}]}, "fringe left left3": {"rotate": [{"value": 3.31, "curve": [0.533, 3.31, 1.067, 0]}, {"time": 1.6, "curve": [2.133, 0, 2.667, 3.31]}, {"time": 3.2, "value": 3.31}]}, "fringe left left4": {"rotate": [{"value": 5.43, "curve": [0.169, 6.36, 0.335, 7.07]}, {"time": 0.5, "value": 7.07, "curve": [1.033, 7.07, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 3.34]}, {"time": 3.2, "value": 5.43}]}, "fringe left left5": {"rotate": [{"value": 2.25, "curve": [0.335, 4.32, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1]}, {"time": 3.2, "value": 2.25}]}, "fringe left left6": {"rotate": [{"value": 5.43, "curve": [0.169, 6.36, 0.335, 7.07]}, {"time": 0.5, "value": 7.07, "curve": [1.033, 7.07, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 3.34]}, {"time": 3.2, "value": 5.43}]}, "fringe left left7": {"rotate": [{"value": 2.25, "curve": [0.335, 4.32, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1]}, {"time": 3.2, "value": 2.25}]}, "fringe left left8": {"rotate": [{"value": 5.43, "curve": [0.169, 6.36, 0.335, 7.07]}, {"time": 0.5, "value": 7.07, "curve": [1.033, 7.07, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 3.34]}, {"time": 3.2, "value": 5.43}]}, "fringe left left9": {"rotate": [{"value": 2.25, "curve": [0.335, 4.32, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1]}, {"time": 3.2, "value": 2.25}]}, "fringe left left10": {"rotate": [{"value": 5.43, "curve": [0.169, 6.36, 0.335, 7.07]}, {"time": 0.5, "value": 7.07, "curve": [1.033, 7.07, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 3.34]}, {"time": 3.2, "value": 5.43}]}, "fringe left left11": {"rotate": [{"value": 2.25, "curve": [0.335, 4.32, 0.668, 7.07]}, {"time": 1, "value": 7.07, "curve": [1.533, 7.07, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1]}, {"time": 3.2, "value": 2.25}]}, "fringe left right5": {"rotate": [{"value": 4.53}, {"time": 1.6}, {"time": 3.2, "value": 4.53}]}, "fringe left right6": {"rotate": [{"value": 8.57, "curve": [0.169, 10.05, 0.335, 11.17]}, {"time": 0.5, "value": 11.17, "curve": [1.033, 11.17, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 5.27]}, {"time": 3.2, "value": 8.57}]}, "fringe left right7": {"rotate": [{"value": 3.55, "curve": [0.335, 6.82, 0.668, 11.17]}, {"time": 1, "value": 11.17, "curve": [1.533, 11.17, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1.58]}, {"time": 3.2, "value": 3.55}]}, "fringe left right8": {"rotate": [{"value": 8.57, "curve": [0.169, 10.05, 0.335, 11.17]}, {"time": 0.5, "value": 11.17, "curve": [1.033, 11.17, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 5.27]}, {"time": 3.2, "value": 8.57}]}, "fringe left right9": {"rotate": [{"value": 3.55, "curve": [0.335, 6.82, 0.668, 11.17]}, {"time": 1, "value": 11.17, "curve": [1.533, 11.17, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1.58]}, {"time": 3.2, "value": 3.55}]}, "fringe left right10": {"rotate": [{"value": 8.57, "curve": [0.169, 10.05, 0.335, 11.17]}, {"time": 0.5, "value": 11.17, "curve": [1.033, 11.17, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 5.27]}, {"time": 3.2, "value": 8.57}]}, "fringe left right11": {"rotate": [{"value": 3.55, "curve": [0.335, 6.82, 0.668, 11.17]}, {"time": 1, "value": 11.17, "curve": [1.533, 11.17, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1.58]}, {"time": 3.2, "value": 3.55}]}, "fringe left right12": {"rotate": [{"value": 8.57, "curve": [0.169, 10.05, 0.335, 11.17]}, {"time": 0.5, "value": 11.17, "curve": [1.033, 11.17, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 5.27]}, {"time": 3.2, "value": 8.57}]}, "fringe left right13": {"rotate": [{"value": 8.57, "curve": [0.169, 10.05, 0.335, 11.17]}, {"time": 0.5, "value": 11.17, "curve": [1.033, 11.17, 1.567, 0]}, {"time": 2.1, "curve": [2.468, 0, 2.836, 5.27]}, {"time": 3.2, "value": 8.57}]}, "fringe left right14": {"rotate": [{"value": 3.55, "curve": [0.335, 6.82, 0.668, 11.17]}, {"time": 1, "value": 11.17, "curve": [1.533, 11.17, 2.067, 0]}, {"time": 2.6, "curve": [2.801, 0, 3.002, 1.58]}, {"time": 3.2, "value": 3.55}]}, "fringe right left9": {"rotate": [{"value": -1.66, "curve": [0.169, -0.72, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -7.13]}, {"time": 2.1, "value": -7.13, "curve": [2.468, -7.13, 2.836, -3.77]}, {"time": 3.2, "value": -1.66}]}, "fringe right left5": {"rotate": [{"value": -1.66, "curve": [0.169, -0.72, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -7.13]}, {"time": 2.1, "value": -7.13, "curve": [2.468, -7.13, 2.836, -3.77]}, {"time": 3.2, "value": -1.66}]}, "fringe right left6": {"rotate": [{"value": -5.88, "curve": [0.335, -1.29, 0.668, 4.82]}, {"time": 1, "value": 4.82, "curve": [1.533, 4.82, 2.067, -10.88]}, {"time": 2.6, "value": -10.88, "curve": [2.801, -10.88, 3.002, -8.66]}, {"time": 3.2, "value": -5.88}]}, "fringe right left7": {"rotate": [{"value": -5.88, "curve": [0.335, -1.29, 0.668, 4.82]}, {"time": 1, "value": 4.82, "curve": [1.533, 4.82, 2.067, -10.88]}, {"time": 2.6, "value": -10.88, "curve": [2.801, -10.88, 3.002, -8.66]}, {"time": 3.2, "value": -5.88}]}, "fringe right left8": {"rotate": [{"value": -5.88, "curve": [0.335, -1.29, 0.668, 4.82]}, {"time": 1, "value": 4.82, "curve": [1.533, 4.82, 2.067, -10.88]}, {"time": 2.6, "value": -10.88, "curve": [2.801, -10.88, 3.002, -8.66]}, {"time": 3.2, "value": -5.88}]}, "fringe right left10": {"rotate": [{"value": -5.88, "curve": [0.335, -1.29, 0.668, 4.82]}, {"time": 1, "value": 4.82, "curve": [1.533, 4.82, 2.067, -10.88]}, {"time": 2.6, "value": -10.88, "curve": [2.801, -10.88, 3.002, -8.66]}, {"time": 3.2, "value": -5.88}]}, "fringe right right5": {"rotate": [{"value": -0.36, "curve": [0.169, -0.16, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.57]}, {"time": 2.1, "value": -1.57, "curve": [2.468, -1.57, 2.836, -0.83]}, {"time": 3.2, "value": -0.36}]}, "fringe right right5b": {"rotate": [{"value": -0.36, "curve": [0.169, -0.16, 0.335, 0]}, {"time": 0.5, "curve": [1.033, 0, 1.567, -1.57]}, {"time": 2.1, "value": -1.57, "curve": [2.468, -1.57, 2.836, -0.83]}, {"time": 3.2, "value": -0.36}]}, "fringe right right5c": {"rotate": [{"value": -1.07, "curve": [0.335, -0.61, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -1.57]}, {"time": 2.6, "value": -1.57, "curve": [2.801, -1.57, 3.002, -1.34]}, {"time": 3.2, "value": -1.07}]}, "fringe right right5d": {"rotate": [{"value": -1.07, "curve": [0.335, -0.61, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -1.57]}, {"time": 2.6, "value": -1.57, "curve": [2.801, -1.57, 3.002, -1.34]}, {"time": 3.2, "value": -1.07}]}, "fringe right right5e": {"rotate": [{"curve": [0.533, 0, 1.067, -1.57]}, {"time": 1.6, "value": -1.57, "curve": [2.133, -1.57, 2.667, 0]}, {"time": 3.2}]}, "fringe right right5f": {"rotate": [{"value": -1.54, "curve": [0.501, -1.36, 1, 0]}, {"time": 1.5, "curve": [2.033, 0, 2.567, -1.57]}, {"time": 3.1, "value": -1.57, "curve": [3.134, -1.57, 3.168, -1.55]}, {"time": 3.2, "value": -1.54}]}, "fringe right right5g": {"rotate": [{"value": -1.31, "curve": [0.133, -1.46, 0.267, -1.57]}, {"time": 0.4, "value": -1.57, "curve": [0.933, -1.57, 1.467, 0]}, {"time": 2, "curve": [2.4, 0, 2.8, -0.87]}, {"time": 3.2, "value": -1.31}]}, "cloth strip right4": {"rotate": [{"value": -0.09, "curve": [0.113, -0.04, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -0.78]}, {"time": 1.9333, "value": -0.78, "curve": [2.356, -0.78, 2.78, -0.29]}, {"time": 3.2, "value": -0.09}]}, "cloth strip right5": {"rotate": [{"value": -0.4, "curve": [0.222, -0.18, 0.444, 0]}, {"time": 0.6667, "curve": [1.2, 0, 1.733, -1.06]}, {"time": 2.2667, "value": -1.06, "curve": [2.578, -1.06, 2.889, -0.7]}, {"time": 3.2, "value": -0.4}]}, "cloth strip right6": {"rotate": [{"value": -1.12, "curve": [0.335, -0.64, 0.668, 0]}, {"time": 1, "curve": [1.533, 0, 2.067, -1.64]}, {"time": 2.6, "value": -1.64, "curve": [2.801, -1.64, 3.002, -1.41]}, {"time": 3.2, "value": -1.12}]}, "cloth strip right7": {"rotate": [{"value": -1.51, "curve": [0.444, -1.13, 0.889, 0]}, {"time": 1.3333, "curve": [1.867, 0, 2.4, -1.64]}, {"time": 2.9333, "value": -1.64, "curve": [3.022, -1.64, 3.111, -1.59]}, {"time": 3.2, "value": -1.51}]}, "cloth strip right8": {"rotate": [{"value": -1.62, "curve": [0.024, -1.63, 0.045, -1.64]}, {"time": 0.0667, "value": -1.64, "curve": [0.6, -1.64, 1.133, 0]}, {"time": 1.6667, "curve": [2.179, 0, 2.691, -1.5]}, {"time": 3.2, "value": -1.62}]}, "cloth strip left4": {"rotate": [{"value": -0.05, "curve": [0.113, -0.02, 0.223, 0]}, {"time": 0.3333, "curve": [0.867, 0, 1.4, -0.46]}, {"time": 1.9333, "value": -0.46, "curve": [2.356, -0.46, 2.78, -0.17]}, {"time": 3.2, "value": -0.05}]}, "cloth strip left5": {"rotate": [{"value": -0.27, "curve": [0.222, -0.13, 0.444, 0]}, {"time": 0.6667, "curve": [1.2, 0, 1.733, -0.72]}, {"time": 2.2667, "value": -0.72, "curve": [2.578, -0.72, 2.889, -0.48]}, {"time": 3.2, "value": -0.27}]}, "cloth strip left6": {"rotate": [{"value": -0.51, "curve": [0.335, -0.17, 0.668, 0.28]}, {"time": 1, "value": 0.28, "curve": [1.533, 0.28, 2.067, -0.87]}, {"time": 2.6, "value": -0.87, "curve": [2.801, -0.87, 3.002, -0.71]}, {"time": 3.2, "value": -0.51}]}, "cloth strip left7": {"rotate": [{"value": -0.82, "curve": [0.444, -0.32, 0.889, 1.19]}, {"time": 1.3333, "value": 1.19, "curve": [1.867, 1.19, 2.4, -0.99]}, {"time": 2.9333, "value": -0.99, "curve": [3.022, -0.99, 3.111, -0.92]}, {"time": 3.2, "value": -0.82}]}, "cloth strip left8": {"rotate": [{"value": -0.97, "curve": [0.024, -0.98, 0.045, -0.99]}, {"time": 0.0667, "value": -0.99, "curve": [0.6, -0.99, 1.133, 1.19]}, {"time": 1.6667, "value": 1.19, "curve": [2.179, 1.19, 2.691, -0.81]}, {"time": 3.2, "value": -0.97}]}, "cloth strip left9": {"rotate": [{"value": -0.64, "curve": [0.133, -0.85, 0.267, -0.99]}, {"time": 0.4, "value": -0.99, "curve": [0.933, -0.99, 1.467, 1.19]}, {"time": 2, "value": 1.19, "curve": [2.4, 1.19, 2.8, -0.03]}, {"time": 3.2, "value": -0.64}]}, "cloth strip left10": {"rotate": [{"value": -0.04, "curve": [0.246, -0.53, 0.49, -0.99]}, {"time": 0.7333, "value": -0.99, "curve": [1.267, -0.99, 1.8, 1.19]}, {"time": 2.3333, "value": 1.19, "curve": [2.623, 1.19, 2.913, 0.55]}, {"time": 3.2, "value": -0.04}]}}}}}