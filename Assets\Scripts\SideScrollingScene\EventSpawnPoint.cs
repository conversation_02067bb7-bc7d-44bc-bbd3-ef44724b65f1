using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    /// <summary>
    /// 事件出生点
    /// </summary>
    public class EventSpawnPoint : MonoBehaviour
    {
        [Header("是否随机生成事件 - 默认随机")]
        public bool Random = true;
        [Header("事件对象类型 - 不填为场景默认事件对象类型")]
        public EventObjType EventObjType = EventObjType.Default;
        [Header("事件id - 不填为随机事件id")]
        public int EventId = -1;

        [Header("事件npcId")]
        public int NpcId = -1;
    }
}