using System;
using System.Collections.Generic;
using GameControl;
using UnityEngine;

namespace DataCenter
{
    public enum GameStat
    {
        /// <summary>
        /// 正在局内
        /// </summary>
        InSession,
        /// <summary>
        /// 在局外
        /// </summary>
        NotInSession
    }

    /// <summary>
    /// 对外提供数据服务
    /// </summary>
    public class DataService : MonoBehaviour
    {
        private DataProcessingCenter _dataCenter;
        private DataProcessingCenter DataCenter
        {
            get
            {
                if (_dataCenter == null)
                {
                    _dataCenter = new();
                    _dataCenter.Init();
                }
                return _dataCenter;
            }
        }

        private static DataService _instance;
        public static DataService Ins()
        {
            return _instance;
        }

        void Awake()
        {
            _instance = this;
        }

        /// <summary>
        /// 开始新游戏并加载数据
        /// </summary>
        public void StartNewGameAndLoadData()
        {
            DataCenter.StartNewGameAndLoadData();
        }

        /// <summary>
        /// 继续游戏并且加载对应slotIdx游戏数据,默认加载临时缓存
        /// </summary>
        /// <param name="index">存档插槽id - 默认为AutoSaveId</param>
        public void ContinueGameAndLoadData(int index = DataDefine.AutoSaveId)
        {
            DataCenter.ContinueGameAndLoadData(index);
        }

        /// <summary>
        /// 离开游戏对局
        /// </summary>
        public void LeaveSession()
        {
            DataCenter.SaveAutoData();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        public void SaveAllData()
        {
            DataCenter.SaveAllData();
        }


        public void SaveGlobeData()
        {
            DataCenter.SaveGlobeData();
        }


        /// <summary>
        /// 获取数据 
        /// 传入subData时，会默认返回当前session的数据 - 仅限局内使用
        /// 传入globeData, 返回全局数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <exception cref="Exception"></exception>
        public T Get<T>() where T : DataBase
        {
            Type dataType = typeof(T);
            //如果是局内数据
            if (typeof(ISubData).IsAssignableFrom(dataType))
            {

                return DataCenter.GetSessionDataById<T>(DataDefine.AutoSaveId);
            }

            //如果是全局数据
            if (typeof(IGlobeData).IsAssignableFrom(dataType))
            {
                return DataCenter.GetGlobeData<T>();
            }

            Debug.LogError($"非局内或者全局数据,请检查:{dataType}");
            return null;
        }


        /// <summary>
        /// 通过sessionSlotId，访问局内数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="idx">传入AutoSaveId会返回自动存储存档信息</param>
        /// <param name="dataType"></param>
        /// <returns></returns>
        public T GetSessionDataById<T>(int idx) where T : DataBase
        {
            Type dataType = typeof(T);
            //如果是局内数据
            if (typeof(ISubData).IsAssignableFrom(dataType))
            {
                CheckError_GameStat(GameStat.InSession, $"在局内时，尝试访问其他局内数据");
                return DataCenter.GetSessionDataById<T>(idx);
            }
            Debug.LogError($"非局内数据,请检查:{dataType}");
            return null;
        }

        /// <summary>
        /// 获取所有局内数据 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataType"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<T> GetAllSessionData<T>() where T : DataBase
        {
            Type dataType = typeof(T);
            //如果是局内数据
            if (typeof(ISubData).IsAssignableFrom(dataType))
            {
                // CheckError_GameStat(GameStat.InSession, $"在局内时，尝试访问其他局内数据");
                return DataCenter.GetAllSessionData<T>();
            }
            Debug.LogError($"非局内数据,请检查:{dataType}");
            return null;
        }

        /// <summary>
        /// 创建新的局内数据存档 - 仅限局内使用
        /// </summary>
        /// <param name="slotIndex">插槽index</param>
        public void CreateNewGameSessionSaveData(int slotIndex)
        {
            CheckError_SlotIndexValid(slotIndex);
            CheckError_GameStat(GameStat.NotInSession, $"未在局内时，尝试存储局内数据");
            DataCenter.CreateNewGameSessionSaveData(slotIndex);
        }



        /// <summary>
        /// 删除局内数据存档
        /// </summary>
        /// <param name="slotIndex"></param>
        public void DeleteSessionDataById(int slotIndex)
        {
            CheckError_SlotIndexValid(slotIndex);

            if (slotIndex == DataDefine.AutoSaveId)
            {
                CheckError_GameStat(GameStat.InSession, $"在局内时,尝试删除自动存档数据");
            }

            DataCenter.DeleteSessionDataById(slotIndex);
        }

        /// <summary>
        /// 删除所有局内数据存档
        /// </summary>
        public void DeleteAllSessionData()
        {
            CheckError_GameStat(GameStat.InSession, $"在局内时,尝试删除存档数据");
            DataCenter.DeleteAllSessionData();
        }

        public void DeleteGlobeData()
        {
            DataCenter.DeleteGlobeData();
        }

        #region  一些合法性检查
        void CheckError_SlotIndexValid(int slotIndex)
        {
            //合法性检查
            if (slotIndex > DataDefine.MaxSaveSlotCount || slotIndex < 0)
            {
                Debug.LogError("invalid save data index");
            }
        }

        /// <summary>
        /// 报错检查 
        /// </summary>
        /// <param name="stat">在什么状态下会产生报错</param>
        /// <param name="errMsg"></param>
        void CheckError_GameStat(GameStat stat, string errMsg)
        {

        }
        #endregion

    }
}