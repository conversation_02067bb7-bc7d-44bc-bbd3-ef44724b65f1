using UnityEngine;
using DG.Tweening;

namespace EventObjSystem
{
    public class ShadowMover : MonoBehaviour
    {
        [Header("箭头组引用")]
        public EventCard mEventCard;
        public GameObject Arrow_1;
        public GameObject Arrow_2;
        public GameObject Arrow_3;

        [Header("闪烁设置")]
        public float FlashDuration = 0.5f;      // 单次闪烁持续时间
        public float FlashInterval = 0.3f;      // 箭头之间的闪烁间隔
        public Color FlashColor = new Color(1f, 0.84f, 0f, 1f);  // 金黄色
        public Color NormalColor = Color.white;  // 正常颜色

        [Header("循环设置")]
        public bool IsLooping = true;
        public float LoopDelay = 1f;             // 一轮闪烁完成后的延迟时间

        private GameObject[] arrows;             // 三个箭头的引用
        private Tween[] arrowTweens;            // 每个箭头的动画引用
        private bool isMoving = false;

        void Start()
        {
            InitializeArrows();
        }

        /// <summary>
        /// 初始化三个箭头
        /// </summary>
        private void InitializeArrows()
        {
            // 直接使用三个箭头GameObject
            arrows = new GameObject[3];
            arrowTweens = new Tween[3];

            arrows[0] = Arrow_3;  // 第一个闪烁：Arrow_3
            arrows[1] = Arrow_2;  // 第二个闪烁：Arrow_2
            arrows[2] = Arrow_1;  // 第三个闪烁：Arrow_1

            // 设置所有箭头为正常颜色
            for (int i = 0; i < arrows.Length; i++)
            {
                if (arrows[i] != null)
                {
                    SetArrowColor(arrows[i].transform, NormalColor);
                }
            }
        }

        /// <summary>
        /// 开始闪烁效果
        /// </summary>
        public void StartConveyorEffect()
        {
            if (isMoving) return;

            isMoving = true;

            // 启动依次闪烁效果
            StartFlashSequence();
        }

        /// <summary>
        /// 停止闪烁效果
        /// </summary>
        public void StopConveyorEffect()
        {
            isMoving = false;

            // 停止所有箭头的动画
            for (int i = 0; i < arrowTweens.Length; i++)
            {
                arrowTweens[i]?.Kill();
                arrowTweens[i] = null;
            }

            // 重置所有箭头为正常颜色
            for (int i = 0; i < arrows.Length; i++)
            {
                if (arrows[i] != null)
                {
                    SetArrowColor(arrows[i].transform, NormalColor);
                }
            }
        }

        /// <summary>
        /// 开始闪烁序列
        /// </summary>
        private void StartFlashSequence()
        {
            // 创建主序列
            Sequence mainSequence = DOTween.Sequence();

            // 依次为每个箭头添加闪烁效果
            for (int i = 0; i < arrows.Length; i++)
            {
                if (arrows[i] != null)
                {
                    // 计算延迟时间
                    float delay = i * FlashInterval;

                    // 添加延迟
                    if (delay > 0)
                        mainSequence.AppendInterval(FlashInterval);

                    // 添加闪烁效果
                    CreateFlashTween(arrows[i].transform, mainSequence, i);
                }
            }

            // 添加循环延迟
            mainSequence.AppendInterval(LoopDelay);

            // 设置循环
            if (IsLooping)
            {
                mainSequence.SetLoops(-1);
            }

            // 保存主序列引用
            if (arrowTweens.Length > 0)
            {
                arrowTweens[0] = mainSequence;
            }
        }

        /// <summary>
        /// 创建单个箭头的闪烁效果
        /// </summary>
        private void CreateFlashTween(Transform arrow, Sequence parentSequence, int index)
        {
            if (arrow == null) return;

            // 创建闪烁序列：正常色 -> 金黄色 -> 正常色
            Sequence flashSequence = DOTween.Sequence();

            // 变为金黄色
            Color currentColor = NormalColor;
            Tween flashToGold = DOTween.To(() => currentColor,
                color =>
                {
                    currentColor = color;
                    SetArrowColor(arrow, color);
                },
                FlashColor, FlashDuration * 0.5f);
            flashSequence.Append(flashToGold);

            // 变回正常色
            Tween flashToNormal = DOTween.To(() => currentColor,
                color =>
                {
                    currentColor = color;
                    SetArrowColor(arrow, color);
                },
                NormalColor, FlashDuration * 0.5f);
            flashSequence.Append(flashToNormal);

            // 将闪烁序列添加到父序列
            parentSequence.Append(flashSequence);
        }

        /// <summary>
        /// 设置箭头颜色
        /// </summary>
        private void SetArrowColor(Transform target, Color color)
        {
            if (target == null) return;

            // 尝试获取SpriteRenderer组件
            SpriteRenderer spriteRenderer = target.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                spriteRenderer.color = color;
                return;
            }
        }

        /// <summary>
        /// 暂停/恢复闪烁效果
        /// </summary>
        public void TogglePause()
        {
            // 只需要控制主序列（存储在第一个位置）
            if (arrowTweens.Length > 0 && arrowTweens[0] != null)
            {
                if (arrowTweens[0].IsPlaying())
                    arrowTweens[0].Pause();
                else
                    arrowTweens[0].Play();
            }
        }

        void OnDestroy()
        {
            StopConveyorEffect();
        }


    }
}