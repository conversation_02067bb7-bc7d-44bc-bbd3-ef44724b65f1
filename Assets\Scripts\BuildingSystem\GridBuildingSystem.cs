using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Common;
using Player;
using UI;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Rendering.Universal;
using UnityEngine.Tilemaps;
using UnityEngine.UI;

enum TileType
{
    Empty,
    White,
    Green,
    Red,
    Black
}

public class GridBuildingSystem : MonoSingleton<GridBuildingSystem>
{
    public static UInt64 s_buildingID = 0;

    public static UInt64 GenBuildingID()
    {
        s_buildingID += 1;
        return s_buildingID;
    }

    private Dictionary<TileType, TileBase> _tilesCreatorDict = new Dictionary<TileType, TileBase>();
    public Tilemap MainTilemap;
    public Tilemap TempTilemap;
    public GridLayout Grid;

    private Building _curMoveBuilding;

    private Dictionary<Vector3Int, TileBase> _tilesDict = new Dictionary<Vector3Int, TileBase>();

    public GameObject BuildingPrefab;

    public GameObject BuildingContainer;

    public GameObject EditModeMask;

    public bool IsEditMode = false;

    private Camera _camera;

    private Dictionary<UInt64, Building> _curMapBuildingDict = new Dictionary<UInt64, Building>();

    private List<FarmingBuilding> _allFarmingBuildingList;

    private GameObject EditModeUIRoot;

    public GameObject escortMapGo;
    
    private void InitBuilding()
    {
        var allBuilding = BuildingContainer.GetComponentsInChildren<Building>();
        foreach (var building in allBuilding)
        {
            var buildingID = GenBuildingID();
            _curMapBuildingDict.Add(buildingID, building);
            var cell = Grid.WorldToCell(building.transform.position);
            building.BuildingArea.position = new Vector3Int(cell.x, cell.y, 0);
            var cellPos = cell + new Vector3(0.5f, 0.5f, 0f);
            var localInterPos = Grid.CellToLocalInterpolated(cellPos);
            // building.transform.localPosition = new Vector3(localInterPos.x, localInterPos.y, 0);
        }

        var allFarmingBuilding = BuildingContainer.GetComponentsInChildren<FarmingBuilding>();
        _allFarmingBuildingList = allFarmingBuilding.ToList();
    }

    public void RotateCurMovingBuilding()
    {
        ClearMovingBuildingArea();
        _curMoveBuilding.RotateBuilding();
        RefreshMovingBuildingTiles();
    }

    protected override void Awake()
    {
        base.Awake();
        _camera = Camera.main;
        InitTiles();

        _tilesCreatorDict.Add(TileType.White, Resources.Load<TileBase>("white"));
        _tilesCreatorDict.Add(TileType.Green, Resources.Load<TileBase>("green"));
        _tilesCreatorDict.Add(TileType.Red, Resources.Load<TileBase>("red"));
        _tilesCreatorDict.Add(TileType.Black, Resources.Load<TileBase>("black"));
        
        MainTilemap.gameObject.SetActive(false);
        TempTilemap.gameObject.SetActive(false);
        // InitBuilding();
        // AddBuilding(0, new Vector3(0 ,0 ,0));
        // AddBuilding(1, new Vector3(-3.35f, -1.6f,0));
    }

    private void OnClickBtnNextDay()
    {
        PlayerEntity.Instance.NextDay();
    }

    private void Start()
    {
        EditModeUIRoot = UIManager.Instance.GetUIRoot().transform.Find("EditModeUI").gameObject;
        InitBuilding();
        InitClickListener();
        RefreshAllBuildingOrder();
        RefreshAllBuildingPosByArea();
    }

    private void InitClickListener()
    {
        var editBtn = GameObject.Find("UIRoot/HUD/main_ui_hud/pnl_lt/pnl_items/edit_item/img_di")
            .GetComponent<Button>();
        editBtn.onClick.AddListener(OnClickBtnEdit);
        
        var exitEditBtn = EditModeUIRoot.transform.Find("main_ui_hud/pnl_lt/pnl_items/edit_item/img_di")
            .GetComponent<Button>();
        exitEditBtn.onClick.AddListener(OnClickBtnExitEdit);

        var nextDayBtn = GameObject.Find("UIRoot/HUD/main_ui_hud/pnl_lt/pnl_time/next_day_btn").GetComponent<Button>();
        nextDayBtn.onClick.AddListener(OnClickBtnNextDay);
        
        var escortBtn = GameObject.Find("UIRoot/HUD/main_ui_hud/pnl_lt/pnl_items/escort_item/img_di")
            .GetComponent<Button>();
        escortBtn.onClick.AddListener(OnClickEscort);

    }

    private void OnClickEscort()
    {
        escortMapGo.SetActive(true);
    }

    private bool _isDragBuilding = false;
    private Vector3 _dragBuildingOffset = Vector3.zero;
    
    private void ClearMovingBuildingArea(bool clearMain = false)
    {
        var buildingArea = _curMoveBuilding.BuildingArea;
        var size = buildingArea.size.x * buildingArea.size.y * buildingArea.size.z;
        var tmpTiles = new TileBase[size];
        for (var index = 0; index < size; index++)
        {
            tmpTiles[index] = null;
        }

        TempTilemap.SetTilesBlock(buildingArea, tmpTiles);
        if (clearMain)
        {
            for (var index = 0; index < size; index++)
            {
                tmpTiles[index] = _tilesCreatorDict[TileType.White];
            }

            MainTilemap.SetTilesBlock(buildingArea, tmpTiles);
        }
    }

    private void RefreshAllBuildingPosByArea()
    {
        foreach (var buildingComp in _curMapBuildingDict.Values)
        {
            var halfCellSize = Grid.CellToLocalInterpolated(new Vector3(0.25f, 0.25f, 0)) -
                               Grid.CellToLocalInterpolated(new Vector3(0.0f, 0, 0));
            var worldPos = Grid.CellToWorld(buildingComp.BuildingArea.position);
            // localInterPos -= halfCellSize;
            buildingComp.transform.position = new Vector3(worldPos.x, worldPos.y, buildingComp.transform.position.z);
        }
    }

    public void OnClickBuilding(Building buildingComp)
    {
        if(!IsEditMode || _curMoveBuilding != null)
            return;
        _curMoveBuilding = buildingComp;
        buildingComp.EnterEditMode();
        buildingComp.SetSpriteOrder(999);
        ClearMovingBuildingArea(true);
        RefreshMovingBuildingTiles();
    }

    public void OnBeginDragBuilding(Building buildingComp, PointerEventData pointerEventData)
    {
        if(_curMoveBuilding == null || _curMoveBuilding != buildingComp)
            return;
        var buildingWorldPos = _curMoveBuilding.transform.position;
        var touchWorldPos = _camera.ScreenToWorldPoint(pointerEventData.position);
        _dragBuildingOffset = buildingWorldPos - touchWorldPos;
        _isDragBuilding = true;
        ClearMovingBuildingArea();
        RefreshMovingBuildingTiles();
    }

    public void OnDragBuilding(Building buildingComp, PointerEventData pointerEventData)
    {
        if (!IsEditMode || !_isDragBuilding)
            return;
        if(_curMoveBuilding == null || _curMoveBuilding != buildingComp)
            return;
        
        var touchWorldPos = _camera.ScreenToWorldPoint(pointerEventData.position);
        var buildingWorldPos = _dragBuildingOffset + touchWorldPos;
        buildingWorldPos = new Vector3(buildingWorldPos.x, buildingWorldPos.y, _curMoveBuilding.transform.position.z);
        _curMoveBuilding.transform.position = buildingWorldPos;
        RefreshMovingBuildingTiles();
        var worldPos = Grid.CellToWorld(_curMoveBuilding.BuildingArea.position);
        // localInterPos -= halfCellSize;
        _curMoveBuilding.transform.position = new Vector3(worldPos.x, worldPos.y, _curMoveBuilding.transform.position.z);
    }

    public void OnDragEndBuilding(Building buildingComp)
    {
        _isDragBuilding = false;

    }

    private int BuildingSortFunc(Building a, Building b)
    {
        var aCellX = Grid.WorldToCell(a.transform.position).x;
        var aCellY = Grid.WorldToCell(a.transform.position).y;
        
        var bCellX = Grid.WorldToCell(b.transform.position).x;
        var bCellY = Grid.WorldToCell(b.transform.position).y;
        if ((aCellX + aCellY) != (bCellX + bCellY))
        {
            return (bCellX + bCellY).CompareTo((aCellX + aCellY));
        }

        foreach (var bPos in b.BuildingArea.allPositionsWithin)
        {
            if (bPos.x >= aCellX && bPos.y >= aCellY)
            {
                return 1;
            }
        }
        
        foreach (var aPos in a.BuildingArea.allPositionsWithin)
        {
            if (aPos.x >= bCellX && aPos.y >= bCellY)
            {
                return -1;
            }
        }

        ;
        if (aCellX != bCellX)
        {
            return bCellX.CompareTo(aCellX);
        }

        return bCellY.CompareTo(aCellY);
    }

    private void RefreshAllBuildingOrder()
    {
        var allBuildingList = new List<Building>(_curMapBuildingDict.Values);
        allBuildingList.Sort(BuildingSortFunc);
        int curOrder = 0;
        for (int index = 0; index < allBuildingList.Count; index++)
        {
            var building = allBuildingList[index];
            curOrder = building.SetSpriteOrder(curOrder);
        }
    }


    private void RefreshMovingBuildingTiles()
    {
        var buildingArea = _curMoveBuilding.BuildingArea;
        var size = buildingArea.size.x * buildingArea.size.y * buildingArea.size.z;
        var tmpTiles = new TileBase[size];
        for (var index = 0; index < size; index++)
        {
            tmpTiles[index] = null;
        }

        TempTilemap.SetTilesBlock(buildingArea, tmpTiles);
        var halfCellSize = Grid.CellToLocalInterpolated(new Vector3(0.25f, 0.25f, 0)) -
                           Grid.CellToLocalInterpolated(new Vector3(0.0f, 0, 0));
        var cell = Grid.WorldToCell(_curMoveBuilding.transform.position);
        // _curMoveBuilding.BuildingArea.position = new Vector3Int(cell.x - buildingArea.size.x/2, cell.y- buildingArea.size.y/2, 0);
        _curMoveBuilding.BuildingArea.position = new Vector3Int(cell.x, cell.y, 0);
        bool needRed = false;
        foreach (var v in _curMoveBuilding.BuildingArea.allPositionsWithin)
        {
            if (!MainTilemap.HasTile(v))
            {
                needRed = true;
                break;
            }

            if (MainTilemap.GetTile(v) == _tilesCreatorDict[TileType.Black])
            {
                needRed = true;
                break;
            }
        }

        TileBase targetTile = needRed ? _tilesCreatorDict[TileType.Red] : _tilesCreatorDict[TileType.Green];
        tmpTiles = new TileBase[size];
        for (var index = 0; index < size; index++)
        {
            tmpTiles[index] = targetTile;
        }

        TempTilemap.SetTilesBlock(_curMoveBuilding.BuildingArea, tmpTiles);
    }
    void OnClickBtnEdit()
    {
        EnterEditMode();
    }

    void OnClickBtnExitEdit()
    {
        ExitEditMode(false, false);
    }

    void ShowAllBuildingTile()
    {
        foreach (var building in _curMapBuildingDict.Values)
        {
            var buildingArea = building.BuildingArea;
            var size = buildingArea.size.x * buildingArea.size.y * buildingArea.size.z;
            var tmpTiles = new TileBase[size];
            for (var index = 0; index < size; index++)
            {
                tmpTiles[index] = _tilesCreatorDict[TileType.Black];
            }

            MainTilemap.SetTilesBlock(building.BuildingArea, tmpTiles);
        }
    }

    void AllBuildingExitEditMode(bool isSave)
    {
        foreach (var building in _curMapBuildingDict.Values)
        {
            building.ExitEditMode(isSave);
        }
    }

    public void EnterEditMode()
    {
        IsEditMode = true;
        EditModeMask.SetActive(true);
        foreach (var farmingBuilding in _allFarmingBuildingList)
        {
            farmingBuilding.HideOperateItem();
        }

        TempTilemap.gameObject.SetActive(true);
        ClearTempMap();
        MainTilemap.gameObject.SetActive(true);
        ShowAllBuildingTile();
        UIManager.Instance.HideHUD();
        EditModeUIRoot.SetActive(true);
        EventManager.Instance.TriggerEvent(EventName.EnterEditMode, this);
    }

    private bool CanMovingBuildingPlaced()
    {
        bool canPlaced = true;
        foreach (var v in _curMoveBuilding.BuildingArea.allPositionsWithin)
        {
            if (!MainTilemap.HasTile(v))
            {
                canPlaced = false;
                break;
            }

            if (MainTilemap.GetTile(v) == _tilesCreatorDict[TileType.Black])
            {
                canPlaced = false;
                break;
            }
        }

        return canPlaced;
    }

    public void ExitEditMode(bool isSave, bool reEnterEdit=false)
    {
        if (isSave && !CanMovingBuildingPlaced())
            return;
        IsEditMode = false;
        EditModeMask.SetActive(false);
        MainTilemap.gameObject.SetActive(false);
        TempTilemap.gameObject.SetActive(false);
        AllBuildingExitEditMode(isSave);
        RefreshAllBuildingPosByArea();
        RefreshAllBuildingOrder();
        _curMoveBuilding = null;
        UIManager.Instance.ShowHUD();
        EditModeUIRoot.SetActive(false);
        EventManager.Instance.TriggerEvent(EventName.ExitEditMode, this);
        if(reEnterEdit)
            EnterEditMode();
    }

    private void InitTiles()
    {
        var bounds = MainTilemap.cellBounds;
        foreach (var v in bounds.allPositionsWithin)
        {
            if (MainTilemap.HasTile(v))
            {
                _tilesDict[v] = MainTilemap.GetTile(v);
            }
        }
    }

    private void ClearTempMap()
    {
        var bounds = TempTilemap.cellBounds;
        foreach (var v in bounds.allPositionsWithin)
        {
            TempTilemap.SetTile(v, null);
        }
    }
}