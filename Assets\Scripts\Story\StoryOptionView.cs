using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace Story
{
    public class StoryOptionView : MonoBehaviour
    {
        [SerializeField]
        private GameObject _optionItemRoot;
        [SerializeField]
        private GameObject _optionItemPrefab;
        private List<string> _curOptionList = null;

        private Action<string> _onChoiceOption = null;
        // Start is called before the first frame update
        public void ShowOptions(List<string> optionList, Action<string> onChoiceOption)
        {
            _curOptionList = optionList;
            _onChoiceOption = onChoiceOption;

            for (int index = 0; index < _optionItemRoot.transform.childCount; index++)
            {
                var child = _optionItemRoot.transform.GetChild(index);
                Destroy(child.gameObject);
            }

            foreach (string optionStr in optionList)
            {
                var optionGo = Instantiate(_optionItemPrefab, Vector3.zero, Quaternion.identity,
                    _optionItemRoot.transform);
                var optionText = optionGo.transform.Find("option_btn/option_txt").GetComponent<TMP_Text>();
                string str = ExcelDataMgr.Instance.Translate<OptionTranslation>(optionStr);
                optionText.text = str;
                var optionBtn = optionGo.transform.Find("option_btn").GetComponent<Button>();
                optionBtn.onClick.AddListener(() =>
                {
                    onChoiceOption?.Invoke(optionStr);
                });
            }
        }
    }

}