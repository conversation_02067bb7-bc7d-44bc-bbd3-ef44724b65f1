﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public static class GoUtil
{
    private const string TAG_UNTAGGED = "Untagged";

    public static void TransformPoint(GameObject parent, GameObject child, out float x, out float y, out float z)
    {
        Vector3 result = parent.transform.TransformPoint(child.transform.localPosition);
        x = result.x;
        y = result.y;
        z = result.z;
    }

    public static void InverseTransformPoint(GameObject to, GameObject from, out float x, out float y, out float z)
    {
        Vector3 result = to.transform.InverseTransformPoint(from.transform.position);
        x = result.x;
        y = result.y;
        z = result.z;
    }

    public static Vector2 ScreenPointToLocalPointInRectangle(GameObject go, Vector2 screenPos, Camera uiCamera)
    {
        Vector2 ret;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(go.transform as RectTransform, screenPos, uiCamera, out ret);
        return ret;
    }

    public static void SetImageAlpha(Image img, float alpha)
    {
        Color color = img.color;
        color.a = alpha;
        img.color = color;
    }

    public static float GetImageAlpha(Image img)
    {
        Color color = img.color;
        return color.a;
    }

    public static void SetCollider(GameObject go, bool isEnable)
    {
        BoxCollider comp = go.GetComponent<BoxCollider>();
        if (comp != null)
        {
            comp.enabled = isEnable;
        }
    }

    public static void SetCastShadow(GameObject go, bool isCast)
    {
        MeshRenderer renderer = go.GetComponent<MeshRenderer>();
        if (renderer != null)
        {
            if (isCast)
            {
                renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;
            }
            else
            {
                renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            }
        }
    }

    public static void CleanAllChildren(GameObject go, GameObject exceptGo = null)
    {
        var trans = go.transform;
        for (int index = 0; index < trans.childCount; index++)
        {
            var childGO = trans.GetChild(index).gameObject;
            if (childGO != exceptGo)
                UnityEngine.Object.Destroy(childGO);
        }
    }

    public static void SetUIVisible(this GameObject root, bool visible)
    {
        if (visible)
        {
            RectTransform rect = root.GetComponent<RectTransform>();
            if (rect == null)
            {
                return;
            }
            Vector3 position = rect.anchoredPosition3D;
            position.z = 0;
            rect.anchoredPosition3D = position;
        }
        else
        {
            Vector3 position = root.transform.position;
            position.z = -100;
            root.transform.position = position;
        }
    }

    public static void ForceRebuildLayoutImmediate(this GameObject root)
    {
        RectTransform rect = root.GetComponent<RectTransform>();
        if (rect == null)
        {
            return;
        }
        LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
    }

    public static GameObject FindFirstTaggedParent(GameObject go)
    {
        int count = 0;
        Transform tempTransform = go.transform;
        while (tempTransform.CompareTag(TAG_UNTAGGED))
        {
            count++;
            tempTransform = tempTransform.parent;
            if (count >= 10)
            {
                return go;
            }
        }
        return tempTransform.gameObject;
    }

    public static GameObject FindChildRecursively(this GameObject root, string name)
    {
        Transform child = null;
        try
        {
            int count = root.transform.childCount;
            for (int i = 0; i < count; i++)
            {
                Transform tf = root.transform.GetChild(i);
                if (tf != null && tf.gameObject.name == name)
                {
                    child = tf;
                    break;
                }
            }

            if (child == null)
            {
                for (int i = 0; i < count; i++)
                {
                    Transform tf = root.transform.GetChild(i);
                    GameObject childGo = FindChildRecursively(tf.gameObject, name);
                    if (childGo != null)
                    {
                        child = childGo.transform;
                        break;
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"{e}");
        }

        if (child != null)
        {
            return child.gameObject;
        }

        return null;
    }

    public static GameObject FindSceneRootChild(string name)
    {
        foreach (GameObject rootObj in UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects())
        {
            if (name == rootObj.name)
            {
                return rootObj;
            }
        }
        return null;
    }
}
