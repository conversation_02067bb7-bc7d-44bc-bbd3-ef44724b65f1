using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Common
{
    public class GlobalTouchTrigger : MonoBehaviour
	{

		//拖动相关
		public delegate void DragCallBack(Vector3 vec);

		private DragCallBack _dragCallBack;

		//拖动开始相关
		public delegate void DragBeginCallBack(Vector3 vec);

		private DragBeginCallBack _dragBeginCallBack;
		

		//拖动结束相关
		public delegate void DragEndCallBack(Vector3 vec);

		private DragEndCallBack _dragEndCallBack;
		
		//点击开始相关
		public delegate void DownCallBack(Vector3 vec);

		private DownCallBack _downCallBack;

		//点击起来相关
		public delegate void UpCallBack(RaycastHit hit, Vector2 mousePos, bool is_click_up);

		private UpCallBack _upCallBack;

		Vector3 _downPosVec3 = Vector3.zero;  //鼠标按下的位置
		Vector2 _downPosVec2 = Vector2.zero;

		//是否忽略UI穿透
		private bool isIgnoreUIThrough = false;
		//静态控制值，当需要全局停止所有实例化的组件时控制
		public static bool Stop = false;
		float _lastFingerDistance = 0f;

		float DRAG_THRESHOLD = 10.0f;

		private TouchMode touchMode;

		bool isJustDown = false;
		
		//点击碰撞检测相关
		public int maxDistance = 1000;
		private RaycastHit[] raycastHits = new RaycastHit[10];
		private Dictionary<string, int> tagWeightTable = new Dictionary<string, int>();
		private Camera _mainCamera;

		private int _layerMask;
		public int LayerMask
		{
			set { _layerMask = value; }
			get { return _layerMask; }
		}
		public static GlobalTouchTrigger Get(GameObject go)
		{
			GlobalTouchTrigger trigger = go.GetComponent<GlobalTouchTrigger>();
			if (trigger == null)
			{
				trigger = go.AddComponent<GlobalTouchTrigger>();
			}
			return trigger;
		}

		// Use this for initialization
		void Awake()
		{
			_mainCamera = Camera.main;
			_layerMask = UnityEngine.LayerMask.GetMask("Default");
		}

		public void SetIsIgnoreUIThrough(bool isIgnoreUIThrough = false)
		{
			this.isIgnoreUIThrough = isIgnoreUIThrough;
		}

		//注册拖动开始事件
		public void AddDragBeginListener(DragBeginCallBack callBack)
		{
			_dragBeginCallBack += callBack;
		}

		//注册拖动事件
		public void AddDragListener(DragCallBack callBack)
		{
			_dragCallBack += callBack;
		}
		//注册拖动结束事件
		public void AddDragEndListener(DragEndCallBack callback)
		{
			_dragEndCallBack += callback;
		}
		//注册点击开始事件
		public void AddDownListener(DownCallBack callback)
		{
			_downCallBack += callback;
		}

		public void AddUpListener(UpCallBack callBack)
		{
			_upCallBack += callBack;
		}

		public void SetTagHitWeight(string tag, int weight)
		{
			tagWeightTable[tag] = weight;
		}



		private void sort(RaycastHit[] raycastHits, int count)
		{
			RaycastHit tempA, tempB;
			for (int i = 0; i < count - 1; i++)
			{
				for (int j = i + 1; j < count; j++)
				{
					tempA = raycastHits[i];
					tempB = raycastHits[j];
					if (compare(tempA, tempB) > 0)
					{
						raycastHits[i] = tempB;
						raycastHits[j] = tempA;
					}
				}
			}
		}

		private int compare(RaycastHit hitA, RaycastHit hitB)
		{
			int weighA = 0, weithB = 0;
			if (hitA.collider != null)
			{
				GameObject goA = GoUtil.FindFirstTaggedParent(hitA.collider.gameObject);
				tagWeightTable.TryGetValue(goA.tag, out weighA);
			}
			if (hitB.collider != null)
			{
				GameObject goB = GoUtil.FindFirstTaggedParent(hitB.collider.gameObject);
				tagWeightTable.TryGetValue(goB.tag, out weithB);
			}

			if (weighA == weithB)
			{
				return hitB.point.z < hitA.point.z ? 1 : -1;
				//return -1;
			}
			return weithB - weighA;
		}

		private void OnDragBegin(Vector2 vector3)
		{
			_dragBeginCallBack?.Invoke(vector3);
		}

		private void OnDrag(Vector2 vector3)
		{
			_dragCallBack?.Invoke(vector3);
		}

		private void OnDragEnd(Vector2 vector3)
		{
			_dragEndCallBack?.Invoke(vector3);
		}

		private void OnDown(Vector2 vector3)
		{
			_downCallBack?.Invoke(vector3);
		}

		private void OnUp(Vector2 screenPos, bool isClickUp)
		{

			if (_upCallBack != null)
			{
				Ray ray = _mainCamera.ScreenPointToRay(screenPos);
				int count = Physics.RaycastNonAlloc(ray, raycastHits, maxDistance, _layerMask);
				RaycastHit hit = default;
				Collider collider = null;
				Vector3 hitPos = new Vector3();
				if (count > 0)
				{
					sort(raycastHits, count);
					hit = raycastHits[0];
					Array.Clear(raycastHits, 0, count);
					collider = hit.collider;
					hitPos = hit.point;
				}

				_upCallBack.Invoke(hit, screenPos, isClickUp);
			}
		}

		//移除监听事件
		public void RemoveTouchListener()
		{
			_dragCallBack = null;
			_dragBeginCallBack = null;
			_dragEndCallBack = null;
			_downCallBack = null;
			_upCallBack = null;
		}

		Vector3 offset = Vector3.zero;

		/// <summary>
		/// 是否允许触摸操作，就是判断 是否不忽略ui 并且 是在ui上
		/// </summary>
		/// <returns>true 表示允许操作，false 表示不能操作</returns>
		bool _IsCanTouch()
		{
			if (!this.isIgnoreUIThrough && IsPointerOverUIObject())
			{
				return false;
			}
			else
			{
				if (results != null)
				{
					results.Clear();
				}
			}
			return true;
		}

        void OnEnable()
        {
            touchMode = TouchMode.NONE;
        }


		void Update()
		{
			if (Stop)
				return;
			int touchCount = GetTouchCount();
			Vector2 mousePosition = GetMousePosition();
            //windows
            //if (OSDef.RunOS == OSDef.Win32 || OSDef.RunOS == OSDef.Mac)
            //{
			if (IsDown()) //鼠标按下事件
			{
				if (!_IsCanTouch())
					return;
				if (touchMode == TouchMode.NONE)
				{
					touchMode = TouchMode.CLICK;
					//onDown(Input.mousePosition);
					isJustDown = true;
					_downPosVec3 = mousePosition;
					OnDown(mousePosition);
				}
				if (touchCount > 1)
				{
					if (this.touchMode == TouchMode.DRAG)
					{
						OnDragEnd(mousePosition);
					}
					this.touchMode = TouchMode.MULT;
					this._lastFingerDistance = GetMultDistance();
				}


				//Vector3 pos = Input.mousePosition;

			}

			if (IsUp())
			{    //鼠标松开事件
				if (_IsCanTouch())
				{
					//Debug.Log("mouse up"+ touchCount + this.isJustDown);
					if (this.touchMode == TouchMode.CLICK)
					{
						//if (Vector2.Distance(mousePosition, _downPosVec3) < DRAG_THRESHOLD)
						//{
						//	OnClick(mousePosition);
						//}
					}
					else if (this.touchMode == TouchMode.DRAG)
					{
						OnDragEnd(mousePosition);
					}
					else if (this.touchMode == TouchMode.MULT && touchCount < 1)
					{
						this.touchMode = TouchMode.NONE;
					}
					OnUp(mousePosition, touchCount == 1 && this.isJustDown);
				}
				this.touchMode = TouchMode.NONE;
				if (touchCount == 1)
					this.isJustDown = false;
			}

			if (IsMove())  //拖动事件
			{
				if (!_IsCanTouch())
					return;
				if (touchMode == TouchMode.NONE)
					return;
				if (touchMode == TouchMode.CLICK)
				{
					if (Vector2.Distance(mousePosition, _downPosVec3) > DRAG_THRESHOLD)
					{
						OnDragBegin(mousePosition);
                        this.touchMode = TouchMode.DRAG;
					}

				}
				if (touchMode == TouchMode.DRAG)
				{
					OnDrag(mousePosition);
				}
				else if (touchMode == TouchMode.MULT)
				{
					float nowDis = GetMultDistance();
					float offset = nowDis - this._lastFingerDistance;
					if (Math.Abs(offset) > DRAG_THRESHOLD)
					{
						this._lastFingerDistance = nowDis;
					}
				}
			}
		}

		private Vector2 GetMousePosition()
		{
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			return Input.mousePosition;
#elif (UNITY_IOS || UNITY_ANDROID)
			if(Input.touchCount > 0)
			{
				var touch = Input.GetTouch(0);
				return touch.position;
			}
			else
			{
				return new Vector2();
			}
#else
			return new Vector2();
#endif
		}

		private float GetMultDistance()
		{
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			return Input.mousePosition.magnitude;
#elif (UNITY_IOS || UNITY_ANDROID)
			if(Input.touchCount >= 2)
			{
				Touch tp0 = Input.GetTouch(0);
				Touch tp1 = Input.GetTouch(1);
				return Vector2.Distance(tp0.position, tp1.position);
			}
			else
			{
				return 0;
			}
#else
			return 0;
#endif
		}

		private int GetTouchCount()
		{
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			int count = (Input.GetMouseButton(0) || Input.GetMouseButtonUp(0) || Input.GetMouseButtonUp(0)) ? 1 : 0;
			if (Input.GetKey(KeyCode.LeftControl) || Input.GetKeyDown(KeyCode.LeftControl) || Input.GetKeyUp(KeyCode.LeftControl))
				count++;
			return count;
#elif (UNITY_IOS || UNITY_ANDROID)
			return Input.touchCount;
#else
			return 0;
#endif
		}

		private bool IsUp()
		{
			//windows
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			return Input.GetMouseButtonUp(0) || Input.GetKeyUp(KeyCode.LeftControl);
#elif (UNITY_IOS || UNITY_ANDROID)
			int touchCount = Input.touchCount;
			for(int i=0; i<touchCount; i++)
			{
				if (Input.GetTouch(i).phase == TouchPhase.Ended)
					return true;
			}
			return false;
#else
			return false;
#endif

		}

		private bool IsDown()
		{
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			return Input.GetMouseButtonDown(0) || Input.GetKeyDown(KeyCode.LeftControl);
#elif (UNITY_IOS || UNITY_ANDROID)
			int touchCount = Input.touchCount;
			for (int i = 0; i < touchCount; i++)
			{
				if (Input.GetTouch(i).phase == TouchPhase.Began)
					return true;
			}
			return false;
#else
			return false;
#endif
		}
		private bool IsMove()
		{
#if (UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX)
			return Input.GetMouseButton(0) || Input.GetKey(KeyCode.LeftControl);
#elif (UNITY_IOS || UNITY_ANDROID)
			int touchCount = Input.touchCount;
			for (int i = 0; i < touchCount; i++)
			{
				if (Input.GetTouch(i).phase == TouchPhase.Moved)
					return true;
			}
			return false;
#else
		return false;
#endif
		}

		void OnDestroy()
		{
			if (eventDataCurrentPosition != null)
			{
				eventDataCurrentPosition = null;
			}
			if (results != null)
			{
				results.Clear();
				results = null;
			}
			RemoveTouchListener();
		}

		PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current);
		List<RaycastResult> results = new List<RaycastResult>();
		//通过UI事件发射射线
		//是 2D UI 的位置，非 3D 位置
		public bool IsPointerOverUIObject()
		{
			var pos = GetMousePosition();
			if (pos.x < 0 || pos.x > Screen.width || pos.y < 0 || pos.y > Screen.height)
				return true;
			eventDataCurrentPosition.position = pos;
			//向点击处发射射线
			EventSystem.current.RaycastAll(eventDataCurrentPosition, results);

			return results.Count > 0;
		}


		////精度
		//const float EPSINON = 0.000001f;

		//bool ComparePositionIsEquals(Vector3 pos1, Vector3 pos2)
		//{
		//	if (IsEqualsZero(pos1.x - pos2.x) && IsEqualsZero(pos1.y - pos2.y) && IsEqualsZero(pos1.z - pos2.z))
		//	{
		//		return true;
		//	}
		//	return false;
		//}

		//bool IsEqualsZero(float f)
		//{
		//	return (f >= -EPSINON && f <= EPSINON);
		//}

		private enum TouchMode
		{
			NONE,
			CLICK,
			DRAG,
			MULT,
		}
	}
}
