using UnityCommunity.UnitySingleton;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace GameSceneService
{
    /// <summary>
    /// 提供场景服务
    /// </summary>
    public class SceneService : MonoSingleton<SceneService>
    {
        public CanvasGroup fadeCanvasGroup;

        public float fadeDuration;

        public SceneType startEnumScene = SceneType.BeginScene;

        /// <summary>
        /// 当前所在场景
        /// </summary>
        [SerializeField]
        private string _currentScene = String.Empty;
        public string CurrentScene
        {
            get { return _currentScene; }
        }

        private Dictionary<string, SceneBase> _sceneMaps = new();

        private Coroutine _coroutine;

        /// <summary>
        /// 如果场景需要有生命周期进行管理 - 注册场景实例
        /// </summary>
        private void RegistScenes()
        {
            string[] sceneTypeNames = Enum.GetNames(typeof(SceneType));
            for (int i = 0; i < sceneTypeNames.Length; i++)
            {
                Type type = Type.GetType(sceneTypeNames[i]);
                if (type != null)
                {
                    var cls = Activator.CreateInstance(type) as SceneBase;
                    if (cls != null)
                    {
                        _sceneMaps.Add(sceneTypeNames[i], cls);
                    }
                }
            }
        }

        private void Start()
        {
            RegistScenes();
            _currentScene = String.Empty;
            Transition(EnumUtils.GetName(startEnumScene));
        }

        public override void ClearSingleton()
        {
            _sceneMaps.Clear();
            _sceneMaps = null;
            if (_coroutine != null)
            {
                StopCoroutine(_coroutine);
            }
        }


        #region  局内持久化场景相关
        // 判断局内持久化场景是否已加载
        public bool IsInsidePersistentLoaded()
        {
            for (int i = 0; i < SceneManager.sceneCount; i++)
            {
                Scene scene = SceneManager.GetSceneAt(i);
                if (scene.name == EnumUtils.GetName(SceneType.InsidePersistent) && scene.isLoaded)
                {
                    return true;
                }
            }
            return false;
        }


        // 激活局内持久場景（异步）
        public void LoadInsidePersistent(Action onComplete = null)
        {
            // 先检查场景是否已加载
            if (IsInsidePersistentLoaded())
            {
                Debug.Log("局内持久化场景已经加载，无需重复加载");
                onComplete?.Invoke();
                return;
            }

            StartCoroutine(LoadInsidePersistentAsync(onComplete));
        }

        // 异步加载局内持久场景的协程
        private IEnumerator LoadInsidePersistentAsync(Action onComplete)
        {
            AsyncOperation asyncOperation = SceneManager.LoadSceneAsync(
              EnumUtils.GetName(SceneType.InsidePersistent),
                LoadSceneMode.Additive
            );

            // 等待场景加载完成
            while (!asyncOperation.isDone)
            {
                yield return null;
            }

            Debug.Log("局内持久化场景加载完成");

            // 加载完成后执行回调
            onComplete?.Invoke();
        }

        // 卸载局内持久场景（异步）
        public void UnloadInsidePersistent(Action onComplete = null)
        {
            // 先检查场景是否已加载
            if (!IsInsidePersistentLoaded())
            {
                Debug.Log("局内持久化场景未加载，无需卸载");
                onComplete?.Invoke();
                return;
            }

            StartCoroutine(UnloadInsidePersistentAsync(onComplete));
        }

        // 异步卸载局内持久场景的协程
        private IEnumerator UnloadInsidePersistentAsync(Action onComplete)
        {
            AsyncOperation asyncOperation = SceneManager.UnloadSceneAsync(EnumUtils.GetName(SceneType.InsidePersistent));

            // 等待场景卸载完成
            while (!asyncOperation.isDone)
            {
                yield return null;
            }

            Debug.Log("局内持久化场景卸载完成");

            // 卸载完成后执行回调
            onComplete?.Invoke();
        }

        #endregion

        #region 场景转换函数 - 相关重载

        /// <summary>
        /// 场景转换函数
        /// </summary>
        /// <param name="type">想要切换的场景类型</param>
        public void Transition(SceneType type)
        {
            string sceneName = EnumUtils.GetName(type);
            Transition(sceneName);
        }

        /// <summary>
        /// 场景转换函数
        /// </summary>
        /// <param name="type">想要切换的场景类型</param>
        public void Transition(string to)
        {
            if (_coroutine != null)
            {
                Debug.LogWarning("当前有场景正在执行切换");
                return;
            }
            _coroutine = StartCoroutine(TransitionToScene(_currentScene, to));
        }

        #endregion


        IEnumerator TransitionToScene(string from, string to)
        {
            fadeCanvasGroup.blocksRaycasts = true;
            if (from != String.Empty)
            {
                yield return Fade(1);
            }

            // Debug.Log(from+","+to);
            if (from != string.Empty)
            {
                if (_sceneMaps.ContainsKey(from))
                {
                    _sceneMaps[from].SceneLeave(to);
                }
                // 卸载不需要的场景
                if (SceneManager.GetSceneByName(from).isLoaded)
                {
                    yield return SceneManager.UnloadSceneAsync(from);
                }
                else
                {
                    Debug.LogWarning($"Scene {from} is not loaded, cannot unload.");
                }
            }

            // load需要的场景
            yield return SceneManager.LoadSceneAsync(to, LoadSceneMode.Additive);

            yield return OnSceneLoadFinish(from, to);

            yield return Fade(0);

            OnFadeInFinish(to);

            fadeCanvasGroup.blocksRaycasts = false;
            _coroutine = null;

        }

        /// <summary>
        /// 卸载当前场景
        /// </summary>
        /// <returns></returns>
        public IEnumerator UnloadCurScene()
        {
            yield return Fade(1);

            if (_sceneMaps.ContainsKey(_currentScene))
            {
                _sceneMaps[_currentScene].SceneLeave(String.Empty);
            }

            // 卸载不需要的场景
            yield return SceneManager.UnloadSceneAsync(_currentScene);
            _currentScene = String.Empty;
        }

        /// <summary>
        /// 场景加载结束
        /// </summary>
        private IEnumerator OnSceneLoadFinish(string from, string to)
        {
            // 根据序号找到新加载的场景，并将其设置为激活场景
            Scene newScene = SceneManager.GetSceneAt(SceneManager.sceneCount - 1);
            SceneManager.SetActiveScene(newScene);

            _currentScene = to;

            if (_sceneMaps.ContainsKey(to))
            {
                //场景需要加载的东西 放这里
                yield return _sceneMaps[to].PrepareLoadSceneAssets();
            }

            if (_sceneMaps.ContainsKey(to))
            {
                _sceneMaps[to].SceneEnter(from, to);
            }
        }

        /// <summary>
        /// 渐入结束
        /// </summary>
        private void OnFadeInFinish(string to)
        {
            if (_sceneMaps.ContainsKey(to))
            {
                _sceneMaps[to].FadeInFinish();
            }

        }

        /// <summary>
        /// 场景切换渐变
        /// </summary>
        /// <param name="targetAlpha">变黑色为1，变透明为0</param>
        /// <returns></returns>
        public IEnumerator Fade(float targetAlpha)
        {
            float speed = Math.Abs(fadeCanvasGroup.alpha - targetAlpha) / fadeDuration;
            while (!Mathf.Approximately(fadeCanvasGroup.alpha, targetAlpha))
            {
                fadeCanvasGroup.alpha = Mathf.MoveTowards(
                    fadeCanvasGroup.alpha,
                    targetAlpha,
                    speed * Time.deltaTime
                );
                yield return null;
            }
        }

    }

}