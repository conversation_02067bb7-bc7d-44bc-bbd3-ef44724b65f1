
using System;
using System.Collections.Generic;
using System.Reflection;
using Commmon;
using Common;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using Object = UnityEngine.Object;
public class ExcelDataMgr : Singleton<ExcelDataMgr>
{
    private Dictionary<Type, Object> _tableCache = new();
    private Dictionary<Type, Func<string, string>> _tableTranslationCache = new();
    private Dictionary<string, Func<string, string>> _translationActionCache = new();
    private Dictionary<string, MethodInfo> _methodCache = new();

    /// <summary>
    /// 获取表格数据
    /// </summary>
    /// <typeparam name="T">表格类型</typeparam>
    /// <returns></returns>
    public T GetTable<T>() where T : Object
    {
        Type type = typeof(T);
        if (_tableCache.ContainsKey(type))
        {
            return _tableCache[type] as T;
        }
        string name = type.ToString();
        T data = ResourceMgr.Instance.LoadResource<T>($"datas/{name}");
        if (data == null)
        {
            Debug.LogError($"LoadTableFail :{name}");
            return null;
        }
        _tableCache.Add(type, data);
        return data;
    }

    #region  获取单条数据
    public DataType GetData<TableType, DataType>(int cfgId) where TableType : Object
    {
        return GetData<TableType, DataType>(new object[] { cfgId });
    }

    public DataType GetData<TableType, DataType>(string key) where TableType : Object
    {
        return GetData<TableType, DataType>(new object[] { key });
    }

    DataType GetData<TableType, DataType>(object[] objects) where TableType : Object
    {
        TableType tableData = GetTable<TableType>();
        Type tableType = tableData.GetType();
        Type dateType = typeof(DataType);
        MethodInfo method = ReflectionHelper.GetMethod(tableType, $"Get{dateType}");
        if (method == null)
        {
            Debug.LogError($"Get{tableType.Name}Data is null");
            return default;
        }
        var result = method.Invoke(tableData, objects);
        if (result is DataType dataType)
        {
            return dataType;
        }
        return default;
    }
    #endregion

    #region 获取DataList
    //TODO 这里有问题，性能待优化
    // public List<DataType> GetDataList<TableType, DataType>(int cfgId) where TableType : Object
    // {
    //     return GetDataList<TableType, DataType>(new object[] { cfgId });
    // }

    // public List<DataType> GetDataList<TableType, DataType>(string key) where TableType : Object
    // {
    //     return GetDataList<TableType, DataType>(new object[] { key });
    // }

    // public List<DataType> GetDataList<TableType, DataType>(object[] param) where TableType : Object
    // {
    //     TableType tableData = GetTable<TableType>();
    //     Type tableType = tableData.GetType();
    //     Type dateType = typeof(DataType);
    //     MethodInfo method;
    //     string methodKey = $"Get{dateType}List";
    //     if (_methodCache.ContainsKey(methodKey))
    //     {
    //         method = _methodCache[methodKey];
    //     }
    //     else
    //     {
    //         method = ReflectionHelper.GetMethod(tableType, methodKey);
    //         if (method == null)
    //         {
    //             Debug.LogError($"Get{tableType.Name}Data is null");
    //             return default;
    //         }
    //         _methodCache.Add(methodKey, method);
    //     }
    //     var result = method.Invoke(tableData, param);
    //     if (result is List<DataType> data)
    //     {
    //         return data;
    //     }
    //     return default;
    // }

    #endregion

    #region 单键表直接获取翻译数据
    public string GetTranslateData<TableType, DataType>(string fieldName, int cfgId) where TableType : Object
    {
        SetTranslationFunc<TableType, DataType>(fieldName);
        DataType data = GetData<TableType, DataType>(cfgId);
        //TODO 可缓存property做优化
        string str = ReflectionHelper.GetMemberValue<string>(data, fieldName);
        return str;
    }

    public string GetTranslateData<TableType, DataType>(string fieldName, string cfgKey) where TableType : Object
    {
        SetTranslationFunc<TableType, DataType>(fieldName);
        DataType data = GetData<TableType, DataType>(cfgKey);
        string str = ReflectionHelper.GetMemberValue<string>(data, fieldName);
        return str;
    }
    #endregion

    /// <summary>
    /// 指定翻译函数
    /// </summary>
    /// <typeparam name="TableType">表格名</typeparam>
    /// <typeparam name="DataType">表格数据类型</typeparam>
    /// <param name="fieldName">字段名</param>
    public void SetTranslationFunc<TableType, DataType>(string fieldName) where TableType : Object
    {
        Type tableType = typeof(TableType);
        TableType table = GetTable<TableType>();
        FieldInfo translateField = tableType.GetField("Translate", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (translateField != null)
        {
            Func<string, string> action = null;
            string actionKey = $"{typeof(DataType)}_{fieldName}Translate";
            MethodInfo method = ReflectionHelper.GetMethod(tableType, $"Get{actionKey}");
            if (method != null)
            {
                if (_translationActionCache.ContainsKey(actionKey))
                {
                    action = _translationActionCache[actionKey];
                }
                else
                {
                    action = GetTranslationFunc(table, method);
                    _translationActionCache[actionKey] = action;
                }
            }
            translateField.SetValue(table, action);
        }
    }

    Func<string, string> GetTranslationFunc(object scope, MethodInfo method)
    {
        return new Func<string, string>((key) =>
                   {
                       var result = method.Invoke(scope, new object[] { key });
                       if (result == null)
                       {
                           Debug.LogError($"Translation is null :{key}");
                           return key;
                       }

                       var translation = result; // 这里需要根据实际返回类型进行转换

                       switch (LanguageUtil.CurLanguage)
                       {
                           case eLanguage.Chinese:
                               string chinese = ReflectionHelper.GetMemberValue<string>(translation, "Chinese");
                               // Debug.Log($"_______chinese:{chinese}");
                               return chinese ?? key;
                           case eLanguage.English:
                               string english = ReflectionHelper.GetMemberValue<string>(translation, "English");
                               // Debug.Log($"_______english:{english}");
                               return english ?? key;
                           default:
                               return key;
                       }
                   });
    }



    public void Test()
    {
        Debug.Log($"Attribute:{GetTranslateData<Attributes, AttributeConfig>("name", "Attack")}");
    }

    #region 直接采用key键读取翻译表的形式,用于单独的翻译表

    /// <summary>
    /// 通过翻译表获取翻译数据
    /// </summary>
    /// <typeparam name="T">翻译table的类型</typeparam>
    /// <param name="key">翻译键值</param>
    /// <returns></returns>
    public string Translate<T>(string key) where T : Object
    {
        Type tableType = typeof(T);
        T tableData = GetTable<T>();
        MethodInfo method = ReflectionHelper.GetMethod(tableType, $"Get{tableType}Data");
        if (method == null)
        {
            Debug.LogError($"Get{tableType.Name}Data is null");
            return key;
        }
        var translation = method.Invoke(tableData, new object[] { key });
        if (translation == null)
        {
            Debug.LogError($"Translation is null :{key}");
            return key;
        }
        switch (LanguageUtil.CurLanguage)
        {
            case eLanguage.Chinese:
                string chinese = ReflectionHelper.GetMemberValue<string>(translation, "Chinese");
                // Debug.Log($"_______chinese:{chinese}");
                return chinese ?? key;
            case eLanguage.English:
                string english = ReflectionHelper.GetMemberValue<string>(translation, "English");
                // Debug.Log($"_______english:{english}");
                return english ?? key;
            default:
                Debug.LogError($"Translation is null :{key}");
                return key;
        }
    }

    #endregion

    public override void ClearSingleton()
    {
        _tableCache.Clear();
        _tableCache = null;

        _tableTranslationCache.Clear();
        _tableTranslationCache = null;

        _translationActionCache.Clear();
        _translationActionCache = null;

        _methodCache.Clear();
        _methodCache = null;
    }
}
