using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class BackGroundDragHandler : <PERSON>o<PERSON><PERSON><PERSON>our, IBeginDragHandler, IEndDragHandler, IDragHandler
{
    private CameraEdgeScroll _cameraEdgeScroll;
    // Start is called before the first frame update
    void Start()
    {
        if (Camera.main != null) _cameraEdgeScroll = Camera.main.gameObject.GetComponent<CameraEdgeScroll>();
    }

    // Update is called once per frame
    void Update()
    {
        
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        _cameraEdgeScroll.OnTouchDownEvent(eventData.position);
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        _cameraEdgeScroll.OnDragEndEvent(eventData.position);
    }

    public void OnDrag(PointerEventData eventData)
    {
        _cameraEdgeScroll.OnDragEvent(eventData.position);
    }
}
