# 按键配置系统使用说明

## 系统概述

这个按键配置系统允许玩家自定义游戏中的按键绑定，支持主要按键和次要按键的设置，并提供了完整的UI界面进行配置。

## 核心组件

### 1. InputActionType (枚举)
定义了所有可配置的游戏动作类型：
- `Escape` - 退出/取消
- `Confirm` - 确认
- `Menu` - 菜单
- `Inventory` - 背包
- `Skill1/2/3` - 技能
- `Pause` - 暂停
- `QuickSave/QuickLoad` - 快速保存/加载
- `Screenshot` - 截图
- `Debug` - 调试

### 2. InputActionData (类)
存储单个动作的按键绑定信息：
```csharp
public class InputActionData
{
    public InputActionType actionType;    // 动作类型
    public KeyCode primaryKey;           // 主要按键
    public KeyCode secondaryKey;         // 次要按键
    public string displayName;           // 显示名称
    public string description;           // 描述
    public bool canBeRebound;           // 是否可重新绑定
}
```

### 3. KeyBindingData (数据类)
全局数据类，负责持久化保存按键配置：
- 继承自 `DataBase` 和 `IGlobeData`
- 自动保存到磁盘
- 提供默认配置初始化

### 4. KeyBindingManager (管理器)
单例管理器，负责：
- 加载和保存按键配置
- 提供按键绑定的增删改查接口
- 检查按键冲突
- 触发按键绑定改变事件

### 5. InputManager (改进版)
增强的输入管理器，支持：
- 可配置按键检测
- 动作级别的锁定
- 新的事件系统（InputActionTriggered）
- 向后兼容原有的按键事件

## 使用方法

### 1. 基本设置

在场景中确保有以下组件：
```csharp
// 获取按键绑定管理器
var keyBindingManager = KeyBindingManager.Instance;

// 获取输入管理器
var inputManager = InputManager.Instance;
```

### 2. 监听输入事件

```csharp
// 监听动作触发事件（推荐）
EventDispatcher.GameEvent.Regist<InputActionType>(
    EventName.InputActionTriggered, 
    OnInputActionTriggered
);

// 监听按键绑定改变事件
EventDispatcher.GameEvent.Regist<InputActionType, KeyCode>(
    EventName.KeyBindingChanged, 
    OnKeyBindingChanged
);

private void OnInputActionTriggered(InputActionType actionType)
{
    switch (actionType)
    {
        case InputActionType.Escape:
            // 处理退出逻辑
            break;
        case InputActionType.Menu:
            // 处理菜单逻辑
            break;
        // ... 其他动作
    }
}
```

### 3. 检查动作状态

```csharp
// 检查动作是否被触发（按下）
bool isTriggered = InputManager.Instance.IsActionTriggered(InputActionType.Skill1);

// 检查动作是否正在被按住
bool isHeld = InputManager.Instance.IsActionHeld(InputActionType.Skill1);

// 获取动作的按键绑定信息
InputActionData binding = KeyBindingManager.Instance.GetKeyBinding(InputActionType.Skill1);
```

### 4. 修改按键绑定

```csharp
// 设置主要按键
bool success = KeyBindingManager.Instance.SetKeyBinding(
    InputActionType.Skill1, 
    KeyCode.Q, 
    true  // isPrimary
);

// 设置次要按键
KeyBindingManager.Instance.SetKeyBinding(
    InputActionType.Skill1, 
    KeyCode.Keypad1, 
    false  // isPrimary
);

// 清除次要按键
KeyBindingManager.Instance.ClearSecondaryKey(InputActionType.Skill1);

// 重置为默认配置
KeyBindingManager.Instance.ResetToDefault();
```

### 5. 锁定输入

```csharp
// 锁定所有输入
InputManager.Instance.IsLockAllKey(true);

// 锁定特定按键
InputManager.Instance.RegistLocker(KeyCode.Escape);
InputManager.Instance.UnRegistLocker(KeyCode.Escape);

// 锁定特定动作
InputManager.Instance.RegistActionLocker(InputActionType.Skill1);
InputManager.Instance.UnRegistActionLocker(InputActionType.Skill1);
```

## UI界面

### KeyBindingUI
主要的按键配置界面，提供：
- 显示所有可配置的动作
- 重新绑定按键功能
- 重置为默认配置
- 保存和取消操作

### KeyBindingItem
单个按键绑定项的UI组件，显示：
- 动作名称和描述
- 主要按键和次要按键
- 重新绑定和清除按钮

## 集成步骤

1. **添加组件到场景**：
   - 确保场景中有 `InputManager` 和 `KeyBindingManager`
   - 添加 `KeyBindingTest` 脚本进行测试

2. **创建UI界面**：
   - 创建按键配置UI预制体
   - 设置 `KeyBindingUI` 和 `KeyBindingItem` 组件

3. **注册事件监听**：
   - 在需要响应输入的脚本中注册事件监听
   - 实现对应的动作处理逻辑

4. **测试配置**：
   - 运行游戏，按Tab键打开配置界面
   - 测试按键重新绑定功能
   - 验证配置的持久化保存

## 注意事项

1. **按键冲突检测**：系统会自动检测按键冲突，不允许重复绑定
2. **数据持久化**：配置会自动保存到磁盘，下次启动时自动加载
3. **向后兼容**：保持了原有的按键事件系统，确保现有代码正常工作
4. **性能优化**：避免在Update中频繁调用按键检测，使用事件驱动模式

## 扩展功能

可以根据需要添加以下功能：
- 按键组合支持（Ctrl+S等）
- 手柄按键支持
- 按键序列支持
- 更复杂的冲突检测规则
- 配置文件导入导出
