using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

public class EventEntryUtil
{
    /// <summary>
    /// 给控件添加自定义事件监听
    /// </summary>
    /// <param name="uiControl">控件对象</param>
    /// <param name="type">事件类型</param>
    /// <param name="callBack">事件的响应函数</param>
    public static void AddCustomEventListener(UIBehaviour uiControl, EventTriggerType type, UnityAction<BaseEventData> callBack)
    {
        EventTrigger.Entry entry = GetCustomEventListenerEntry(uiControl, type);

        if (entry != null)
        {
            entry.callback.AddListener(callBack);
        }
    }

    /// <summary>
    /// 移除控件的自定义事件监听
    /// </summary>
    /// <param name="uiControl">控件对象</param>
    /// <param name="type">事件类型</param>
    /// <param name="callBack">事件的响应函数</param>
    public static void RemoveCustomEventListener(UIBehaviour uiControl, EventTriggerType type, UnityAction<BaseEventData> callBack)
    {

        EventTrigger trigger = uiControl.GetComponent<EventTrigger>();
        if (trigger == null)
        {
            Debug.LogError($"RemoveCustomEventListener Can't Find EventTrigger From {uiControl.name}");
            return;
        }

        EventTrigger.Entry entry = trigger.triggers.Find((entry) =>
         {
             return entry.eventID == type;
         });

        if (entry != null)
        {
            entry.callback.RemoveListener(callBack);
        }
        else
        {
            Debug.LogError($"RemoveCustomEventListener Can't Find EventTrigger.Entry From {uiControl.name}");
            return;
        }
    }
    /// <summary>
    /// 获取控件的自定义事件监听
    /// </summary>
    /// <param name="uiControl">控件对象</param>
    /// <param name="type">事件类型</param>
    public static EventTrigger.Entry GetCustomEventListenerEntry(UIBehaviour uiControl, EventTriggerType type)
    {


        EventTrigger trigger = uiControl.gameObject.GetComponent<EventTrigger>();
        if (trigger == null)
        {
            trigger = uiControl.gameObject.AddComponent<EventTrigger>();
        }

        EventTrigger.Entry entry = trigger.triggers.Find((entry) =>
         {
             return entry.eventID == type;
         });
        if (entry != null)
        {
            return entry;
        }
        else
        {
            entry = new EventTrigger.Entry();
            entry.eventID = type;
            trigger.triggers.Add(entry);
            return entry;
        }
    }


    /// <summary>
    /// 获取控件的自定义事件监听
    /// </summary>
    /// <param name="gameObject">控件对象</param>
    /// <param name="type">事件类型</param>
    public static EventTrigger.Entry GetCustomEventListenerEntry(GameObject gameObject, EventTriggerType type)
    {
        if (!CheckCanAddEventTrigger(gameObject))
        {
            return null;
        }

        EventTrigger trigger = gameObject.GetComponent<EventTrigger>();
        if (trigger == null)
        {
            trigger = gameObject.AddComponent<EventTrigger>();
        }

        EventTrigger.Entry entry = trigger.triggers.Find((entry) =>
         {
             return entry.eventID == type;
         });
        if (entry != null)
        {
            return entry;
        }
        else
        {
            entry = new EventTrigger.Entry();
            entry.eventID = type;
            trigger.triggers.Add(entry);
            return entry;
        }
    }

    /// <summary>
    /// 给控件添加自定义事件监听
    /// </summary>
    /// <param name="go">控件对象</param>
    /// <param name="type">事件类型</param>
    /// <param name="callBack">事件的响应函数</param>
    public static void AddCustomEventListener(GameObject go, EventTriggerType type, UnityAction<BaseEventData> callBack)
    {
        EventTrigger.Entry entry = GetCustomEventListenerEntry(go, type);

        if (entry != null)
        {
            entry.callback.AddListener(callBack);
        }
    }

    /// <summary>
    /// 移除控件的自定义事件监听
    /// </summary>
    /// <param name="go">控件对象</param>
    /// <param name="type">事件类型</param>
    /// <param name="callBack">事件的响应函数</param>
    public static void RemoveCustomEventListener(GameObject go, EventTriggerType type, UnityAction<BaseEventData> callBack)
    {

        EventTrigger trigger = go.GetComponent<EventTrigger>();
        if (trigger == null)
        {
            Debug.LogError($"RemoveCustomEventListener Can't Find EventTrigger From {go.name}");
            return;
        }

        EventTrigger.Entry entry = trigger.triggers.Find((entry) =>
         {
             return entry.eventID == type;
         });

        if (entry != null)
        {
            entry.callback.RemoveListener(callBack);
        }
        else
        {
            Debug.LogError($"RemoveCustomEventListener Can't Find EventTrigger.Entry From {go.name}");
            return;
        }
    }

    private static bool CheckCanAddEventTrigger(GameObject gameObject)
    {
        return true;
        if (gameObject.GetComponent<Collider>() == null || gameObject.GetComponent<Collider2D>() == null
        || gameObject.GetComponent<BoxCollider2D>() == null || gameObject.GetComponent<PolygonCollider2D>() == null)
        {
            Debug.LogError($"________Can't Add Event Trigger due to the lack of Collider Component.");
            return false;
        }
        return true;
    }
}