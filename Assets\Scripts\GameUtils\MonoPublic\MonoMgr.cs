using System.Collections;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using UnityEngine.Events;


public class MonoMgr : Singleton<MonoMgr>
{
    private MonoController _controller;

    protected override void OnInitialized()
    {
        //保证了monocontroller的唯一性
        GameObject obj = new GameObject("MonoController");
        GameObject.DontDestroyOnLoad(obj);
        _controller = obj.AddComponent<MonoController>();
    }

    public override void ClearSingleton()
    {
        GameObject.Destroy(_controller.gameObject);
        _controller = null;
    }

    public Coroutine StartCoroutine(IEnumerator routine)
    {
        return _controller.StartCoroutine(routine);
    }

    public void StopCoroutine(IEnumerator routine)
    {
        _controller.StopCoroutine(routine);
    }

    public void StopCoroutine(Coroutine coroutine)
    {
        _controller.StopCoroutine(coroutine);
    }

    public void AddUpdateListener(UnityAction action)
    {
        _controller.AddUpdateListener(action);
    }

    public void RemoveUpdateListener(UnityAction action)
    {
        _controller.RemoveUpdateListener(action);
    }

}
