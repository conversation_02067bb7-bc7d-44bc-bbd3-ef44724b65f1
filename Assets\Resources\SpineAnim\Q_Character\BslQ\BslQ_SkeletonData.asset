%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1b3b4b945939a54ea0b23d3396115fb, type: 3}
  m_Name: BslQ_SkeletonData
  m_EditorClassIdentifier: 
  atlasAssets:
  - {fileID: 11400000, guid: 6f433c2660f746148b6059e85089cbbd, type: 2}
  scale: 0.01
  skeletonJSON: {fileID: 4900000, guid: 1f8190b8f7a611e43ac74340fb948cea, type: 3}
  isUpgradingBlendModeMaterials: 0
  blendModeMaterials:
    requiresBlendModeMaterials: 1
    applyAdditiveMaterial: 1
    additiveMaterials: []
    multiplyMaterials:
    - pageName: BslQ.png
      material: {fileID: 2100000, guid: 1593a042ca6b3a64c84f8a8599fed6c9, type: 2}
    screenMaterials: []
  skeletonDataModifiers: []
  fromAnimation: []
  toAnimation: []
  duration: []
  defaultMix: 0.2
  controller: {fileID: 0}
