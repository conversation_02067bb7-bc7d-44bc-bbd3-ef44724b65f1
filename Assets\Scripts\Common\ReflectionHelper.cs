using System;
using System.Reflection;

public static class ReflectionHelper
{
    /// <summary>
    /// 判断 type 类型里是否有名为 methodName、签名匹配的公有实例方法
    /// </summary>
    public static MethodInfo GetMethod(
        Type type,
        string methodName,
        Type[] parameterTypes = null)
    {
        BindingFlags flags = BindingFlags.Instance | BindingFlags.Public;
        MethodInfo mi;
        if (parameterTypes == null)
        {
            // 只按名字查
            mi = type.GetMethod(methodName, flags);
        }
        else
        {
            // 按名字+参数类型查
            mi = type.GetMethod(methodName, flags, null, parameterTypes, null);
        }
        return mi;
    }

    /// <summary>
    /// 通用的获取对象属性或字段值的方法
    /// </summary>
    /// <param name="obj">目标对象</param>
    /// <param name="memberName">属性或字段名称</param>
    /// <param name="bindingFlags">绑定标志，默认为公共实例成员</param>
    /// <returns>属性或字段的值</returns>
    public static object GetMemberValue(object obj, string memberName, BindingFlags bindingFlags = BindingFlags.Public | BindingFlags.Instance)
    {
        if (obj == null) return null;
        if (string.IsNullOrEmpty(memberName)) return null;

        try
        {
            Type objType = obj.GetType();

            // 优先尝试获取属性
            PropertyInfo property = objType.GetProperty(memberName, bindingFlags);
            if (property != null && property.CanRead)
            {
                return property.GetValue(obj);
            }

            // 如果没有属性，尝试字段
            FieldInfo field = objType.GetField(memberName, bindingFlags);
            if (field != null)
            {
                return field.GetValue(obj);
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"获取成员值失败: {memberName}, 错误: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 泛型版本，直接返回指定类型
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="obj">目标对象</param>
    /// <param name="memberName">属性或字段名称</param>
    /// <param name="bindingFlags">绑定标志</param>
    /// <returns>指定类型的值</returns>
    public static T GetMemberValue<T>(object obj, string memberName, BindingFlags bindingFlags = BindingFlags.Public | BindingFlags.Instance)
    {
        var value = GetMemberValue(obj, memberName, bindingFlags);

        if (value == null) return default(T);

        try
        {
            // 尝试直接转换
            if (value is T directValue)
                return directValue;

            // 尝试类型转换
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default(T);
        }
    }
}