
namespace AttributeSystem
{
    /// <summary>
    /// 操作类型枚举
    /// </summary>
    public enum OperationType
    {
        Add,
        Multiply,
        Override,
        Max
    }

    /// <summary>
    /// 角色属性数据类型
    /// </summary>
    public enum AttrDataType
    {
        Vector,
        Number
    }


    /// <summary>
    /// 修改属性原因[用于需要手动移除的情况]
    /// </summary>
    public enum ModAttrReason
    {
        // 可以在这里添加具体的原因枚举值
    }
}
