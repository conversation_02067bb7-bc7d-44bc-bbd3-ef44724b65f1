using Common;
using Cysharp.Threading.Tasks;
using UnityCommunity.UnitySingleton;
using UnityEngine;

namespace CharacterSystem
{

    /// <summary>
    /// 角色管理器
    /// </summary>
    public class CharacterManager : Singleton<CharacterManager>
    {
        public async UniTask<GameObject> GetQNpc(int characterId)
        {
            GameObject qNpc = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.Q_Player);
            CharacterBase characterBase = qNpc.GetComponent<CharacterBase>();
            characterBase.Initialize(characterId);
            return qNpc;
        }

        public void ReturnQNpc(GameObject qNpc)
        {
            ResourceLoader.Instance.ReleaseInstance(qNpc);
        }

    }
}