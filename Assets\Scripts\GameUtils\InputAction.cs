using System;
using UnityEngine;

namespace GameUtils
{
    /// <summary>
    /// 输入动作类型枚举
    /// </summary>
    public enum InputActionType
    {
        None = 0,
        Escape = 1,        // 退出/取消
        Confirm = 2,       // 确认
        Menu = 3,          // 菜单
        Inventory = 4,     // 背包
        Skill1 = 5,        // 技能1
        Skill2 = 6,        // 技能2
        Skill3 = 7,        // 技能3
        Pause = 8,         // 暂停
        QuickSave = 9,     // 快速保存
        QuickLoad = 10,    // 快速加载
        Screenshot = 11,   // 截图
        Debug = 12,        // 调试
    }

    /// <summary>
    /// 输入动作数据
    /// </summary>
    [Serializable]
    public class InputActionData
    {
        [SerializeField] public InputActionType actionType;
        [SerializeField] public KeyCode primaryKey;
        [SerializeField] public KeyCode secondaryKey;
        [SerializeField] public string displayName;
        [SerializeField] public string description;
        [SerializeField] public bool canBeRebound;

        public InputActionData()
        {
            actionType = InputActionType.None;
            primaryKey = KeyCode.None;
            secondaryKey = KeyCode.None;
            displayName = "";
            description = "";
            canBeRebound = true;
        }

        public InputActionData(InputActionType type, KeyCode primary, KeyCode secondary = KeyCode.None, 
                              string name = "", string desc = "", bool rebindable = true)
        {
            actionType = type;
            primaryKey = primary;
            secondaryKey = secondary;
            displayName = name;
            description = desc;
            canBeRebound = rebindable;
        }

        /// <summary>
        /// 检查指定按键是否匹配此动作
        /// </summary>
        public bool IsKeyMatch(KeyCode keyCode)
        {
            return keyCode == primaryKey || keyCode == secondaryKey;
        }

        /// <summary>
        /// 获取主要按键的显示名称
        /// </summary>
        public string GetPrimaryKeyDisplayName()
        {
            return GetKeyDisplayName(primaryKey);
        }

        /// <summary>
        /// 获取次要按键的显示名称
        /// </summary>
        public string GetSecondaryKeyDisplayName()
        {
            return secondaryKey == KeyCode.None ? "" : GetKeyDisplayName(secondaryKey);
        }

        /// <summary>
        /// 获取按键的显示名称（中文）
        /// </summary>
        private string GetKeyDisplayName(KeyCode keyCode)
        {
            return keyCode switch
            {
                KeyCode.Escape => "ESC",
                KeyCode.Alpha1 => "1",
                KeyCode.Alpha2 => "2",
                KeyCode.Alpha3 => "3",
                KeyCode.Alpha4 => "4",
                KeyCode.Alpha5 => "5",
                KeyCode.Alpha6 => "6",
                KeyCode.Alpha7 => "7",
                KeyCode.Alpha8 => "8",
                KeyCode.Alpha9 => "9",
                KeyCode.Alpha0 => "0",
                KeyCode.Space => "空格",
                KeyCode.Return => "回车",
                KeyCode.Tab => "Tab",
                KeyCode.LeftShift => "左Shift",
                KeyCode.RightShift => "右Shift",
                KeyCode.LeftControl => "左Ctrl",
                KeyCode.RightControl => "右Ctrl",
                KeyCode.LeftAlt => "左Alt",
                KeyCode.RightAlt => "右Alt",
                KeyCode.F1 => "F1",
                KeyCode.F2 => "F2",
                KeyCode.F3 => "F3",
                KeyCode.F4 => "F4",
                KeyCode.F5 => "F5",
                KeyCode.F6 => "F6",
                KeyCode.F7 => "F7",
                KeyCode.F8 => "F8",
                KeyCode.F9 => "F9",
                KeyCode.F10 => "F10",
                KeyCode.F11 => "F11",
                KeyCode.F12 => "F12",
                KeyCode.None => "无",
                _ => keyCode.ToString()
            };
        }
    }
}
