using System.Collections;
using UnityEngine;
using DG.Tweening;
using Common;
using FeatureService;

namespace EventObjSystem
{
    public class FlipCard : MonoBehaviour
    {
        [Tooltip("翻面时长")]
        public float duration = 0.8f;
        [Tooltip("在远方时的透明度")]
        public float ColorAlpha = 0.5f;
        [Toolt<PERSON>("在近距离时的缩放大小")]
        [SerializeField] private float largerScale = 1.05f;

        [Header("SpriteRenderer组件")]
        public SpriteRenderer frontSpriteRenderer;
        public SpriteRenderer behindSpriteRenderer;

        private Sprite _noShadowCard;
        private Sprite _shadowCard;

        private bool _isBehind = true;
        private Coroutine _flipRoutine;
        private EventCard mEventCard;

        [Header("浮动效果设置")]
        [Tooltip("浮动高度")]
        [SerializeField] private float floatHeight = 0.3f;      // 浮动高度
        [Tooltip("浮动周期时间")]
        [SerializeField] private float floatDuration = 2f;      // 浮动周期时间
        [Tooltip("浮动缓动类型")]
        [SerializeField] private Ease floatEase = Ease.InOutSine; // 缓动类型

        private Vector3 originalPosition;
        private Tween floatTween;
        public Canvas TipCanvas;
        EventButton _cardButton;


        #region 生命周期

        public async void Init(EventCard parent)
        {
            mEventCard = parent;
            string prefix = ResourcePathConst.Prefix.EventCardSprite + mEventCard.CardStyleInfo.cardPath;
            string behindPath = prefix + 1;
            string noShadowPath = prefix + 2;
            string shadowPath = prefix + 3;
            Debug.Log($"behindPath:{behindPath},noShadowPath:{noShadowPath},shadowPath:{shadowPath}");

            _noShadowCard = await ResourceLoader.Instance.LoadAsync<Sprite>(noShadowPath);
            _shadowCard = await ResourceLoader.Instance.LoadAsync<Sprite>(shadowPath);
            frontSpriteRenderer.sprite = _noShadowCard;
            behindSpriteRenderer.sprite = await ResourceLoader.Instance.LoadAsync<Sprite>(behindPath);

            _cardButton = new EventButton(frontSpriteRenderer.gameObject);
            _cardButton.OnPointerClick.AddListener((data) =>
            {
                TriggerCardEffect();
            });
        }

        private void Start()
        {
            originalPosition = transform.localPosition;
            ResetFlip();
        }

        private void OnDestroy()
        {
            StopFloating();
        }

        #endregion
        public void OnCardStateChange(E_CardState e_CardState)
        {
            switch (e_CardState)
            {
                case E_CardState.Faraway:
                    ResetFlip();
                    break;
                case E_CardState.Closer:
                    StartFloating();
                    SetCardView(1, largerScale, _noShadowCard);
                    FlipTo(true);
                    IsShowTip(false);
                    break;
                case E_CardState.Front:
                    StartFloating();
                    SetCardView(1, 1, _shadowCard);
                    FlipTo(false);
                    IsShowTip(true);
                    break;
            }
        }

        #region  翻页效果相关
        /// <summary>
        /// 调用这个方法开始翻页
        /// </summary>
        public void Flip()
        {
            if (_flipRoutine != null) StopCoroutine(_flipRoutine);
            _flipRoutine = StartCoroutine(FlipCoroutine());
        }

        private IEnumerator FlipCoroutine()
        {
            float elapsed = 0f;
            float startAngle = _isBehind ? 180f : 0f;
            float endAngle = _isBehind ? 0f : 180f;
            bool hasFlipped = false;  // 添加标志，防止重复切换

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = Mathf.SmoothStep(0f, 1f, elapsed / duration);
                float y = Mathf.Lerp(startAngle, endAngle, t);
                transform.localRotation = Quaternion.Euler(0f, y, 0f);

                // 在90度时切换显示的GameObject，只切换一次
                if (!hasFlipped && Mathf.Abs(y - 90f) < 5f)  // 在90度附近切换
                {
                    hasFlipped = true;
                    _isBehind = !_isBehind;  // 切换状态
                    FlipGo(_isBehind);      // 切换显示的GameObject
                }
                yield return null;
            }
            // 确保最终状态正确
            transform.localRotation = Quaternion.Euler(0f, endAngle, 0f);
            _flipRoutine = null;
        }

        public void FlipTo(bool isBehind)
        {
            if (_isBehind == isBehind)
            {
                return;
            }
            Flip();
        }

        public void ResetFlip()
        {
            _isBehind = true;
            FlipGo(_isBehind);
            transform.localRotation = Quaternion.Euler(0f, 180f, 0f);
            StopFloating();
            SetCardView(0.6f, 1, _noShadowCard);
            IsShowTip(false);
        }


        void FlipGo(bool isBehind)
        {
            frontSpriteRenderer.gameObject.SetActive(!isBehind);
            behindSpriteRenderer.gameObject.SetActive(isBehind);
        }

        #endregion

        #region 上下浮动效果

        /// <summary>
        /// 开始浮动效果
        /// </summary>
        public void StartFloating()
        {
            StopFloating(); // 先停止之前的动画

            // 先重置到原始位置
            transform.localPosition = originalPosition;

            // 使用本地坐标创建上下浮动的循环动画，更稳定
            floatTween = transform.DOLocalMove(originalPosition + Vector3.up * floatHeight, floatDuration)
                .SetEase(floatEase)
                .SetLoops(-1, LoopType.Yoyo); // 无限循环，来回摆动
        }

        /// <summary>
        /// 停止浮动效果
        /// </summary>
        public void StopFloating()
        {
            if (floatTween != null)
            {
                floatTween.Kill();
                floatTween = null;
            }
        }

        /// <summary>
        /// 重置到原始位置
        /// </summary>
        public void ResetPosition()
        {
            StopFloating();
            transform.position = originalPosition;
        }

        #endregion

        #region 提示与点击效果相关
        public void IsShowTip(bool isShow)
        {
            TipCanvas.gameObject.SetActive(isShow);
        }

        public void TriggerCardEffect()
        {
            FeatureManager.Instance.TriggerGameFeature(mEventCard.featureType, mEventCard);
        }

        #endregion
        void SetCardView(float alpha, float scale, Sprite frontSprite)
        {
            Color newColor = Color.white;
            newColor.a = alpha;

            frontSpriteRenderer.sprite = frontSprite;

            // 直接设置颜色，无需判空
            frontSpriteRenderer.color = newColor;
            behindSpriteRenderer.color = newColor;

            transform.localScale = Vector3.one * scale;
        }

    }
}