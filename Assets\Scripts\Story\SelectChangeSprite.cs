using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class HighlightChangeSprite : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{   
    [SerializeField]
    private Sprite _normalSprite;
    [SerializeField]
    private Sprite _highlightSprite;
    [SerializeField]
    private Image _image;

    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        _image.sprite = _highlightSprite;
        _image.SetNativeSize();
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        _image.sprite = _normalSprite;
        _image.SetNativeSize();
    }
}
