using System.Collections;
using UnityEngine;


namespace GameSceneService

{
    public abstract class SceneBase
    {
        private SceneType _sceneType;
        public SceneType SceneType
        {
            get
            {
                return _sceneType;
            }
        }

        private SceneStat _sceneStat;
        public SceneStat SceneStat
        {
            get
            {
                return _sceneStat;
            }
        }


        private bool _isFirstIn = true;

        private float _prepareLoadTime = 0;
        public float PrepareLoadTime
        {
            get
            {
                return _prepareLoadTime;
            }
        }

        public SceneBase(SceneType type, SceneStat sceneStat = SceneStat.InGameSession)
        {
            _sceneType = type;
            _sceneStat = sceneStat;
        }


        public IEnumerator PrepareLoadSceneAssets()
        {
            float startTime = Time.time;
            _prepareLoadTime = 0;
            yield return OnPrepareLoadSceneAssets();
            _prepareLoadTime = Time.time - startTime;
        }

        public void SceneEnter(string fromScene, string toScene)
        {
            if (_isFirstIn)
            {
                OnSceneInit();
                _isFirstIn = false;
            }
            OnSceneEnter(fromScene, toScene);
        }

        /// <summary>
        /// 在即将离开场景时调用
        /// </summary>
        /// <param name="toScene"></param>
        public void SceneLeave(string toScene)
        {
            OnSceneLeave(toScene);
        }

        /// <summary>
        /// 资源加载，一些需要异步加载的东西写这里
        /// </summary>
        /// <returns></returns>
        protected virtual IEnumerator OnPrepareLoadSceneAssets()
        {
            yield return null;
        }


        /// <summary>
        /// 第一次进入场景时的初始化操作
        /// </summary>
        protected abstract void OnSceneInit();

        /// <summary>
        /// 在进入场景后调用
        /// </summary>
        /// <param name="fromScene"></param>
        /// <param name="toScene"></param>
        protected abstract void OnSceneEnter(string fromScene, string toScene);

        /// <summary>
        /// 在即将离开场景时调用
        /// </summary>
        /// <param name="toScene"></param>
        protected abstract void OnSceneLeave(string toScene);

        /// <summary>
        /// 在进入场景后的渐入效果结束后调用(若该场景无渐变效果,则会在SceneEnter后调用)
        /// </summary>
        public virtual void FadeInFinish()
        {

        }

    }
}