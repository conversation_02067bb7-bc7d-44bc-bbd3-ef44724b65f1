using UnityEngine;
using static EventsTable;

namespace EventObjSystem
{

    /// <summary>
    /// 事件对象的基类
    /// </summary>
    public abstract class IEventObj : MonoBehaviour
    {
        [Header("触发的游戏功能")]
        public FeatureType featureType = FeatureType.Default;

        /// <summary>
        /// 事件id
        /// </summary>
        public int EventId = -1;

        [Header("是否是自动触发")]
        public bool IsAutoTrigger = false;

        protected EventEntry eventData;
        public EventEntry EventData => eventData;
        protected string styleKey;
        public string StyleKey => styleKey;

        public virtual void Init(int eventId)
        {
            EventId = eventId;
            EventEntry data = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(eventId);
            if (data == null)
            {
                Debug.LogError($"eventId:{eventId} eventData is null");
                return;
            }
            eventData = data;
            IsAutoTrigger = data.IsAutoTrigger;
            featureType = data.featureType;
            styleKey = EnumUtils.GetName(data.featureType);
            Debug.Log($"evenId:{EventId} IsAutoTrigger:{IsAutoTrigger} featureType {styleKey} EventObjType {data.eventObjType}");
        }
    }
}