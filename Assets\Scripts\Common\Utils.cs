﻿using UnityEngine;
using UnityEngine.EventSystems;

public class MouseHoverHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    public delegate void MouseEvent(PointerEventData eventData);
    public event MouseEvent OnMouseEnterEvent;
    public event MouseEvent OnMouseExitEvent;

    public void OnPointerEnter(PointerEventData eventData)
    {
        OnMouseEnterEvent?.Invoke(eventData); 
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        OnMouseExitEvent?.Invoke(eventData); // 调用事件
    }
}
public static class Utils
{
    public static Vector2 GetMouseWorldPosition()
    {
        Vector3 mousePos = Input.mousePosition;
        mousePos.z = -Camera.main.transform.position.z;

        Vector3 wPos = Camera.main.ScreenToWorldPoint(mousePos);
        return wPos;
    }

    public static Vector2 GetMouseScreenPos()
    {
        return Input.mousePosition;
    }

    public static Rect GetWorldRect(RectTransform rect)
    {
        Vector3[] worldCorners = new Vector3[4];
        rect.GetWorldCorners(worldCorners);
        var pos = worldCorners[0];
        var size = worldCorners[2] - worldCorners[0];
        return new Rect(pos, size);
    }
    
    public static Rect GetScreenRectOfScreenUI(RectTransform rect)
    {
        Vector3[] worldCorners = new Vector3[4];
        rect.GetWorldCorners(worldCorners);
        var pos = worldCorners[0];
        var size = worldCorners[2] - worldCorners[0];
        return new Rect(pos, size);
    }

    public static Rect GetScreenRectOfWorldUI(RectTransform rectTransform, Camera camera)
    {
        Vector3[] worldCorners = new Vector3[4];
        Vector3[] screenCorners = new Vector3[4];
        rectTransform.GetWorldCorners(worldCorners);
        for (var index = 0; index < worldCorners.Length; index++)
        {
            screenCorners[index] = camera.WorldToScreenPoint(worldCorners[index]);
        }
        var pos = screenCorners[0];
        var size = screenCorners[2] - screenCorners[0];
        return new Rect(pos, size);
    }
}