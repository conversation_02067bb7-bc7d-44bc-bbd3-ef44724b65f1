namespace AttributeSystem
{
    public interface IAttributeHolder
    {
        /// <summary>
        /// 属性唯一标识 - 默认为characterID,镖局或其他需要属性的则以1001开头
        /// </summary>
        public int GetAttributeUniqueID();
        public AttributeComp GetAttributeComp();
    }

    public enum AttrType
    {
        /// <summary>经验值</summary>
        Experience,

        // 战斗类
        /// <summary>攻击力</summary>
        Attack,
        /// <summary>防御力</summary>
        Defence,
        /// <summary>生命值</summary>
        Health,

        //养成类
        /// <summary>行会口令</summary>
        Jargon,
        /// <summary>策略</summary>
        Strategy,
        /// <summary>气运 - 用于角色</summary>
        Luck,
        /// <summary>黑暗</summary>
        Dark,

        //经营类
        /// <summary>声望</summary>
        Reputation,
        /// <summary>好感度</summary>
        Affinity,
        //镖局属性
        /// <summary>战力</summary>
        CombatPower,
        /// <summary>谈判</summary>
        Negotiation,
        /// <summary>团结</summary>
        Unity,
        /// <summary>气运 - 用于镖局</summary>
        Fortune,

    }
}