using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class GameObjectEditor : MonoBehaviour
{
    [MenuItem("GameObject/GetRout",priority =0)]
    static void GetRout()
    {
        var transform = Selection.activeTransform;
        var route =GetRoute(transform);
        Debug.Log(route);
    }
    
    public static string GetRoute(Transform transform, string splitter = "/")
    {
        var result = transform.name;
        var parent = transform.parent;
        while (parent != null)
        {
            result = $"{parent.name}{splitter}{result}";
            parent = parent.parent;
        }
        return result;
    }
}
