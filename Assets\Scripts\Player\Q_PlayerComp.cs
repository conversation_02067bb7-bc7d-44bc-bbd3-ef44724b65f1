using CharacterSystem;
using Common;
using UnityEngine;
using UnityEngine.Events;

public class Q_PlayerComp : CharacterBase
{

    private Q_PlayerMoveComp _moveComp;

    public bool IsMoving
    {
        get
        {
            return _moveComp.IsMoving;
        }
    }

    private bool _moveable;
    public bool Moveable
    {
        get
        {
            return _moveable;
        }
        set
        {
            _moveable = value;
            _moveComp.enabled = value;
        }
    }

    public override void Initialize(int characterId)
    {
        base.Initialize(characterId);
        _moveComp = gameObject.GetComponent<Q_PlayerMoveComp>();
    }

    public void SetQPlayerPosition(Vector3 pos)
    {
        gameObject.transform.position = pos;
    }

    public void ActivategameObject()
    {
        gameObject.SetActive(true);
    }

    public void DeactivegameObject()
    {
        gameObject.SetActive(false);
    }

    public void SetPlayerMoveLimit(Vector2 pos)
    {
        _moveComp.SetPlayerMoveLimit(pos);
    }

    public void Destroy()
    {
        _moveComp = null;
    }

}