using EventObjSystem;
using FeatureService;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace CharacterSystem
{
    /// <summary>
    /// EventNpc
    /// </summary>
    public class EventNpc : IEventObj
    {
        Button Btn;
        TMP_Text IntroText;
        Canvas Canvas;

        [Header("是否是常驻场景npc")]
        public bool IsStatic;

        void Awake()
        {

        }

        void OnEnable()
        {
            InitUI();
        }


        public override void Init(int eventId)
        {
            base.Init(eventId);
            InitUI();
        }
        void InitUI()
        {
            Canvas = transform.Find("Canvas").GetComponent<Canvas>();
            Btn = Canvas.transform.Find("Button").GetComponent<Button>();
            IntroText = Btn.transform.Find("IntroText").GetComponent<TMP_Text>();
            Canvas.worldCamera = Camera.main;
            Btn.onClick.RemoveAllListeners();
            Btn.onClick.AddListener(TriggerGameFunction);
            SetUIActive(false);
            UpdateUIView();
        }

        public void OnTriggerEnter2D(Collider2D collision)
        {
            EventEntry eventData = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(EventId);
            if (eventData == null)
            {
                return;
            }
            IsAutoTrigger = eventData.IsAutoTrigger;
            featureType = eventData.featureType;
            Debug.Log($"OnTriggerEnter2D IsAutoTrigger:{IsAutoTrigger} featureType {EnumUtils.GetName(featureType)}");
            UpdateUIView();
            if (IsAutoTrigger)
            {
                TriggerGameFunction();
            }
            else
            {
                SetUIActive(true);
            }
        }

        public void OnTriggerExit2D(Collider2D collision)
        {
            if (IsAutoTrigger)
            {

            }
            else
            {
                SetUIActive(false);
            }
        }

        void TriggerGameFunction()
        {
            FeatureManager.Instance.TriggerGameFeature(featureType, this);
        }

        void UpdateUIView()
        {
            //TODO :应该是根据表格更新内容
            if (IntroText)
            {
                IntroText.text = EnumUtils.GetName(featureType);
            }
        }

        void SetUIActive(bool isActive)
        {
            Canvas.enabled = isActive;
        }

    }

}