﻿using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Escort
{
    public class EscortRoadItem : MonoBehaviour
    {
        public int RoadPlaceOneID = 0;
        public int RoadPlaceTwoID = 0;
        public bool IsWeak = false;

        private Dictionary<Tuple<int, int>, List<Transform>> _cityPathPointsDict =
            new Dictionary<Tuple<int, int>, List<Transform>>();

        private List<Transform> _roadWaypointsList = new List<Transform>();
        private EscortMapView _mapViewContext;

        public void SetMapViewContext(EscortMapView mapView)
        {
            _mapViewContext = mapView;
            _roadWaypointsList = transform.GetComponentsInChildren<Transform>().Where(t=>t!=transform).ToList();
            RefreshRoadInfo();
        }

        private void RefreshRoadInfo()
        {
            var placeOneTransform = _mapViewContext.GetCityItem(RoadPlaceOneID);
            var placeTwoTransform = _mapViewContext.GetCityItem(RoadPlaceTwoID);

            var firstWayPoint = _roadWaypointsList[0];
            if (Vector3.Distance(firstWayPoint.position, placeOneTransform.transform.position) <
                Vector3.Distance(firstWayPoint.position, placeTwoTransform.transform.position))
            {
                _cityPathPointsDict.Add(Tuple.Create(RoadPlaceOneID, RoadPlaceTwoID), _roadWaypointsList);
                var reverseList = _roadWaypointsList.ToList();
                reverseList.Reverse();
                _cityPathPointsDict.Add(Tuple.Create(RoadPlaceTwoID, RoadPlaceOneID), reverseList);
            }
            else
            {
                var reverseList = _roadWaypointsList.ToList();
                reverseList.Reverse();
                _cityPathPointsDict.Add(Tuple.Create(RoadPlaceOneID, RoadPlaceTwoID), reverseList);
                _cityPathPointsDict.Add(Tuple.Create(RoadPlaceTwoID, RoadPlaceOneID), _roadWaypointsList);
            }
        }

        public Dictionary<Tuple<int, int>, List<Transform>> GetAllCityRoadDict()
        {
            return _cityPathPointsDict;
        }
    }
}