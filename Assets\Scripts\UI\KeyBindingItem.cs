using UnityEngine;
using UnityEngine.UI;
using TMPro;
using GameUtils;

namespace UI
{
    /// <summary>
    /// 按键绑定项UI组件
    /// </summary>
    public class KeyBindingItem : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI actionNameText;
        [SerializeField] private TextMeshProUGUI actionDescriptionText;
        [SerializeField] private Button primaryKeyButton;
        [SerializeField] private Button secondaryKeyButton;
        [SerializeField] private Button clearSecondaryButton;
        [SerializeField] private TextMeshProUGUI primaryKeyText;
        [SerializeField] private TextMeshProUGUI secondaryKeyText;

        private InputActionData _actionData;
        private KeyBindingUI _parentUI;

        /// <summary>
        /// 初始化按键绑定项
        /// </summary>
        public void Initialize(InputActionData actionData, KeyBindingUI parentUI)
        {
            _actionData = actionData;
            _parentUI = parentUI;

            // 绑定按钮事件
            primaryKeyButton?.onClick.AddListener(() => StartRebindPrimary());
            secondaryKeyButton?.onClick.AddListener(() => StartRebindSecondary());
            clearSecondaryButton?.onClick.AddListener(() => ClearSecondary());

            // 刷新显示
            RefreshDisplay();
        }

        /// <summary>
        /// 刷新显示内容
        /// </summary>
        public void RefreshDisplay()
        {
            if (_actionData == null) return;

            // 更新动作名称和描述
            if (actionNameText != null)
                actionNameText.text = _actionData.displayName;

            if (actionDescriptionText != null)
                actionDescriptionText.text = _actionData.description;

            // 更新按键显示
            if (primaryKeyText != null)
                primaryKeyText.text = _actionData.GetPrimaryKeyDisplayName();

            if (secondaryKeyText != null)
            {
                string secondaryKeyName = _actionData.GetSecondaryKeyDisplayName();
                secondaryKeyText.text = string.IsNullOrEmpty(secondaryKeyName) ? "无" : secondaryKeyName;
            }

            // 更新按钮可交互状态
            bool canRebind = _actionData.canBeRebound;
            
            if (primaryKeyButton != null)
                primaryKeyButton.interactable = canRebind;

            if (secondaryKeyButton != null)
                secondaryKeyButton.interactable = canRebind;

            if (clearSecondaryButton != null)
            {
                clearSecondaryButton.interactable = canRebind && _actionData.secondaryKey != KeyCode.None;
            }
        }

        /// <summary>
        /// 开始重新绑定主要按键
        /// </summary>
        private void StartRebindPrimary()
        {
            if (_actionData?.canBeRebound == true)
            {
                _parentUI?.StartRebinding(_actionData.actionType, true);
            }
        }

        /// <summary>
        /// 开始重新绑定次要按键
        /// </summary>
        private void StartRebindSecondary()
        {
            if (_actionData?.canBeRebound == true)
            {
                _parentUI?.StartRebinding(_actionData.actionType, false);
            }
        }

        /// <summary>
        /// 清除次要按键
        /// </summary>
        private void ClearSecondary()
        {
            if (_actionData?.canBeRebound == true)
            {
                _parentUI?.ClearSecondaryKey(_actionData.actionType);
            }
        }

        /// <summary>
        /// 设置高亮状态（用于冲突提示等）
        /// </summary>
        public void SetHighlight(bool highlight)
        {
            // 这里可以实现高亮效果，比如改变背景色
            // 可以根据需要添加高亮视觉效果
        }

        /// <summary>
        /// 检查是否使用了指定按键
        /// </summary>
        public bool UsesKey(KeyCode keyCode)
        {
            return _actionData?.IsKeyMatch(keyCode) ?? false;
        }

        /// <summary>
        /// 获取动作类型
        /// </summary>
        public InputActionType GetActionType()
        {
            return _actionData?.actionType ?? InputActionType.None;
        }
    }
}
