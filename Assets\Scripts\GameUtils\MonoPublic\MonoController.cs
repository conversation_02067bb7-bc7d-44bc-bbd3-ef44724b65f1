using UnityEngine;
using UnityEngine.Events;


public class MonoController : Mono<PERSON><PERSON><PERSON><PERSON>
{
    private event UnityAction _updateEvent;

    public void AddUpdateListener(UnityAction action)
    {
        if (_updateEvent == null)
        {
            _updateEvent = new UnityAction(action);
        }
        else
        {
            _updateEvent += action;
        }
    }

    public void RemoveUpdateListener(UnityAction action)
    {
        if (_updateEvent == null)
        {
            Debug.LogError($"________RemoveUpdateListenerError there is no UpdateAction");
            return;
        }

        _updateEvent -= action;
    }

    void Update()
    {
        if (_updateEvent != null)
        {
            _updateEvent();
        }
    }

    void OnDestroy()
    {
        _updateEvent = null;
    }
}
