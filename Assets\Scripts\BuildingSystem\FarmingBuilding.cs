﻿using System;
using Common;
using UI;
using UnityEngine;

public class FarmingBuilding : MonoBehaviour
{
    public GameObject FarmingOperatePrefab;
    private int _currentCropType = -1;
    private int _growthValue = 0;
    private readonly string CropSpritePathFromat = "crops/{0}/{1}";
    private GrowStateEnum _cropGrowState = GrowStateEnum.Seed;

    public GameObject WateringGo;
    public GameObject FertilizationGo;
    public GameObject CropItemGo;
    public enum GrowStateEnum
    {
        Seed=1,
        Seedling=2,
        Ripe=3
    }

    public GrowStateEnum cropGrowState => _cropGrowState;
    public int growthValue
    {
        get => _growthValue;
        set
        {
            _growthValue = value;
            OnGrowthValueChanged();
        }
        
    }
    private bool _hasCrop = false;
    private FarmingOperateItem _farmingOperateItem;

    private GameObject namebarFollowGo;

    public void HideOperateItem()
    {
        _farmingOperateItem.gameObject.SetActive(false);
    }

    private void Awake()
    {
                
        namebarFollowGo = new GameObject("farmingNameBar")
        {
            transform =
            {
                parent = this.transform,
                localPosition = new Vector3(0.2f, 1.8f, 0)
            }
        };
    }

    private void Start()
    {
        var uiRoot = UIManager.Instance.GetUIRoot();
        var buildingGo = Instantiate<GameObject>(FarmingOperatePrefab, uiRoot.transform);
        var buildingOperate = buildingGo.GetComponent<FarmingOperateItem>();
        _farmingOperateItem = buildingOperate;
        buildingOperate.AddFollowGo(namebarFollowGo);
        buildingOperate.SetCamera(Camera.main);
        buildingOperate.gameObject.SetActive(false);
            
        WateringGo.SetActive(false);
        FertilizationGo.SetActive(false);
        CropItemGo.SetActive(false);
        
        EventManager.Instance.AddListener(EventName.OnDayChanged, OnDayChanged);
    }

    private void OnDayChanged(object sender, EventArgs e)
    {
        if(_currentCropType == -1)
            return;
        _hasCrop = false;
        WateringGo.SetActive(false);
        FertilizationGo.SetActive(false);
        var curGrowth = growthValue;
        curGrowth += 1;
        growthValue = curGrowth;
    }

    public void OnClickFraming()
    {
        if (_currentCropType == -1)
        {
            _farmingOperateItem.ShowSeed();
        }
        else if(_currentCropType != -1 && !_hasCrop)
        {
            _farmingOperateItem.ShowCrop();
        }
    }

    public void DoReceive()
    {
        _currentCropType = -1;
        _farmingOperateItem.Hide();
        CropItemGo.SetActive(false);
        WateringGo.SetActive(false);
        FertilizationGo.SetActive(false);
    }

    public void DoSeed(int cropType)
    {
        _farmingOperateItem.Hide();
        CropItemGo.SetActive(true);
        _currentCropType = cropType;
        growthValue = 1;
    }

    public void DoWater()
    {
        _hasCrop = true;
        _farmingOperateItem.Hide();
        var curGrowth = growthValue;
        curGrowth += 1;
        growthValue = curGrowth;
        WateringGo.SetActive(true);
    }

    public void DoFertilize()
    {
        _hasCrop = true;
        _farmingOperateItem.Hide();
        var curGrowth = growthValue;
        curGrowth += 5;
        growthValue = curGrowth;
        FertilizationGo.SetActive(true);
    }

    private void OnGrowthValueChanged()
    {
        if (growthValue < 3)
            _cropGrowState = GrowStateEnum.Seed;
        else if (growthValue >= 4 && growthValue < 6)
            _cropGrowState = GrowStateEnum.Seedling;
        else if (growthValue >= 6)
            _cropGrowState = GrowStateEnum.Ripe;
        int cropGrowStateValue = (int)_cropGrowState;
        string spritePath = String.Format(CropSpritePathFromat, _currentCropType.ToString(), cropGrowStateValue.ToString());
        var sprite = Resources.Load<Sprite>(spritePath);
        var spriteRender = CropItemGo.GetComponent<SpriteRenderer>();
        spriteRender.sprite = sprite;
    }
}