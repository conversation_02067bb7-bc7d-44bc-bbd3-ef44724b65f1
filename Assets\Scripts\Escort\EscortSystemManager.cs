﻿using System.Collections.Generic;
using UnityCommunity.UnitySingleton;

namespace Escort
{
    enum EscortOrderStateEnum
    {
        Pending,
        Running,
        Complete
    }
    
    public class EscortOrderInfo
    {
        public EscortOrderInfo(int orderCfgID)
        {
            _orderCfgID = orderCfgID;
            _curState = EscortOrderStateEnum.Pending;
            _startRunningDay = 0;
        }

        public int GetTotalRequireDayCount()
        {
            return 5;
        }

        public int GetRoadCfgID()
        {
            return 1;
        }
        private int _orderCfgID;
        private EscortOrderStateEnum _curState;
        private int _startRunningDay;
    }
    public class EscortSystemManager : Singleton<EscortSystemManager>
    {
        private Dictionary<int, EscortOrderInfo> _escortOrderInfoDic = new Dictionary<int, EscortOrderInfo>();
        public void GetCityEscortDailyOrder(int cityID)
        {
            
        }
    }
}